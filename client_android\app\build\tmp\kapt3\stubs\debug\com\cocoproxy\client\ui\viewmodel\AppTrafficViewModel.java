package com.cocoproxy.client.ui.viewmodel;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010 \u001a\u00020!J\u0006\u0010\"\u001a\u00020!J\u0006\u0010#\u001a\u00020!J\u0010\u0010$\u001a\u0004\u0018\u00010\r2\u0006\u0010%\u001a\u00020\u0016J\f\u0010&\u001a\b\u0012\u0004\u0012\u00020\r0\fJ\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\r0\fJ\u0006\u0010(\u001a\u00020!R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0016\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0017\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001a0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0011R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001a0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0011R\u0017\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001a0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0011\u00a8\u0006)"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/AppTrafficViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "appManager", "Lcom/cocoproxy/client/core/AppManager;", "<init>", "(Landroid/app/Application;Lcom/cocoproxy/client/core/AppManager;)V", "trafficRepository", "Lcom/cocoproxy/client/data/repository/TrafficRepository;", "_trafficStats", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/cocoproxy/client/data/model/AppTrafficStats;", "trafficStats", "Lkotlinx/coroutines/flow/StateFlow;", "getTrafficStats", "()Lkotlinx/coroutines/flow/StateFlow;", "_isLoading", "", "isLoading", "_errorMessage", "", "errorMessage", "getErrorMessage", "totalUpload", "", "getTotalUpload", "totalDownload", "getTotalDownload", "totalBytes", "getTotalBytes", "loadTrafficStats", "", "refreshStats", "clearStats", "getAppTrafficStats", "packageName", "getProxiedAppsStats", "getDirectAppsStats", "clearErrorMessage", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AppTrafficViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.AppManager appManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.repository.TrafficRepository trafficRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.cocoproxy.client.data.model.AppTrafficStats>> _trafficStats = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.AppTrafficStats>> trafficStats = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> totalUpload = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> totalDownload = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> totalBytes = null;
    
    @javax.inject.Inject()
    public AppTrafficViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.AppManager appManager) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.AppTrafficStats>> getTrafficStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getTotalUpload() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getTotalDownload() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getTotalBytes() {
        return null;
    }
    
    /**
     * 加载流量统计数据
     */
    public final void loadTrafficStats() {
    }
    
    /**
     * 刷新统计数据
     */
    public final void refreshStats() {
    }
    
    /**
     * 清除统计数据
     */
    public final void clearStats() {
    }
    
    /**
     * 获取指定应用的流量统计
     */
    @org.jetbrains.annotations.Nullable()
    public final com.cocoproxy.client.data.model.AppTrafficStats getAppTrafficStats(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName) {
        return null;
    }
    
    /**
     * 获取代理应用的流量统计
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.AppTrafficStats> getProxiedAppsStats() {
        return null;
    }
    
    /**
     * 获取直连应用的流量统计
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.AppTrafficStats> getDirectAppsStats() {
        return null;
    }
    
    /**
     * 清除错误消息
     */
    public final void clearErrorMessage() {
    }
}