package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.data.model.ProxyConfig
import com.cocoproxy.client.ui.theme.*
import com.cocoproxy.client.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProxyConfigScreen(
    onNavigateBack: () -> Unit,
    viewModel: MainViewModel = hiltViewModel()
) {
    val proxyConfig by viewModel.proxyConfig.collectAsStateWithLifecycle()
    
    var localPort by remember(proxyConfig) { mutableStateOf(proxyConfig.localPort.toString()) }
    var proxyType by remember(proxyConfig) { mutableStateOf(proxyConfig.proxyType) }
    var enableAuth by remember(proxyConfig) { mutableStateOf(proxyConfig.enableAuth) }
    var authUsername by remember(proxyConfig) { mutableStateOf(proxyConfig.authUsername) }
    var authPassword by remember(proxyConfig) { mutableStateOf(proxyConfig.authPassword) }
    var enableDNS by remember(proxyConfig) { mutableStateOf(proxyConfig.enableDNS) }
    var dnsServer by remember(proxyConfig) { mutableStateOf(proxyConfig.dnsServer) }
    var enableUDP by remember(proxyConfig) { mutableStateOf(proxyConfig.enableUDP) }
    var bufferSize by remember(proxyConfig) { mutableStateOf(proxyConfig.bufferSize.toString()) }
    var maxConnections by remember(proxyConfig) { mutableStateOf(proxyConfig.maxConnections.toString()) }
    
    var showAdvanced by remember { mutableStateOf(false) }
    var showProxyTypeMenu by remember { mutableStateOf(false) }
    
    val proxyTypes = listOf("SOCKS5", "HTTP", "HTTPS")
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onNavigateBack) {
                Icon(
                    imageVector = Icons.Filled.ArrowBack,
                    contentDescription = "Back"
                )
            }
            
            Text(
                text = "Proxy Configuration",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.weight(1f)
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Basic Proxy Settings
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Basic Settings",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Local Port
                OutlinedTextField(
                    value = localPort,
                    onValueChange = { localPort = it },
                    label = { Text("Local Port") },
                    placeholder = { Text("1080") },
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Filled.Router,
                            contentDescription = null
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                )
                
                Spacer(modifier = Modifier.height(12.dp))
                
                // Proxy Type
                ExposedDropdownMenuBox(
                    expanded = showProxyTypeMenu,
                    onExpandedChange = { showProxyTypeMenu = it }
                ) {
                    OutlinedTextField(
                        value = proxyType,
                        onValueChange = {},
                        readOnly = true,
                        label = { Text("Proxy Type") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.SwapHoriz,
                                contentDescription = null
                            )
                        },
                        trailingIcon = {
                            ExposedDropdownMenuDefaults.TrailingIcon(
                                expanded = showProxyTypeMenu
                            )
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .menuAnchor()
                    )
                    
                    ExposedDropdownMenu(
                        expanded = showProxyTypeMenu,
                        onDismissRequest = { showProxyTypeMenu = false }
                    ) {
                        proxyTypes.forEach { type ->
                            DropdownMenuItem(
                                text = { Text(type) },
                                onClick = {
                                    proxyType = type
                                    showProxyTypeMenu = false
                                }
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Authentication Settings
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "Authentication",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Enable Authentication
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.Security,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Enable Authentication",
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Require username and password",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = enableAuth,
                        onCheckedChange = { enableAuth = it }
                    )
                }
                
                if (enableAuth) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Auth Username
                    OutlinedTextField(
                        value = authUsername,
                        onValueChange = { authUsername = it },
                        label = { Text("Username") },
                        placeholder = { Text("Enter username") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.Person,
                                contentDescription = null
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Auth Password
                    OutlinedTextField(
                        value = authPassword,
                        onValueChange = { authPassword = it },
                        label = { Text("Password") },
                        placeholder = { Text("Enter password") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.Lock,
                                contentDescription = null
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // DNS Settings
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "DNS Settings",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Enable DNS
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Filled.Dns,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Custom DNS",
                            style = MaterialTheme.typography.bodyLarge,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = "Use custom DNS server",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    Switch(
                        checked = enableDNS,
                        onCheckedChange = { enableDNS = it }
                    )
                }
                
                if (enableDNS) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // DNS Server
                    OutlinedTextField(
                        value = dnsServer,
                        onValueChange = { dnsServer = it },
                        label = { Text("DNS Server") },
                        placeholder = { Text("*******") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.Language,
                                contentDescription = null
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Advanced Settings
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Advanced Settings",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.weight(1f)
                    )
                    
                    IconButton(onClick = { showAdvanced = !showAdvanced }) {
                        Icon(
                            imageVector = if (showAdvanced) Icons.Filled.ExpandLess else Icons.Filled.ExpandMore,
                            contentDescription = if (showAdvanced) "Hide advanced" else "Show advanced"
                        )
                    }
                }
                
                if (showAdvanced) {
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Enable UDP
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Filled.NetworkCheck,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        
                        Spacer(modifier = Modifier.width(12.dp))
                        
                        Column(
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = "Enable UDP",
                                style = MaterialTheme.typography.bodyLarge,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "Support UDP traffic",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        
                        Switch(
                            checked = enableUDP,
                            onCheckedChange = { enableUDP = it }
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Buffer Size
                    OutlinedTextField(
                        value = bufferSize,
                        onValueChange = { bufferSize = it },
                        label = { Text("Buffer Size (KB)") },
                        placeholder = { Text("64") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.Memory,
                                contentDescription = null
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    // Max Connections
                    OutlinedTextField(
                        value = maxConnections,
                        onValueChange = { maxConnections = it },
                        label = { Text("Max Connections") },
                        placeholder = { Text("1000") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Filled.DeviceHub,
                                contentDescription = null
                            )
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Save Button
        Button(
            onClick = {
                val config = ProxyConfig(
                    localPort = localPort.toIntOrNull() ?: 1080,
                    proxyType = proxyType,
                    enableAuth = enableAuth,
                    authUsername = authUsername,
                    authPassword = authPassword,
                    enableDNS = enableDNS,
                    dnsServer = dnsServer,
                    enableUDP = enableUDP,
                    bufferSize = bufferSize.toIntOrNull() ?: 64,
                    maxConnections = maxConnections.toIntOrNull() ?: 1000
                )
                viewModel.saveProxyConfig(config)
                onNavigateBack()
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = localPort.isNotBlank()
        ) {
            Icon(
                imageVector = Icons.Filled.Save,
                contentDescription = null,
                modifier = Modifier.size(18.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("Save Configuration")
        }
        
        Spacer(modifier = Modifier.height(32.dp))
    }
}