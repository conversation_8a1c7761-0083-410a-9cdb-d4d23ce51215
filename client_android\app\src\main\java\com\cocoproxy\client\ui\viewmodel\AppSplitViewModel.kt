package com.cocoproxy.client.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.cocoproxy.client.core.AppManager
import com.cocoproxy.client.core.AppSplitConfig
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.data.model.InstalledApp
import com.cocoproxy.client.data.repository.ConfigRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

@HiltViewModel
class AppSplitViewModel @Inject constructor(
    application: Application,
    private val configRepository: ConfigRepository,
    private val appManager: AppManager
) : AndroidViewModel(application) {
    
    private val _splitConfig = MutableStateFlow(AppSplitConfig())
    val splitConfig: StateFlow<AppSplitConfig> = _splitConfig.asStateFlow()
    
    private val _allApps = MutableStateFlow<List<InstalledApp>>(emptyList())
    val allApps: StateFlow<List<InstalledApp>> = _allApps.asStateFlow()
    
    private val _selectedApps = MutableStateFlow<Set<String>>(emptySet())
    val selectedApps: StateFlow<Set<String>> = _selectedApps.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 派生状态
    val selectedAppsCount: StateFlow<Int> = _selectedApps.map { it.size }
        .stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0)
    
    val filteredApps: StateFlow<List<InstalledApp>> = combine(
        _allApps,
        _selectedApps
    ) { apps, selected ->
        apps.map { app ->
            app.copy(isSelected = selected.contains(app.packageName))
        }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), emptyList())
    
    init {
        loadConfiguration()
        loadInstalledApps()
    }
    
    /**
     * 加载当前配置
     */
    private fun loadConfiguration() {
        viewModelScope.launch {
            try {
                configRepository.appSplitConfig.collect { config ->
                    _splitConfig.value = config
                    _selectedApps.value = config.selectedApps
                }
            } catch (e: Exception) {
                _errorMessage.value = "加载配置失败: ${e.message}"
            }
        }
    }
    
    /**
     * 加载已安装应用
     */
    fun loadInstalledApps(includeSystemApps: Boolean = false) {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                val apps = withContext(Dispatchers.IO) {
                    appManager.getInstalledApps(includeSystemApps)
                }
                _allApps.value = apps
            } catch (e: Exception) {
                _errorMessage.value = "加载应用列表失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 设置分流模式
     */
    fun setSplitMode(mode: SplitMode) {
        viewModelScope.launch {
            try {
                val newConfig = _splitConfig.value.copy(
                    mode = mode,
                    enabled = mode != SplitMode.DISABLED
                )
                _splitConfig.value = newConfig
                saveConfiguration()
            } catch (e: Exception) {
                _errorMessage.value = "设置分流模式失败: ${e.message}"
            }
        }
    }
    
    /**
     * 启用分流模式
     */
    fun enableSplitMode() {
        viewModelScope.launch {
            try {
                val currentMode = _splitConfig.value.mode
                val newConfig = _splitConfig.value.copy(
                    enabled = true,
                    mode = if (currentMode == SplitMode.DISABLED) SplitMode.WHITELIST else currentMode
                )
                _splitConfig.value = newConfig
                saveConfiguration()
            } catch (e: Exception) {
                _errorMessage.value = "启用分流失败: ${e.message}"
            }
        }
    }
    
    /**
     * 禁用分流模式
     */
    fun disableSplitMode() {
        viewModelScope.launch {
            try {
                val newConfig = _splitConfig.value.copy(enabled = false)
                _splitConfig.value = newConfig
                saveConfiguration()
            } catch (e: Exception) {
                _errorMessage.value = "禁用分流失败: ${e.message}"
            }
        }
    }
    
    /**
     * 切换应用选择状态
     */
    fun toggleAppSelection(packageName: String) {
        val currentSelected = _selectedApps.value.toMutableSet()
        if (currentSelected.contains(packageName)) {
            currentSelected.remove(packageName)
        } else {
            currentSelected.add(packageName)
        }
        _selectedApps.value = currentSelected
    }
    
    /**
     * 选择所有应用
     */
    fun selectAllApps() {
        _selectedApps.value = _allApps.value.map { it.packageName }.toSet()
    }
    
    /**
     * 取消选择所有应用
     */
    fun deselectAllApps() {
        _selectedApps.value = emptySet()
    }
    
    /**
     * 根据类别选择应用
     */
    fun selectAppsByCategory(category: com.cocoproxy.client.data.model.AppCategory) {
        val categoryApps = _allApps.value
            .filter { it.category == category }
            .map { it.packageName }
        
        val currentSelected = _selectedApps.value.toMutableSet()
        currentSelected.addAll(categoryApps)
        _selectedApps.value = currentSelected
    }
    
    /**
     * 搜索应用
     */
    fun searchApps(query: String): List<InstalledApp> {
        if (query.isBlank()) return _allApps.value
        
        val lowerQuery = query.lowercase()
        return _allApps.value.filter { app ->
            app.appName.lowercase().contains(lowerQuery) ||
            app.packageName.lowercase().contains(lowerQuery)
        }
    }
    
    /**
     * 保存配置
     */
    fun saveConfiguration() {
        viewModelScope.launch {
            try {
                val config = _splitConfig.value.copy(
                    selectedApps = _selectedApps.value
                )
                configRepository.saveAppSplitConfig(config)
                _splitConfig.value = config
            } catch (e: Exception) {
                _errorMessage.value = "保存配置失败: ${e.message}"
            }
        }
    }
    
    /**
     * 重置配置
     */
    fun resetConfiguration() {
        viewModelScope.launch {
            try {
                val defaultConfig = AppSplitConfig()
                _splitConfig.value = defaultConfig
                _selectedApps.value = emptySet()
                configRepository.saveAppSplitConfig(defaultConfig)
            } catch (e: Exception) {
                _errorMessage.value = "重置配置失败: ${e.message}"
            }
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
    
    /**
     * 获取应用统计信息
     */
    fun getAppCategoryStats(): Map<com.cocoproxy.client.data.model.AppCategory, Int> {
        return _allApps.value.groupBy { it.category }
            .mapValues { it.value.size }
    }
    
    /**
     * 导入应用列表
     */
    fun importAppList(packageNames: List<String>) {
        val validPackages = packageNames.filter { packageName ->
            _allApps.value.any { it.packageName == packageName }
        }
        _selectedApps.value = validPackages.toSet()
    }
    
    /**
     * 导出应用列表
     */
    fun exportAppList(): List<String> {
        return _selectedApps.value.toList()
    }
}