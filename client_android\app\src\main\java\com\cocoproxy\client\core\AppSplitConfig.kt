package com.cocoproxy.client.core

import android.os.Parcelable
import androidx.compose.runtime.Stable
import kotlinx.parcelize.Parcelize

/**
 * Application split tunneling configuration
 */
@Parcelize
@Stable
data class AppSplitConfig(
    val enabled: Boolean = false,
    val mode: SplitMode = SplitMode.DISABLED,
    val selectedApps: Set<String> = emptySet() // Package names
) : Parcelable {
    
    // Legacy compatibility properties
    val splitMode: SplitMode get() = mode
    val selectedPackages: List<String> get() = selectedApps.toList()
}