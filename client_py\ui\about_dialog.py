#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QTabWidget, QWidget
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPixmap

class AboutDialog(QDialog):
    """About dialog for CocoProxy client"""
    
    def __init__(self, parent=None):
        """Initialize about dialog"""
        super().__init__(parent)
        
        # Dialog properties
        self.setWindowTitle("About CocoProxy")
        self.setModal(True)
        self.setFixedSize(450, 350)
        
        # Setup UI
        self.setup_ui()
    
    def setup_ui(self):
        """Setup user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Header layout
        header_layout = QHBoxLayout()
        
        # Logo (placeholder)
        logo_label = QLabel("🥥")  # Coconut emoji as placeholder
        logo_font = QFont()
        logo_font.setPointSize(48)
        logo_label.setFont(logo_font)
        logo_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(logo_label)
        
        # Title and version
        title_layout = QVBoxLayout()
        
        title_label = QLabel("CocoProxy Client")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)
        
        version_label = QLabel("Version 1.0.0")
        version_font = QFont()
        version_font.setPointSize(12)
        version_label.setFont(version_font)
        version_label.setStyleSheet("color: gray;")
        title_layout.addWidget(version_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Tab widget for different information
        tab_widget = QTabWidget()
        
        # About tab
        about_tab = QWidget()
        about_layout = QVBoxLayout(about_tab)
        
        about_text = QLabel("""
        <p><b>CocoProxy</b> is a secure and efficient proxy client that provides 
        encrypted network tunneling capabilities.</p>
        
        <p><b>Features:</b></p>
        <ul>
        <li>SOCKS5 and HTTP proxy support</li>
        <li>Multiple encryption algorithms (AES, ChaCha20)</li>
        <li>User authentication and traffic monitoring</li>
        <li>Cross-platform desktop client</li>
        <li>System tray integration</li>
        </ul>
        
        <p><b>Copyright:</b> © 2024 CocoProxy Team</p>
        """)
        about_text.setWordWrap(True)
        about_text.setAlignment(Qt.AlignTop)
        about_layout.addWidget(about_text)
        
        tab_widget.addTab(about_tab, "About")
        
        # License tab
        license_tab = QWidget()
        license_layout = QVBoxLayout(license_tab)
        
        license_text = QTextEdit()
        license_text.setReadOnly(True)
        license_text.setPlainText("""
MIT License

Copyright (c) 2024 CocoProxy Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
        """.strip())
        license_layout.addWidget(license_text)
        
        tab_widget.addTab(license_tab, "License")
        
        # System info tab
        system_tab = QWidget()
        system_layout = QVBoxLayout(system_tab)
        
        system_info = self.get_system_info()
        system_text = QLabel(system_info)
        system_text.setWordWrap(True)
        system_text.setAlignment(Qt.AlignTop)
        system_layout.addWidget(system_text)
        
        tab_widget.addTab(system_tab, "System")
        
        main_layout.addWidget(tab_widget)
        
        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # Close button
        close_button = QPushButton("Close")
        close_button.setDefault(True)
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)
        
        main_layout.addLayout(button_layout)
    
    def get_system_info(self):
        """Get system information"""
        import sys
        import platform
        from PySide6 import __version__ as pyside_version
        
        info = f"""
        <p><b>System Information:</b></p>
        <ul>
        <li><b>Operating System:</b> {platform.system()} {platform.release()}</li>
        <li><b>Architecture:</b> {platform.machine()}</li>
        <li><b>Python Version:</b> {sys.version.split()[0]}</li>
        <li><b>PySide6 Version:</b> {pyside_version}</li>
        <li><b>Platform:</b> {platform.platform()}</li>
        </ul>
        
        <p><b>Application Information:</b></p>
        <ul>
        <li><b>Build Date:</b> 2024-06-30</li>
        <li><b>Build Type:</b> Release</li>
        <li><b>Qt Version:</b> {pyside_version}</li>
        </ul>
        """
        
        return info