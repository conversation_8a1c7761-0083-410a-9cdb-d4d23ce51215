package com.cocoproxy.client.service

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.ParcelFileDescriptor
import android.system.OsConstants
import android.util.Log
import androidx.core.app.NotificationCompat
// import com.cocoproxy.client.ui.MainActivity
import com.cocoproxy.client.R
import com.cocoproxy.client.data.model.ProxyConfig
import com.cocoproxy.client.data.model.ServerConfig
import com.cocoproxy.client.core.AppSplitConfig
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.core.Socks5Client
import com.cocoproxy.client.core.ConnectionPool
import com.cocoproxy.client.core.AppManager
import com.cocoproxy.client.data.model.VpnConfig
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.net.InetAddress
import java.net.InetSocketAddress
import java.net.ServerSocket
import java.net.Socket
import java.nio.ByteBuffer
import java.nio.channels.DatagramChannel
import java.nio.channels.SocketChannel
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

@AndroidEntryPoint
class CocoProxyVpnService : VpnService() {
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "vpn_service_channel"
        private const val VPN_MTU = 1500
        private const val VPN_ADDRESS = "********"
        private const val VPN_ROUTE = "0.0.0.0"
        
        const val ACTION_START_VPN = "com.cocoproxy.client.START_VPN"
        const val ACTION_STOP_VPN = "com.cocoproxy.client.STOP_VPN"
    }
    
    private val binder = VpnServiceBinder()
    private var vpnInterface: ParcelFileDescriptor? = null
    private var serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var connectionPool: ConnectionPool? = null
    private var appManager: AppManager? = null
    private var appSplitConfig: AppSplitConfig? = null
    private var localProxyServerSocket: ServerSocket? = null
    
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()
    
    private val _bytesTransferred = MutableStateFlow(0L)
    val bytesTransferred: StateFlow<Long> = _bytesTransferred.asStateFlow()
    
    // 添加VPN状态流
    private val _vpnStatus = MutableStateFlow<VpnStatus>(VpnStatus.DISCONNECTED)
    val vpnStatus: StateFlow<VpnStatus> = _vpnStatus.asStateFlow()
    
    // 定义VPN状态枚举
    enum class VpnStatus {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        ERROR
    }
    
    private var vpnConfig: VpnConfig = VpnConfig()
    private var proxyHost = "127.0.0.1"
    private var proxyPort = 1080
    private var serverHost = "127.0.0.1" // 新增：服务器地址
    private var serverPort = 28888 // 新增：服务器端口
    private var localSocksPort = 1080 // 新增：本地SOCKS5代理端口
    private var serverUsername = "default" // 新增：服务器用户名
    
    // 连接池：为每个目标地址维护一个连接
    private val activeProxyConnections = ConcurrentHashMap<String, Socket>()
    
    // TCP连接状态管理（参考Shadowsocks实现）
    private val tcpConnections = ConcurrentHashMap<String, TcpConnectionState>()
    
    // TCP连接状态类
    data class TcpConnectionState(
        var localSeq: Long = 1000,  // 本地序列号
        var remoteSeq: Long = 0,    // 远程序列号
        var state: TcpState = TcpState.CLOSED,
        var lastActivity: Long = System.currentTimeMillis(),
        var synRetryCount: Int = 0  // SYN重试次数
    )
    
    enum class TcpState {
        CLOSED, SYN_SENT, SYN_RECEIVED, ESTABLISHED, FIN_WAIT, CLOSE_WAIT, LAST_ACK, TIME_WAIT
    }
    
    inner class VpnServiceBinder : Binder() {
        fun getService(): CocoProxyVpnService = this@CocoProxyVpnService
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_VPN -> {
                val proxyConfig = intent.getParcelableExtra<ProxyConfig>("proxy_config")
                val serverConfig = intent.getParcelableExtra<ServerConfig>("server_config")
                val vpnConfig = intent.getParcelableExtra<VpnConfig>("vpn_config")
                
                if (proxyConfig != null && serverConfig != null) {
                    if (vpnConfig != null) {
                        startVpn(proxyConfig, serverConfig, vpnConfig)
                    } else {
                        startVpn(proxyConfig, serverConfig)
                    }
                } else {
                    startVpn()
                }
            }
            ACTION_STOP_VPN -> {
                stopVpn()
            }
        }
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopVpn()
        serviceScope.cancel()
    }
    
    fun startVpn(proxyConfig: ProxyConfig? = null, serverConfig: ServerConfig? = null, config: VpnConfig = VpnConfig()) {
        if (_isRunning.value) {
            Log.d("CocoProxyVpnService", "VPN service already running, ignoring start request")
            return
        }
        
        // VPN permission should already be checked above, but double-check
        val intent = VpnService.prepare(this)
        if (intent != null) {
            Log.e("CocoProxyVpnService", "VPN permission check failed unexpectedly")
            _vpnStatus.value = VpnStatus.ERROR
            _isRunning.value = false
            stopForeground(STOP_FOREGROUND_REMOVE)
            stopSelf()
            return
        }
        
        _vpnStatus.value = VpnStatus.CONNECTING
        
        vpnConfig = config
        
        // Update proxy settings if provided
        if (proxyConfig != null && serverConfig != null) {
            // 修复：正确区分服务器地址/端口和本地代理端口
            updateProxySettings(serverConfig.host, serverConfig.port, proxyConfig.localPort, serverConfig.username)
        }
        
        // Check VPN permission first before starting foreground service
        val permissionIntent = VpnService.prepare(this)
        if (permissionIntent != null) {
            Log.w("CocoProxyVpnService", "VPN permission not granted, service will stop gracefully")
            _vpnStatus.value = VpnStatus.ERROR
            _isRunning.value = false
            stopSelf()
            return
        }
        
        // Start foreground service immediately to avoid crash
        startForeground(NOTIFICATION_ID, createNotification())
        
        try {
            Log.d("CocoProxyVpnService", "Starting VPN service...")
            Log.d("CocoProxyVpnService", "Proxy server: $proxyHost:$proxyPort")
            
            // Initialize connection pool and app manager
            connectionPool = ConnectionPool()
            appManager = AppManager(this)
            
            // Load app split configuration from current settings
            appSplitConfig = if (config.appSplitConfig.enabled) {
                config.appSplitConfig
            } else {
                com.cocoproxy.client.core.AppSplitConfig()
            }
            
            Log.d("CocoProxyVpnService", "App split config loaded: mode=${appSplitConfig?.mode}, enabled=${appSplitConfig?.enabled}, selectedApps=${appSplitConfig?.selectedApps?.size}")
            
            // Create VPN interface
            val builder = Builder()
                .setMtu(VPN_MTU)
                .addAddress(VPN_ADDRESS, 24)
                .setSession("CocoProxy VPN")
                .setConfigureIntent(
                    PendingIntent.getActivity(
                        this,
                        0,
                        packageManager.getLaunchIntentForPackage(packageName),
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                )
                .addRoute(VPN_ROUTE, 0) // 添加默认路由，使所有流量通过VPN
                .addDnsServer("*******") // 添加DNS服务器
                .addDnsServer("*******")

            // 尝试设置HTTP代理（可能不被所有应用支持）
            try {
                builder.setHttpProxy(android.net.ProxyInfo.buildDirectProxy("127.0.0.1", proxyPort))
                Log.d("CocoProxyVpnService", "VPN configured with HTTP proxy: 127.0.0.1:$proxyPort")
            } catch (e: Exception) {
                Log.w("CocoProxyVpnService", "Failed to set HTTP proxy, will rely on packet processing", e)
            }
            
            // IMPORTANT: Exclude proxy server from VPN to avoid routing loop
            if (serverConfig != null) {
                try {
                    // Resolve proxy server IP and exclude it from VPN
                    val proxyIp = java.net.InetAddress.getByName(serverConfig.host)
                    Log.d("CocoProxyVpnService", "Excluding proxy server from VPN: ${proxyIp.hostAddress}")
                    // Don't route proxy server traffic through VPN - let it go direct
                    // We do this by not adding any route for the proxy server IP
                    // The default system routing will handle it
                    Log.d("CocoProxyVpnService", "Proxy server ${proxyIp.hostAddress} will use direct routing")
                } catch (e: Exception) {
                    Log.w("CocoProxyVpnService", "Failed to resolve proxy server IP: ${serverConfig.host}", e)
                }
            }

            // Route all traffic through VPN (except proxy server)
            // Add routes for common blocked sites
//            builder.addRoute("0.0.0.0", 0)

            // Set up DNS to use our servers
//            builder.addDnsServer("*******")
//            builder.addDnsServer("*******")

            // Always exclude our own app to prevent routing loops
            try {
                builder.addDisallowedApplication(packageName)
                Log.d("CocoProxyVpnService", "Excluded own app from VPN: $packageName")
            } catch (e: Exception) {
                Log.w("CocoProxyVpnService", "Failed to exclude own app from VPN", e)
            }
            
            // 配置应用分流
            configureAppFiltering(builder, vpnConfig)
            
            // 建立VPN接口
            vpnInterface = builder.establish()
            if (vpnInterface == null) {
                Log.e("CocoProxyVpnService", "Failed to establish VPN interface")
                _vpnStatus.value = VpnStatus.ERROR
                throw Exception("Failed to establish VPN interface")
            }
            
            Log.d("CocoProxyVpnService", "VPN interface established successfully")
            
            // 启动数据包处理循环
            processPacketsWithProxy()
            
            // 通知状态变化
            _vpnStatus.value = VpnStatus.CONNECTED
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error starting VPN", e)
            _vpnStatus.value = VpnStatus.ERROR
            _isRunning.value = false
            stopForeground(STOP_FOREGROUND_REMOVE)
            throw e // Re-throw to trigger the service crash handling
        }
    }
    
    fun stopVpn() {
        if (!_isRunning.value) return
        
        _isRunning.value = false
        
        vpnInterface?.close()
        vpnInterface = null
        
        // Cleanup connection pool
        connectionPool?.shutdown()
        connectionPool = null
        appManager = null
        appSplitConfig = null
        
        // 关闭本地代理服务器
        localProxyServerSocket?.close()
        localProxyServerSocket = null
        
        serviceScope.coroutineContext.cancelChildren()
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    // 删除重复的configureAppSplit方法，使用configureAppFiltering代替
    
    /**
     * 配置应用分流
     */
    private fun configureAppFiltering(builder: Builder, vpnConfig: VpnConfig) {
        // 实现应用分流
        val splitConfig = vpnConfig.appSplitConfig
        if (!splitConfig.enabled) {
            // 没有应用分流，配置VPN配置中排除的应用
            if (vpnConfig.excludedApps.isNotEmpty()) {
                vpnConfig.excludedApps.forEach { packageName ->
                    try {
                        builder.addDisallowedApplication(packageName)
                        Log.d("CocoProxyVpnService", "Excluded app from VPN: $packageName")
                    } catch (e: Exception) {
                        Log.w("CocoProxyVpnService", "Failed to exclude app: $packageName", e)
                    }
                }
            }
            return
        }
        
        when (splitConfig.mode) {
            SplitMode.WHITELIST -> {
                // 只有选定的应用通过VPN/代理
                // 所有其他应用绕过VPN（直接连接）
                Log.d("CocoProxyVpnService", "Configuring WHITELIST mode with ${splitConfig.selectedApps.size} selected apps: ${splitConfig.selectedApps}")
                
                // 使用runBlocking确保在VPN建立之前完成
                runBlocking {
                    val allApps = appManager?.getInstalledApps(includeSystemApps = true) ?: emptyList()
                    Log.d("CocoProxyVpnService", "Found ${allApps.size} total apps")
                    
                    allApps.forEach { app ->
                        if (!splitConfig.selectedApps.contains(app.packageName)) {
                            try {
                                // 从VPN中排除非选定的应用，使其使用直接连接
                                builder.addDisallowedApplication(app.packageName)
                                Log.v("CocoProxyVpnService", "Excluded app from VPN: ${app.packageName}")
                            } catch (e: Exception) {
                                Log.w("CocoProxyVpnService", "Failed to exclude app: ${app.packageName}", e)
                            }
                        } else {
                            Log.d("CocoProxyVpnService", "App will use VPN/proxy: ${app.packageName}")
                        }
                    }
                }
            }
            SplitMode.BLACKLIST -> {
                // 选定的应用绕过VPN（直接连接）
                // 所有其他应用通过VPN/代理
                Log.d("CocoProxyVpnService", "Configuring BLACKLIST mode with ${splitConfig.selectedApps.size} excluded apps: ${splitConfig.selectedApps}")
                
                splitConfig.selectedApps.forEach { packageName ->
                    try {
                        builder.addDisallowedApplication(packageName)
                        Log.d("CocoProxyVpnService", "Excluded app from VPN: $packageName")
                    } catch (e: Exception) {
                        Log.w("CocoProxyVpnService", "Failed to exclude app: $packageName", e)
                    }
                }
            }
            SplitMode.DISABLED -> {
                // 所有应用通过代理（在上面处理）
            }
        }
    }
    
    // 删除重复的startVpn方法
    
    private fun updateProxySettings(host: String, port: Int, localPort: Int, username: String = "default") {
        serverHost = host
        serverPort = port
        serverUsername = username
        proxyHost = "127.0.0.1" // 本地代理始终使用127.0.0.1
        proxyPort = localPort
        localSocksPort = localPort // 设置本地SOCKS5代理端口
        Log.d("CocoProxyVpnService", "Updated proxy settings - Server: $serverHost:$serverPort, User: $serverUsername, Local proxy: $proxyHost:$proxyPort")
        
        // 启动本地SOCKS5代理服务器
        serviceScope.launch {
            startLocalSocksServer()
        }
    }
    
    private suspend fun startLocalSocksServer() = withContext(Dispatchers.IO) {
        try {
            // 创建并启动本地SOCKS5代理服务器
            val localServerSocket = ServerSocket(proxyPort)
            Log.d("CocoProxyVpnService", "Started local SOCKS5 proxy server on $proxyHost:$proxyPort")
            Log.d("CocoProxyVpnService", "Local SOCKS5 server listening on port: ${localServerSocket.localPort}")
            
            // 保存服务器套接字以便后续关闭
            localProxyServerSocket = localServerSocket
            
            while (_isRunning.value && isActive) {
                try {
                    val clientSocket = localServerSocket.accept()
                    Log.d("CocoProxyVpnService", "New SOCKS5 client connected: ${clientSocket.remoteSocketAddress}")
                    
                    // 在单独的协程中处理客户端连接
                    serviceScope.launch {
                        handleSocks5ClientConnection(clientSocket)
                    }
                } catch (e: IOException) {
                    if (_isRunning.value) {
                        Log.e("CocoProxyVpnService", "Error accepting SOCKS5 connection", e)
                    }
                    break
                }
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Failed to start local SOCKS5 server", e)
        }
    }
    
    private suspend fun handleSocks5ClientConnection(clientSocket: Socket) = withContext(Dispatchers.IO) {
        try {
            val input = clientSocket.getInputStream()
            val output = clientSocket.getOutputStream()
            
            // SOCKS5握手 - 读取版本和方法数量
            val version = input.read()
            if (version != 0x05) {
                Log.e("CocoProxyVpnService", "Invalid SOCKS version: $version")
                throw IOException("Invalid SOCKS version: $version")
            }

            val methodCount = input.read()
            if (methodCount <= 0) {
                Log.e("CocoProxyVpnService", "Invalid method count: $methodCount")
                throw IOException("Invalid method count: $methodCount")
            }

            val methods = ByteArray(methodCount)
            input.read(methods)

            Log.d("CocoProxyVpnService", "SOCKS5 handshake: version=$version, methodCount=$methodCount")
            
            // 发送方法选择响应（无认证）
            output.write(byteArrayOf(0x05, 0x00))
            output.flush()
            Log.d("CocoProxyVpnService", "Sent SOCKS5 method selection response")
            
            // 处理连接请求
            val request = ByteArray(4)
            val requestBytesRead = input.read(request)

            if (requestBytesRead != 4 || request[0] != 0x05.toByte()) {
                Log.e("CocoProxyVpnService", "Invalid SOCKS request: ${request.joinToString { "%02x".format(it) }}")
                throw IOException("Invalid SOCKS request")
            }

            Log.d("CocoProxyVpnService", "SOCKS5 connect request: ${request.joinToString { "%02x".format(it) }}")
            
            val addressType = request[3].toInt() and 0xFF
            val targetHost: String
            val targetPort: Int
            
            when (addressType) {
                0x01 -> { // IPv4
                    val address = ByteArray(4)
                    input.read(address)
                    targetHost = address.joinToString(".") { (it.toInt() and 0xFF).toString() }
                }
                0x03 -> { // 域名
                    val domainLength = input.read()
                    val domain = ByteArray(domainLength)
                    input.read(domain)
                    targetHost = String(domain)
                }
                else -> {
                    throw IOException("Unsupported address type: $addressType")
                }
            }
            
            val portBytes = ByteArray(2)
            input.read(portBytes)
            targetPort = ((portBytes[0].toInt() and 0xFF) shl 8) or (portBytes[1].toInt() and 0xFF)
            
            Log.d("CocoProxyVpnService", "SOCKS5 request: $targetHost:$targetPort")
            
            // 连接到远程服务器
            val remoteSocket = connectToCocoProxyServerForSocks5(targetHost, targetPort)
            if (remoteSocket == null) {
                // 发送失败响应
                output.write(byteArrayOf(
                    0x05, 0x01, 0x00, 0x01, // SOCKS5, 失败, 保留, IPv4
                    0x00, 0x00, 0x00, 0x00, // 绑定地址 (0.0.0.0)
                    0x00, 0x00 // 绑定端口 (0)
                ))
                return@withContext
            }
            
            // 发送成功响应
            output.write(byteArrayOf(
                0x05, 0x00, 0x00, 0x01, // SOCKS5, 成功, 保留, IPv4
                0x00, 0x00, 0x00, 0x00, // 绑定地址 (0.0.0.0)
                0x00, 0x00 // 绑定端口 (0)
            ))
            
            // 双向转发数据
            val job1 = serviceScope.launch {
                try {
                    val buffer = ByteArray(8192)
                    var bytesRead = 0
                    while (isActive && (input.read(buffer).also { bytesRead = it }) != -1) {
                        remoteSocket.getOutputStream().write(buffer, 0, bytesRead)
                        remoteSocket.getOutputStream().flush()
                    }
                } catch (e: Exception) {
                    // 忽略连接关闭异常
                }
            }
            
            val job2 = serviceScope.launch {
                try {
                    val buffer = ByteArray(8192)
                    var bytesRead = 0
                    while (isActive && (remoteSocket.getInputStream().read(buffer).also { bytesRead = it }) != -1) {
                        output.write(buffer, 0, bytesRead)
                        output.flush()
                    }
                } catch (e: Exception) {
                    // 忽略连接关闭异常
                }
            }
            
            // 等待任一方向完成
            job1.join()
            job2.join()
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error handling SOCKS5 client", e)
        } finally {
            try {
                clientSocket.close()
            } catch (e: Exception) {}
        }
    }
    

    
    /**
     * Check if packet should use proxy based on app split configuration
     */
    private fun shouldUseProxyForPacket(packet: ByteBuffer): Boolean {
        val splitConfig = appSplitConfig ?: return true
        
        if (splitConfig.splitMode == SplitMode.DISABLED) {
            return true
        }
        
        // For more accurate app identification, we would need to track
        // socket connections and map them to UIDs/package names
        // This is a simplified implementation
        
        return true // Default to using proxy
    }

    /**
     * 处理UDP数据包
     */
    @SuppressLint("DefaultLocale")
    private fun processUdpPacket(packet: ByteBuffer, vpnOutput: FileOutputStream) {
        try {
            // 提取源IP和目标IP
            val sourceIp = String.format(
                "%d.%d.%d.%d",
                packet.get(12).toInt() and 0xFF,
                packet.get(13).toInt() and 0xFF,
                packet.get(14).toInt() and 0xFF,
                packet.get(15).toInt() and 0xFF
            )
            
            val destIp = String.format(
                "%d.%d.%d.%d",
                packet.get(16).toInt() and 0xFF,
                packet.get(17).toInt() and 0xFF,
                packet.get(18).toInt() and 0xFF,
                packet.get(19).toInt() and 0xFF
            )
            
            // 提取源端口和目标端口
            val sourcePort = ((packet.get(20).toInt() and 0xFF) shl 8) or 
                          (packet.get(21).toInt() and 0xFF)
            val destPort = ((packet.get(22).toInt() and 0xFF) shl 8) or 
                          (packet.get(23).toInt() and 0xFF)
            
            // 检查是否应该使用代理
            val shouldUseProxy = shouldUseProxyForPacket(packet)
            
            // 添加断点位置 - 这里可以设置断点
            Log.d("CocoProxyVpnService", "UDP Packet details: $sourceIp:$sourcePort -> $destIp:$destPort, useProxy: $shouldUseProxy")
            
            // 检查是否是Chrome的请求
            val uid = getAppUidByPort(sourcePort)
            val packageName = getPackageNameByUid(uid)
            Log.d("CocoProxyVpnService", "UDP packet from app: $packageName (UID: $uid)")
            
            if (packageName == "com.android.chrome") {
                Log.d("CocoProxyVpnService", "Chrome UDP packet detected: $sourceIp:$sourcePort -> $destIp:$destPort")
                // 添加断点位置 - 这里可以设置断点
            }
            
            // 检查是否是DNS查询
            if (destPort == 53) {
                Log.d("CocoProxyVpnService", "DNS query detected: $sourceIp:$sourcePort -> $destIp:$destPort")
                Log.d("CocoProxyVpnService", "DNS shouldUseProxy: $shouldUseProxy")

                if (shouldUseProxy) {
                    // 通过代理转发DNS查询
                    Log.d("CocoProxyVpnService", "Handling DNS query through proxy: $sourceIp:$sourcePort -> $destIp:$destPort")
                    handleDnsQuery(packet, sourceIp, sourcePort, destIp, destPort, vpnOutput)
                } else {
                    // 直接转发DNS查询
                    Log.d("CocoProxyVpnService", "Directly forwarding DNS query (no proxy): $sourceIp:$sourcePort -> $destIp:$destPort")
                    val length = packet.limit()
                    vpnOutput.write(packet.array(), 0, length)
                    vpnOutput.flush()
                }
            } else {
                // 其他UDP包直接转发
                val length = packet.limit()
                vpnOutput.write(packet.array(), 0, length)
                vpnOutput.flush()
            }
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error processing UDP packet", e)
        }
    }

    /**
     * 处理DNS查询 - 改进版本
     */
    private fun handleDnsQuery(
        packet: ByteBuffer,
        sourceIp: String,
        sourcePort: Int,
        destIp: String,
        destPort: Int,
        vpnOutput: FileOutputStream
    ) {
        Log.d("CocoProxyVpnService", "handleDnsQuery method called")

        try {
            serviceScope.launch {
                Log.d("CocoProxyVpnService", "DNS query coroutine started")
                try {
                    Log.d("CocoProxyVpnService", "Processing DNS query: $sourceIp:$sourcePort -> $destIp:$destPort")

                // 提取DNS查询数据 - 改进版本
                val ipHeaderLength = (packet.get(0).toInt() and 0x0F) * 4
                val udpHeaderLength = 8
                val dnsDataOffset = ipHeaderLength + udpHeaderLength
                val totalPacketLength = packet.limit()
                val dnsDataLength = totalPacketLength - dnsDataOffset

                Log.d("CocoProxyVpnService", "DNS packet analysis: total=${totalPacketLength}, ipHeader=${ipHeaderLength}, udpHeader=${udpHeaderLength}, dnsOffset=${dnsDataOffset}, dnsLength=${dnsDataLength}")

                // 验证数据长度
                if (dnsDataLength <= 0) {
                    Log.w("CocoProxyVpnService", "Invalid DNS query length: $dnsDataLength")
                    return@launch
                }

                // 验证DNS数据大小是否合理 (DNS查询通常小于512字节)
                if (dnsDataLength > 512) {
                    Log.w("CocoProxyVpnService", "DNS query too large: ${dnsDataLength} bytes, falling back to direct forwarding")
                    // 对于异常大的DNS查询，直接转发原始包
                    try {
                        val length = packet.limit()
                        packet.position(0)
                        vpnOutput.write(packet.array(), 0, length)
                        vpnOutput.flush()
                        Log.d("CocoProxyVpnService", "Large DNS query forwarded directly")
                    } catch (e: Exception) {
                        Log.e("CocoProxyVpnService", "Failed to forward large DNS query", e)
                    }
                    return@launch
                }

                // 提取DNS数据
                val dnsData = ByteArray(dnsDataLength)
                packet.position(dnsDataOffset)
                packet.get(dnsData)

                // 调试：检查DNS数据
                Log.d("CocoProxyVpnService", "DNS data extracted: ${dnsData.size} bytes")
                if (dnsData.size >= 16) {
                    Log.d("CocoProxyVpnService", "DNS data first 16 bytes: ${dnsData.take(16).joinToString(" ") { "%02x".format(it) }}")
                }

                // 通过CocoProxy代理服务器转发DNS查询（使用SOCKS5协议）
                Log.d("CocoProxyVpnService", "Attempting to connect to CocoProxy server for DNS via SOCKS5")
                val proxySocket = connectToCocoProxyServerForSocks5(destIp, destPort)
                if (proxySocket == null) {
                    Log.w("CocoProxyVpnService", "Failed to connect to CocoProxy server for DNS via SOCKS5")
                    throw Exception("Failed to connect to CocoProxy server for DNS via SOCKS5")
                }

                // 设置socket超时
                try {
                    proxySocket.soTimeout = 10000 // 10秒超时
                    Log.d("CocoProxyVpnService", "Successfully connected to CocoProxy server for DNS via SOCKS5 with 10s timeout")
                } catch (e: Exception) {
                    Log.w("CocoProxyVpnService", "Failed to set socket timeout", e)
                }

                // 发送DNS查询数据（通过已建立的SOCKS5隧道）
                // DNS over TCP格式：2字节长度前缀 + DNS数据
                Log.d("CocoProxyVpnService", "Sending DNS query data: ${dnsData.size} bytes (TCP format)")

                // 构建TCP格式的DNS查询
                val tcpDnsQuery = ByteArrayOutputStream()
                // 写入长度前缀（2字节，大端序）
                tcpDnsQuery.write((dnsData.size shr 8) and 0xFF)
                tcpDnsQuery.write(dnsData.size and 0xFF)
                // 写入DNS数据
                tcpDnsQuery.write(dnsData)

                val tcpDnsData = tcpDnsQuery.toByteArray()
                Log.d("CocoProxyVpnService", "TCP DNS query: ${tcpDnsData.size} bytes (${dnsData.size} + 2 length prefix)")

                proxySocket.getOutputStream().write(tcpDnsData)
                proxySocket.getOutputStream().flush()
                Log.d("CocoProxyVpnService", "TCP DNS query data sent via SOCKS5, waiting for response...")

                // 接收DNS响应（TCP格式：2字节长度前缀 + DNS数据）
                val inputStream = proxySocket.getInputStream()
                val startTime = System.currentTimeMillis()

                // 首先读取2字节长度前缀
                val lengthBuffer = ByteArray(2)
                var lengthBytesRead = 0
                while (lengthBytesRead < 2) {
                    val bytesRead = inputStream.read(lengthBuffer, lengthBytesRead, 2 - lengthBytesRead)
                    if (bytesRead == -1) {
                        throw Exception("Connection closed while reading length prefix")
                    }
                    lengthBytesRead += bytesRead
                }

                // 解析长度
                val responseDataLength = ((lengthBuffer[0].toInt() and 0xFF) shl 8) or (lengthBuffer[1].toInt() and 0xFF)
                Log.d("CocoProxyVpnService", "TCP DNS response length: $responseDataLength bytes")

                if (responseDataLength <= 0 || responseDataLength > 512) {
                    throw Exception("Invalid DNS response length: $responseDataLength")
                }

                // 读取DNS响应数据
                val responseBuffer = ByteArray(responseDataLength)
                var responseBytesRead = 0
                while (responseBytesRead < responseDataLength) {
                    val bytesRead = inputStream.read(responseBuffer, responseBytesRead, responseDataLength - responseBytesRead)
                    if (bytesRead == -1) {
                        throw Exception("Connection closed while reading DNS response data")
                    }
                    responseBytesRead += bytesRead
                }

                val duration = System.currentTimeMillis() - startTime
                Log.d("CocoProxyVpnService", "TCP DNS response received in ${duration}ms, data length: $responseDataLength bytes")

                val responseLength = responseDataLength

                // 确保关闭socket
                try {
                    proxySocket.close()
                } catch (e: Exception) {
                    Log.w("CocoProxyVpnService", "Error closing proxy socket", e)
                }

                if (responseLength > 0) {
                    Log.d("CocoProxyVpnService", "Received DNS response: $responseLength bytes")

                    // 验证响应数据
                    if (responseLength > 512) {
                        Log.w("CocoProxyVpnService", "DNS response too large: $responseLength bytes")
                        throw Exception("DNS response too large")
                    }

                    // 构建UDP响应包
                    val responsePacket = buildUdpResponsePacket(
                        destIp, destPort, sourceIp, sourcePort,
                        responseBuffer, responseLength
                    )

                    if (responsePacket != null) {
                        // 发送响应给客户端
                        vpnOutput.write(responsePacket)
                        vpnOutput.flush()
                        Log.d("CocoProxyVpnService", "Successfully sent DNS response to client: ${responsePacket.size} bytes")
                    } else {
                        Log.e("CocoProxyVpnService", "Failed to build DNS response packet")
                        throw Exception("Failed to build DNS response packet")
                    }
                } else {
                    Log.w("CocoProxyVpnService", "No DNS response received (length: $responseLength)")
                    throw Exception("Empty DNS response")
                }

                } catch (e: Exception) {
                    Log.e("CocoProxyVpnService", "Error in DNS query coroutine: ${e.message}", e)

                    // 如果代理失败，尝试直接转发原始包
                    try {
                        val length = packet.limit()
                        packet.position(0)
                        vpnOutput.write(packet.array(), 0, length)
                        vpnOutput.flush()
                        Log.d("CocoProxyVpnService", "Fallback: forwarded original DNS packet")
                    } catch (fallbackError: Exception) {
                        Log.e("CocoProxyVpnService", "Fallback DNS forwarding failed", fallbackError)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error launching DNS query coroutine: ${e.message}", e)

            // 如果协程启动失败，直接转发原始包
            try {
                val length = packet.limit()
                packet.position(0)
                vpnOutput.write(packet.array(), 0, length)
                vpnOutput.flush()
                Log.d("CocoProxyVpnService", "Fallback: forwarded original DNS packet (coroutine launch failed)")
            } catch (fallbackError: Exception) {
                Log.e("CocoProxyVpnService", "Fallback DNS forwarding failed (coroutine launch failed)", fallbackError)
            }
        }
    }

    /**
     * 构建UDP响应包
     */
    private fun buildUdpResponsePacket(
        sourceIp: String, sourcePort: Int,
        destIp: String, destPort: Int,
        data: ByteArray, dataLength: Int
    ): ByteArray {
        val totalLength = 20 + 8 + dataLength // IP头 + UDP头 + 数据
        val packet = ByteArray(totalLength)
        var offset = 0

        // IP头部 (20字节)
        packet[offset++] = 0x45.toByte() // 版本(4) + 头长度(5*4=20)
        packet[offset++] = 0x00.toByte() // 服务类型
        packet[offset++] = (totalLength shr 8).toByte() // 总长度高字节
        packet[offset++] = (totalLength and 0xFF).toByte() // 总长度低字节
        packet[offset++] = 0x00.toByte() // 标识高字节
        packet[offset++] = 0x01.toByte() // 标识低字节
        packet[offset++] = 0x40.toByte() // 标志(DF=1) + 片偏移高3位
        packet[offset++] = 0x00.toByte() // 片偏移低8位
        packet[offset++] = 0x40.toByte() // TTL = 64
        packet[offset++] = 0x11.toByte() // 协议 = UDP
        packet[offset++] = 0x00.toByte() // 校验和高字节（稍后计算）
        packet[offset++] = 0x00.toByte() // 校验和低字节

        // 源IP地址
        val sourceIpBytes = sourceIp.split(".").map { it.toInt().toByte() }.toByteArray()
        System.arraycopy(sourceIpBytes, 0, packet, offset, 4)
        offset += 4

        // 目标IP地址
        val destIpBytes = destIp.split(".").map { it.toInt().toByte() }.toByteArray()
        System.arraycopy(destIpBytes, 0, packet, offset, 4)
        offset += 4

        // UDP头部 (8字节)
        packet[offset++] = (sourcePort shr 8).toByte() // 源端口高字节
        packet[offset++] = (sourcePort and 0xFF).toByte() // 源端口低字节
        packet[offset++] = (destPort shr 8).toByte() // 目标端口高字节
        packet[offset++] = (destPort and 0xFF).toByte() // 目标端口低字节

        val udpLength = 8 + dataLength
        packet[offset++] = (udpLength shr 8).toByte() // UDP长度高字节
        packet[offset++] = (udpLength and 0xFF).toByte() // UDP长度低字节
        packet[offset++] = 0x00.toByte() // UDP校验和高字节（暂时为0）
        packet[offset++] = 0x00.toByte() // UDP校验和低字节

        // 数据部分
        System.arraycopy(data, 0, packet, offset, dataLength)

        // 计算IP头校验和
        calculateIpChecksum(packet, 0, 20)

        // 计算UDP校验和（可选，设为0表示不使用）
        // calculateUdpChecksum(packet, sourceIpBytes, destIpBytes, udpLength)

        return packet
    }
    
    /**
     * 使用代理的简化数据包处理方法
     */
    @SuppressLint("DefaultLocale")
    private fun processPacketsWithProxy() {
        _isRunning.value = true

        // 启动连接清理任务
        serviceScope.launch {
            startConnectionCleanupTask()
        }

        serviceScope.launch {
            try {
                val vpnInput = FileInputStream(vpnInterface?.fileDescriptor)
                val vpnOutput = FileOutputStream(vpnInterface?.fileDescriptor)

                val buffer = ByteBuffer.allocate(VPN_MTU)

                Log.d("CocoProxyVpnService", "Starting simplified packet processing with proxy redirection")

                while (_isRunning.value && isActive) {
                    // 清空缓冲区
                    buffer.clear()

                    // 读取VPN接口数据
                    val length = vpnInput.read(buffer.array())
                    if (length <= 0) {
                        Thread.sleep(10)
                        continue
                    }

                    // 设置缓冲区限制
                    buffer.limit(length)

                    // 更新流量统计
                    _bytesTransferred.value += length

                    // 检查数据包类型
                    val ipVersion = buffer.get(0).toInt() and 0xF0

                    // 只处理IPv4数据包
                    if (ipVersion != 0x40) {
                        // 直接转发非IPv4包
                        vpnOutput.write(buffer.array(), 0, length)
                        vpnOutput.flush()
                        continue
                    }

                    // 提取协议类型
                    val protocol = buffer.get(9).toInt() and 0xFF

                    // 对于需要代理的TCP连接，我们简单地丢弃SYN包
                    // 这会强制应用重新连接，希望能通过HTTP代理
                    if (protocol == 6) { // TCP
                        // 提取目标IP和端口
                        val destIp = String.format(
                            "%d.%d.%d.%d",
                            buffer.get(16).toInt() and 0xFF,
                            buffer.get(17).toInt() and 0xFF,
                            buffer.get(18).toInt() and 0xFF,
                            buffer.get(19).toInt() and 0xFF
                        )

                        // 检查是否应该使用代理
                        val shouldUseProxy = shouldUseProxyForPacket(buffer)

                        if (shouldUseProxy) {
                            // 丢弃需要代理的TCP包，强制应用使用HTTP代理
                            Log.v("CocoProxyVpnService", "Dropping TCP packet to force proxy: $destIp")
                            continue
                        }
                    }

                    // 直接转发不需要代理的包
                    vpnOutput.write(buffer.array(), 0, length)
                    vpnOutput.flush()
                }
            } catch (e: Exception) {
                Log.e("CocoProxyVpnService", "Error in simplified packet processing", e)
                _vpnStatus.value = VpnStatus.ERROR
            } finally {
                _isRunning.value = false
            }
        }
    }

    /**
     * 原始的复杂数据包处理方法（保留作为备用）
     */
    @SuppressLint("DefaultLocale")
    private fun processPacketsSimple() {
        _isRunning.value = true
        
        serviceScope.launch {
            try {
                val vpnInput = FileInputStream(vpnInterface?.fileDescriptor)
                val vpnOutput = FileOutputStream(vpnInterface?.fileDescriptor)
                
                val buffer = ByteBuffer.allocate(VPN_MTU)
                
                Log.d("CocoProxyVpnService", "Starting packet processing loop")
                
                while (_isRunning.value && isActive) {
                    // 清空缓冲区
                    buffer.clear()
                    
                    // 读取VPN接口数据
                    val length = vpnInput.read(buffer.array())
                    if (length <= 0) {
                        Thread.sleep(100)
                        continue
                    }
                    
                    // 设置缓冲区限制
                    buffer.limit(length)
                    
                    // 更新流量统计
                    _bytesTransferred.value += length
                    
                    // 检查数据包类型
                    val ipVersion = buffer.get(0).toInt() and 0xF0
                    
                    // 只处理IPv4数据包
                    if (ipVersion != 0x40) {
                        Log.v("CocoProxyVpnService", "Non-IPv4 packet received, version: ${ipVersion shr 4}")
                        vpnOutput.write(buffer.array(), 0, length)
                        vpnOutput.flush()
                        continue
                    }
                    
                    // 提取协议类型
                    val protocol = buffer.get(9).toInt() and 0xFF
                    
                    // 提取源IP和目标IP
                    val sourceIp = String.format(
                        "%d.%d.%d.%d",
                        buffer.get(12).toInt() and 0xFF,
                        buffer.get(13).toInt() and 0xFF,
                        buffer.get(14).toInt() and 0xFF,
                        buffer.get(15).toInt() and 0xFF
                    )
                    
                    val destIp = String.format(
                        "%d.%d.%d.%d",
                        buffer.get(16).toInt() and 0xFF,
                        buffer.get(17).toInt() and 0xFF,
                        buffer.get(18).toInt() and 0xFF,
                        buffer.get(19).toInt() and 0xFF
                    )
                    
                    // 根据协议类型处理数据包
                    when (protocol) {
                        6 -> { // TCP
                            // 提取源端口和目标端口
                            val sourcePort = ((buffer.get(20).toInt() and 0xFF) shl 8) or 
                                          (buffer.get(21).toInt() and 0xFF)
                            val destPort = ((buffer.get(22).toInt() and 0xFF) shl 8) or 
                                          (buffer.get(23).toInt() and 0xFF)
                            
                            // 添加断点位置 - 这里可以设置断点
                            Log.d("CocoProxyVpnService", "TCP Packet: $sourceIp:$sourcePort -> $destIp:$destPort")
                            
                            // 检查是否应该使用代理
                            val shouldUseProxy = shouldUseProxyForPacket(buffer)
                            
                            // 添加断点位置 - 这里可以设置断点
                            if (shouldUseProxy) {
                                Log.d("CocoProxyVpnService", "TCP packet should use proxy: $destIp:$destPort")
                                
                                // 检查是否是Chrome的请求
                                val uid = getAppUidByPort(sourcePort)
                                val packageName = getPackageNameByUid(uid)
                                Log.d("CocoProxyVpnService", "TCP packet from app: $packageName (UID: $uid)")
                                
                                if (packageName == "com.android.chrome") {
                                    Log.d("CocoProxyVpnService", "Chrome TCP packet detected: $sourceIp:$sourcePort -> $destIp:$destPort")
                                    // 添加断点位置 - 这里可以设置断点
                                }
                                
                                try {
                                    // 处理TCP包（参考Shadowsocks实现）
                                    handleTcpPacket(buffer, length, sourceIp, sourcePort, destIp, destPort, vpnOutput)
                                } catch (e: Exception) {
                                    Log.e("CocoProxyVpnService", "Error handling TCP packet", e)
                                    // 发生错误时，直接写回VPN接口
                                    vpnOutput.write(buffer.array(), 0, length)
                                    vpnOutput.flush()
                                }
                            } else {
                                Log.d("CocoProxyVpnService", "TCP packet direct: $destIp:$destPort")
                                // 写回VPN接口
                                vpnOutput.write(buffer.array(), 0, length)
                                vpnOutput.flush()
                            }
                        }
                        17 -> { // UDP
                            // 提取源端口和目标端口
                            val sourcePort = ((buffer.get(20).toInt() and 0xFF) shl 8) or 
                                          (buffer.get(21).toInt() and 0xFF)
                            val destPort = ((buffer.get(22).toInt() and 0xFF) shl 8) or 
                                          (buffer.get(23).toInt() and 0xFF)
                            
                            Log.d("CocoProxyVpnService", "UDP Packet: $sourceIp:$sourcePort -> $destIp:$destPort")
                            
                            // 处理UDP数据包
                            processUdpPacket(buffer, vpnOutput)
                        }
                        else -> {
                            // 其他类型的数据包直接写回
                            Log.v("CocoProxyVpnService", "Other packet protocol: $protocol")
                            vpnOutput.write(buffer.array(), 0, length)
                            vpnOutput.flush()
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e("CocoProxyVpnService", "Error in packet processing", e)
                _vpnStatus.value = VpnStatus.ERROR
            } finally {
                _isRunning.value = false
            }
        }
    }

    /**
     * 根据端口获取应用UID
     * 注意：这是一个简化实现，实际上需要更复杂的连接跟踪
     */
    private fun getAppUidByPort(port: Int): Int {
        try {
            // 读取/proc/net/tcp文件获取TCP连接信息
            val file = File("/proc/net/tcp")
            if (!file.exists()) {
                Log.w("CocoProxyVpnService", "/proc/net/tcp file does not exist")
                return -1
            }
            
            if (!file.canRead()) {
                Log.w("CocoProxyVpnService", "Cannot read /proc/net/tcp file (permission denied)")
                return -1
            }
            
            try {
                file.bufferedReader().use { reader ->
                    // 跳过标题行
                    reader.readLine()
                    
                    // 读取每一行连接信息
                    reader.lineSequence().forEach { line ->
                        Log.d("CocoProxyVpnService", "TCP line: $line")
                        // 使用正则表达式更精确地分割行
                        val parts = line.trim().split("\\s+".toRegex())
                        Log.d("CocoProxyVpnService", "Split parts: ${parts.joinToString(", ")}")
                        
                        if (parts.size >= 8) {
                            try {
                                // 解析本地端口（十六进制）
                                val localAddrPort = parts[1].split(":")[1]
                                val localPort = Integer.parseInt(localAddrPort, 16)
                                
                                Log.d("CocoProxyVpnService", "Parsed port: $localPort (hex: $localAddrPort) vs requested: $port")
                                
                                if (localPort == port) {
                                    // 找到匹配的端口，返回UID
                                    val uid = parts[7].toInt()
                                    Log.d("CocoProxyVpnService", "Found matching port $port with UID: $uid")
                                    return uid
                                }
                            } catch (e: Exception) {
                                Log.w("CocoProxyVpnService", "Error parsing TCP line: ${e.message}")
                            }
                        }
                    }
                }
            } catch (e: SecurityException) {
                Log.w("CocoProxyVpnService", "Security exception reading /proc/net/tcp: ${e.message}")
                return -1
            } catch (e: IOException) {
                Log.w("CocoProxyVpnService", "IO exception reading /proc/net/tcp: ${e.message}")
                return -1
            }
        } catch (e: Exception) {
            // 捕获所有其他异常，记录日志但不中断程序
            Log.w("CocoProxyVpnService", "Error getting app UID by port: ${e.javaClass.simpleName}: ${e.message}")
        }
        
        // 如果找不到匹配的端口，尝试使用UDP连接
        try {
            val udpFile = File("/proc/net/udp")
            if (udpFile.exists() && udpFile.canRead()) {
                udpFile.bufferedReader().use { reader ->
                    // 跳过标题行
                    reader.readLine()
                    
                    // 读取每一行连接信息
                    reader.lineSequence().forEach { line ->
                        Log.d("CocoProxyVpnService", "UDP line: $line")
                        // 使用正则表达式更精确地分割行
                        val parts = line.trim().split("\\s+".toRegex())
                        
                        if (parts.size >= 8) {
                            try {
                                // 解析本地端口（十六进制）
                                val localAddrPort = parts[1].split(":")[1]
                                val localPort = Integer.parseInt(localAddrPort, 16)
                                
                                Log.d("CocoProxyVpnService", "Parsed UDP port: $localPort (hex: $localAddrPort) vs requested: $port")
                                
                                if (localPort == port) {
                                    // 找到匹配的端口，返回UID
                                    val uid = parts[7].toInt()
                                    Log.d("CocoProxyVpnService", "Found matching UDP port $port with UID: $uid")
                                    return uid
                                }
                            } catch (e: Exception) {
                                Log.w("CocoProxyVpnService", "Error parsing UDP line: ${e.message}")
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.w("CocoProxyVpnService", "Error checking UDP connections: ${e.message}")
        }
        
        Log.w("CocoProxyVpnService", "Could not find UID for port: $port")
        return -1
    }
    
    /**
     * 根据UID获取应用包名
     */
    private fun getPackageNameByUid(uid: Int): String {
        try {
            if (uid < 0) return "unknown"
            
            val pm = packageManager
            val packages = pm.getPackagesForUid(uid)
            
            if (packages != null && packages.isNotEmpty()) {
                return packages[0]
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error getting package name by UID", e)
        }
        return "unknown"
    }
    
    /**
     * 处理TCP包（参考Shadowsocks实现）
     */
    private suspend fun handleTcpPacket(
        buffer: ByteBuffer,
        length: Int,
        sourceIp: String,
        sourcePort: Int,
        destIp: String,
        destPort: Int,
        vpnOutput: FileOutputStream
    ) = withContext(Dispatchers.IO) {
        try {
            // 解析TCP包
            val ipHeaderLength = (buffer.get(0).toInt() and 0x0F) * 4
            val tcpHeaderLength = ((buffer.get(ipHeaderLength + 12).toInt() and 0xF0) shr 4) * 4
            val dataOffset = ipHeaderLength + tcpHeaderLength
            
            // 提取TCP头部信息
            val tcpFlags = buffer.get(ipHeaderLength + 13).toInt() and 0xFF
            val seqNum = buffer.getInt(ipHeaderLength + 4).toLong() and 0xFFFFFFFFL
            val ackNum = buffer.getInt(ipHeaderLength + 8).toLong() and 0xFFFFFFFFL
            
            val isSyn = (tcpFlags and 0x02) != 0
            val isAck = (tcpFlags and 0x10) != 0
            val isPsh = (tcpFlags and 0x08) != 0
            val isFin = (tcpFlags and 0x01) != 0
            val isRst = (tcpFlags and 0x04) != 0

            val connectionKey = "$sourceIp:$sourcePort-$destIp:$destPort"

            Log.d("CocoProxyVpnService", "TCP packet: $sourceIp:$sourcePort -> $destIp:$destPort, flags: ${getTcpFlagString(tcpFlags)}, seq: $seqNum, ack: $ackNum")

            // 获取或创建TCP连接状态
            var tcpState = tcpConnections[connectionKey]
            if (tcpState == null) {
                tcpState = TcpConnectionState()
                tcpConnections[connectionKey] = tcpState
            }

            // 处理不同的TCP状态
            when {
                isRst -> {
                    // RST包 - 立即重置连接
                    Log.d("CocoProxyVpnService", "Processing RST packet - resetting connection")
                    handleRstPacket(tcpState, connectionKey)
                }
                isSyn && !isAck -> {
                    // SYN包 - 开始TCP握手
                    Log.d("CocoProxyVpnService", "Processing SYN packet")
                    handleSynPacket(tcpState, connectionKey, seqNum, sourceIp, sourcePort, destIp, destPort, vpnOutput)
                }
                isAck && !isSyn && !isPsh && (length == dataOffset) -> {
                    // 纯ACK包 - 完成三次握手或确认数据
                    Log.d("CocoProxyVpnService", "Processing pure ACK packet")
                    if (tcpState.state == TcpState.SYN_SENT || tcpState.state == TcpState.SYN_RECEIVED) {
                        handleAckPacket(tcpState, ackNum)
                    }
                }
                isPsh || (length > dataOffset) -> {
                    // 数据包（可能也包含ACK）
                    Log.d("CocoProxyVpnService", "Processing data packet")
                    val dataLength = length - dataOffset
                    if (dataLength > 0) {
                        handleDataPacket(tcpState, connectionKey, buffer, dataOffset, dataLength, destIp, destPort)
                    }
                }
                isFin -> {
                    // FIN包 - 关闭连接
                    Log.d("CocoProxyVpnService", "Processing FIN packet")
                    handleFinPacket(tcpState, connectionKey)
                }
                else -> {
                    Log.d("CocoProxyVpnService", "Unhandled TCP packet: flags=${getTcpFlagString(tcpFlags)}, state=${tcpState.state}")
                }
            }
            
            tcpState.lastActivity = System.currentTimeMillis()
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error in handleTcpPacket: ${e.message}", e)
        }
    }
    
    /**
     * 处理SYN包 - 建立连接
     */
    private suspend fun handleSynPacket(
        tcpState: TcpConnectionState,
        connectionKey: String,
        clientSeq: Long,
        sourceIp: String,
        sourcePort: Int,
        destIp: String,
        destPort: Int,
        vpnOutput: FileOutputStream
    ) {
        Log.d("CocoProxyVpnService", "Handling SYN packet for $destIp:$destPort, current state: ${tcpState.state}")

        // 检查是否是SYN重传
        if (tcpState.state == TcpState.SYN_RECEIVED) {
            Log.d("CocoProxyVpnService", "SYN retransmission detected, resending SYN-ACK with same sequence numbers")

            // 重发原来的SYN-ACK，使用相同的序列号
            val synAckSeq = tcpState.localSeq - 1  // localSeq已经递增过了，所以要减1
            val synAckAck = tcpState.remoteSeq + 1

            Log.d("CocoProxyVpnService", "Resending SYN-ACK: seq=$synAckSeq, ack=$synAckAck (retransmission)")

            sendTcpResponse(
                destIp, destPort, sourceIp, sourcePort,
                synAckSeq, synAckAck,
                0x12, // SYN + ACK
                null, vpnOutput
            )

            Log.d("CocoProxyVpnService", "SYN-ACK retransmitted for $destIp:$destPort")
            return
        }

        // 新的SYN包，建立新连接
        Log.d("CocoProxyVpnService", "New SYN packet, establishing connection")

        // 首先清理可能存在的旧连接
        cleanupConnection(connectionKey)

        // 创建到CocoProxy服务器的连接
        val proxySocket = connectToCocoProxyServerForTcp(destIp, destPort)
        if (proxySocket == null) {
            Log.e("CocoProxyVpnService", "Failed to connect to CocoProxy server for $destIp:$destPort")
            // 发送RST包
            sendTcpResponse(sourceIp, sourcePort, destIp, destPort, 0, clientSeq + 1, 0x04, null, vpnOutput)
            tcpState.state = TcpState.CLOSED
            return
        }

        // 保存连接（使用正确的键）
        val proxyConnectionKey = "$destIp:$destPort"
        activeProxyConnections[proxyConnectionKey] = proxySocket

        // 更新TCP状态
        tcpState.remoteSeq = clientSeq
        tcpState.localSeq = generateInitialSeq()
        tcpState.state = TcpState.SYN_RECEIVED

        Log.d("CocoProxyVpnService", "SYN packet processed: clientSeq=$clientSeq, generated localSeq=${tcpState.localSeq}")

        // 发送SYN-ACK响应
        val synAckSeq = tcpState.localSeq
        val synAckAck = tcpState.remoteSeq + 1

        Log.d("CocoProxyVpnService", "Sending SYN-ACK: seq=$synAckSeq, ack=$synAckAck")

        sendTcpResponse(
            destIp, destPort, sourceIp, sourcePort,
            synAckSeq, synAckAck,
            0x12, // SYN + ACK
            null, vpnOutput
        )

        tcpState.localSeq++
        Log.d("CocoProxyVpnService", "After SYN-ACK: localSeq incremented to ${tcpState.localSeq}")
        Log.d("CocoProxyVpnService", "Expected ACK ackNum should be: ${synAckSeq + 1}")

        // 保持SYN_RECEIVED状态，等待客户端的ACK
        Log.d("CocoProxyVpnService", "TCP state after SYN-ACK: ${tcpState.state}, waiting for ACK")

        // 启动响应处理
        serviceScope.launch {
            handleProxyResponseNew(proxySocket, connectionKey, sourceIp, sourcePort, destIp, destPort, vpnOutput)
        }

        Log.d("CocoProxyVpnService", "SYN-ACK sent for $destIp:$destPort")
    }
    
    /**
     * 处理ACK包
     */
    private fun handleAckPacket(tcpState: TcpConnectionState, ackNum: Long) {
        Log.d("CocoProxyVpnService", "Processing ACK packet: state=${tcpState.state}, ackNum=$ackNum, localSeq=${tcpState.localSeq}")

        // 服务器端：SYN_RECEIVED -> ESTABLISHED
        // ACK包的确认号应该是我们发送的SYN-ACK的序列号+1
        if (tcpState.state == TcpState.SYN_RECEIVED && ackNum == tcpState.localSeq) {
            tcpState.state = TcpState.ESTABLISHED
            Log.d("CocoProxyVpnService", "TCP connection established (server side): $ackNum == ${tcpState.localSeq}")
        }
        // 客户端：SYN_SENT -> ESTABLISHED
        else if (tcpState.state == TcpState.SYN_SENT && ackNum == tcpState.localSeq) {
            tcpState.state = TcpState.ESTABLISHED
            Log.d("CocoProxyVpnService", "TCP connection established (client side): $ackNum == ${tcpState.localSeq}")
        }
        // 允许一定的序列号偏差（处理重传等情况）
        else if (tcpState.state == TcpState.SYN_RECEIVED && Math.abs(ackNum - tcpState.localSeq) <= 1) {
            tcpState.state = TcpState.ESTABLISHED
            Log.d("CocoProxyVpnService", "TCP connection established with sequence tolerance: ackNum=$ackNum, localSeq=${tcpState.localSeq}")
        }
        else {
            Log.d("CocoProxyVpnService", "ACK packet not processed: state=${tcpState.state}, ackNum=$ackNum, localSeq=${tcpState.localSeq}")
        }
    }
    
    /**
     * 处理数据包
     */
    private suspend fun handleDataPacket(
        tcpState: TcpConnectionState,
        connectionKey: String,
        buffer: ByteBuffer,
        dataOffset: Int,
        dataLength: Int,
        destIp: String,
        destPort: Int
    ) {
        if (tcpState.state != TcpState.ESTABLISHED) {
            Log.w("CocoProxyVpnService", "Received data packet but connection not established, state: ${tcpState.state}")
            // 如果是ACK包且状态是SYN_RECEIVED，说明握手完成
            if (tcpState.state == TcpState.SYN_RECEIVED) {
                tcpState.state = TcpState.ESTABLISHED
                Log.d("CocoProxyVpnService", "Connection established via data packet (SYN_RECEIVED -> ESTABLISHED)")
            }
            // 如果是ACK包且状态是SYN_SENT，说明握手完成（客户端模式）
            else if (tcpState.state == TcpState.SYN_SENT) {
                tcpState.state = TcpState.ESTABLISHED
                Log.d("CocoProxyVpnService", "Connection established via data packet (SYN_SENT -> ESTABLISHED)")
            } else {
                return
            }
        }
        
        // 使用正确的连接键查找代理连接
        val proxyConnectionKey = "$destIp:$destPort"
        val proxySocket = activeProxyConnections[proxyConnectionKey]
        if (proxySocket == null || proxySocket.isClosed) {
            Log.w("CocoProxyVpnService", "No proxy connection for data packet, key: $proxyConnectionKey")
            return
        }
        
        try {
            val output = proxySocket.getOutputStream()
            output.write(buffer.array(), dataOffset, dataLength)
            output.flush()

            // 更新远程序列号
            tcpState.remoteSeq += dataLength
            Log.d("CocoProxyVpnService", "Sent $dataLength bytes to proxy server, updated remoteSeq to ${tcpState.remoteSeq}")

            // 发送ACK响应给客户端，确认数据已接收
            serviceScope.launch {
                val vpnOutput = FileOutputStream(vpnInterface?.fileDescriptor)
                // 从connectionKey中提取源IP和端口
                val parts = connectionKey.split("-")
                val sourceParts = parts[0].split(":")
                val sourceIp = sourceParts[0]
                val sourcePort = sourceParts[1].toInt()

                sendTcpResponse(
                    destIp, destPort, // 源地址（服务器）
                    sourceIp, sourcePort, // 目标地址（客户端）
                    tcpState.localSeq, tcpState.remoteSeq,
                    0x10, // ACK
                    null, vpnOutput
                )
                Log.d("CocoProxyVpnService", "Sent ACK for received data to $sourceIp:$sourcePort")
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error sending data to proxy: ${e.message}")
        }
    }
    
    /**
     * 处理FIN包
     */
    private fun handleFinPacket(tcpState: TcpConnectionState, connectionKey: String) {
        tcpState.state = TcpState.CLOSE_WAIT
        cleanupConnection(connectionKey)
        Log.d("CocoProxyVpnService", "Connection closed via FIN")
    }

    /**
     * 处理RST包
     */
    private fun handleRstPacket(tcpState: TcpConnectionState, connectionKey: String) {
        Log.d("CocoProxyVpnService", "RST packet received, resetting connection: $connectionKey")
        tcpState.state = TcpState.CLOSED
        cleanupConnection(connectionKey)
        Log.d("CocoProxyVpnService", "Connection reset via RST")
    }

    /**
     * 清理连接资源
     */
    private fun cleanupConnection(connectionKey: String) {
        // 从连接映射中移除
        tcpConnections.remove(connectionKey)

        // 提取目标地址并关闭代理连接
        val parts = connectionKey.split("-")
        if (parts.size >= 2) {
            val destPart = parts[1] // 格式: IP:Port
            activeProxyConnections.remove(destPart)?.let { socket ->
                try {
                    socket.close()
                    Log.d("CocoProxyVpnService", "Closed proxy socket for $destPart")
                } catch (e: Exception) {
                    Log.w("CocoProxyVpnService", "Error closing proxy socket: ${e.message}")
                }
            }
        }
    }

    /**
     * 启动连接清理任务
     */
    private suspend fun startConnectionCleanupTask() {
        while (_isRunning.value) {
            try {
                delay(30000) // 每30秒清理一次
                cleanupStaleConnections()
            } catch (e: Exception) {
                Log.e("CocoProxyVpnService", "Error in connection cleanup task: ${e.message}")
            }
        }
    }

    /**
     * 清理过期连接
     */
    private fun cleanupStaleConnections() {
        val now = System.currentTimeMillis()
        val staleConnections = mutableListOf<String>()

        // 查找过期连接（超过60秒无活动或SYN_RECEIVED状态超过10秒）
        tcpConnections.forEach { (key, state) ->
            val timeSinceLastActivity = now - state.lastActivity
            when {
                timeSinceLastActivity > 60000 -> { // 60秒无活动
                    staleConnections.add(key)
                    Log.d("CocoProxyVpnService", "Marking connection as stale due to inactivity: $key")
                }
                state.state == TcpState.SYN_RECEIVED && timeSinceLastActivity > 10000 -> { // SYN_RECEIVED状态超过10秒
                    staleConnections.add(key)
                    Log.d("CocoProxyVpnService", "Marking SYN_RECEIVED connection as stale: $key")
                }
            }
        }

        // 清理过期连接
        staleConnections.forEach { key ->
            cleanupConnection(key)
        }

        if (staleConnections.isNotEmpty()) {
            Log.d("CocoProxyVpnService", "Cleaned up ${staleConnections.size} stale connections")
        }
    }
    
    /**
     * 发送TCP响应包
     */
    private suspend fun sendTcpResponse(
        sourceIp: String, sourcePort: Int,
        destIp: String, destPort: Int,
        seqNum: Long, ackNum: Long,
        flags: Int, data: ByteArray?,
        vpnOutput: FileOutputStream
    ) = withContext(Dispatchers.IO) {
        try {
            val dataLen = data?.size ?: 0
            val packet = buildTcpPacket(sourceIp, sourcePort, destIp, destPort, seqNum, ackNum, flags, data)

            Log.d("CocoProxyVpnService", "Built TCP packet: ${packet.size} bytes, seq=$seqNum, ack=$ackNum")
            Log.d("CocoProxyVpnService", "Packet header: ${packet.take(40).joinToString(" ") { "%02x".format(it) }}")

            vpnOutput.write(packet)
            vpnOutput.flush()

            Log.d("CocoProxyVpnService", "Sent TCP response: $sourceIp:$sourcePort -> $destIp:$destPort, flags: ${getTcpFlagString(flags)}, data: $dataLen bytes")
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error sending TCP response: ${e.message}")
        }
    }
    
    /**
     * 构建TCP包（修复字节序和校验和问题）
     */
    private fun buildTcpPacket(
        sourceIp: String, sourcePort: Int,
        destIp: String, destPort: Int,
        seqNum: Long, ackNum: Long,
        flags: Int, data: ByteArray?
    ): ByteArray {
        val dataLen = data?.size ?: 0
        val totalLen = 20 + 20 + dataLen // IP头 + TCP头 + 数据
        
        val packet = ByteArray(totalLen)
        var offset = 0
        
        // IP头部 (20字节) - 网络字节序（大端序）
        packet[offset++] = 0x45.toByte() // 版本(4) + 头长度(5*4=20)
        packet[offset++] = 0x00.toByte() // 服务类型
        packet[offset++] = (totalLen shr 8).toByte() // 总长度高字节
        packet[offset++] = (totalLen and 0xFF).toByte() // 总长度低字节
        packet[offset++] = 0x00.toByte() // 标识高字节
        packet[offset++] = 0x01.toByte() // 标识低字节（使用非零值）
        packet[offset++] = 0x40.toByte() // 标志(DF=1) + 片偏移高3位
        packet[offset++] = 0x00.toByte() // 片偏移低8位
        packet[offset++] = 0x40.toByte() // TTL = 64
        packet[offset++] = 0x06.toByte() // 协议 = TCP
        packet[offset++] = 0x00.toByte() // 校验和高字节（稍后计算）
        packet[offset++] = 0x00.toByte() // 校验和低字节
        
        // 源IP地址
        val sourceIpBytes = sourceIp.split(".").map { it.toInt().toByte() }.toByteArray()
        System.arraycopy(sourceIpBytes, 0, packet, offset, 4)
        offset += 4
        
        // 目标IP地址
        val destIpBytes = destIp.split(".").map { it.toInt().toByte() }.toByteArray()
        System.arraycopy(destIpBytes, 0, packet, offset, 4)
        offset += 4
        
        // TCP头部 (20字节) - 网络字节序
        packet[offset++] = (sourcePort shr 8).toByte() // 源端口高字节
        packet[offset++] = (sourcePort and 0xFF).toByte() // 源端口低字节
        packet[offset++] = (destPort shr 8).toByte() // 目标端口高字节
        packet[offset++] = (destPort and 0xFF).toByte() // 目标端口低字节
        
        // 序列号（32位，网络字节序）
        packet[offset++] = ((seqNum shr 24) and 0xFF).toByte()
        packet[offset++] = ((seqNum shr 16) and 0xFF).toByte()
        packet[offset++] = ((seqNum shr 8) and 0xFF).toByte()
        packet[offset++] = (seqNum and 0xFF).toByte()
        
        // 确认号（32位，网络字节序）
        packet[offset++] = ((ackNum shr 24) and 0xFF).toByte()
        packet[offset++] = ((ackNum shr 16) and 0xFF).toByte()
        packet[offset++] = ((ackNum shr 8) and 0xFF).toByte()
        packet[offset++] = (ackNum and 0xFF).toByte()
        
        packet[offset++] = 0x50.toByte() // 数据偏移(5*4=20字节) + 保留位
        packet[offset++] = (flags and 0xFF).toByte() // TCP标志位
        packet[offset++] = 0x72.toByte() // 窗口大小高字节 (29200)
        packet[offset++] = 0x10.toByte() // 窗口大小低字节
        packet[offset++] = 0x00.toByte() // TCP校验和高字节（稍后计算）
        packet[offset++] = 0x00.toByte() // TCP校验和低字节
        packet[offset++] = 0x00.toByte() // 紧急指针高字节
        packet[offset++] = 0x00.toByte() // 紧急指针低字节
        
        // 数据部分
        data?.let { 
            System.arraycopy(it, 0, packet, offset, dataLen)
        }
        
        // 计算IP头校验和
        calculateIpChecksum(packet, 0, 20)
        
        // 计算TCP校验和
        calculateTcpChecksum(packet, sourceIpBytes, destIpBytes, dataLen)
        
        return packet
    }
    
    /**
     * 计算IP头校验和
     */
    private fun calculateIpChecksum(packet: ByteArray, offset: Int, length: Int) {
        // 清零校验和字段
        packet[offset + 10] = 0
        packet[offset + 11] = 0
        
        var sum = 0L
        for (i in 0 until length step 2) {
            val word = ((packet[offset + i].toInt() and 0xFF) shl 8) or 
                      (packet[offset + i + 1].toInt() and 0xFF)
            sum += word
        }
        
        // 处理进位
        while (sum shr 16 != 0L) {
            sum = (sum and 0xFFFF) + (sum shr 16)
        }
        
        // 取反
        val checksum = (sum.inv() and 0xFFFF).toInt()
        packet[offset + 10] = (checksum shr 8).toByte()
        packet[offset + 11] = (checksum and 0xFF).toByte()
    }
    
    /**
     * 计算TCP校验和
     */
    private fun calculateTcpChecksum(packet: ByteArray, sourceIp: ByteArray, destIp: ByteArray, dataLen: Int) {
        val tcpOffset = 20 // TCP头在IP包中的偏移
        val tcpLen = 20 + dataLen // TCP头长度 + 数据长度
        
        // 清零TCP校验和字段
        packet[tcpOffset + 16] = 0
        packet[tcpOffset + 17] = 0
        
        var sum = 0L
        
        // 伪头部校验和
        // 源IP
        for (i in 0 until 4 step 2) {
            sum += ((sourceIp[i].toInt() and 0xFF) shl 8) or (sourceIp[i + 1].toInt() and 0xFF)
        }
        // 目标IP
        for (i in 0 until 4 step 2) {
            sum += ((destIp[i].toInt() and 0xFF) shl 8) or (destIp[i + 1].toInt() and 0xFF)
        }
        // 协议号
        sum += 6 // TCP协议号
        // TCP长度
        sum += tcpLen
        
        // TCP头部和数据校验和
        var i = tcpOffset
        while (i < tcpOffset + tcpLen - 1) {
            val word = ((packet[i].toInt() and 0xFF) shl 8) or (packet[i + 1].toInt() and 0xFF)
            sum += word
            i += 2
        }
        
        // 如果TCP长度为奇数，处理最后一个字节
        if (tcpLen % 2 == 1) {
            sum += (packet[tcpOffset + tcpLen - 1].toInt() and 0xFF) shl 8
        }
        
        // 处理进位
        while (sum shr 16 != 0L) {
            sum = (sum and 0xFFFF) + (sum shr 16)
        }
        
        // 取反
        val checksum = (sum.inv() and 0xFFFF).toInt()
        packet[tcpOffset + 16] = (checksum shr 8).toByte()
        packet[tcpOffset + 17] = (checksum and 0xFF).toByte()
    }
    
    /**
     * 生成初始序列号
     */
    private fun generateInitialSeq(): Long = (System.currentTimeMillis() and 0xFFFFFFFFL)
    
    /**
     * 获取TCP标志字符串
     */
    private fun getTcpFlagString(flags: Int): String {
        val flagNames = mutableListOf<String>()
        if (flags and 0x01 != 0) flagNames.add("FIN")
        if (flags and 0x02 != 0) flagNames.add("SYN")
        if (flags and 0x04 != 0) flagNames.add("RST")
        if (flags and 0x08 != 0) flagNames.add("PSH")
        if (flags and 0x10 != 0) flagNames.add("ACK")
        if (flags and 0x20 != 0) flagNames.add("URG")
        return flagNames.joinToString("|").ifEmpty { "NONE" }
    }
    
    /**
     * 新的代理响应处理方法
     */
    private suspend fun handleProxyResponseNew(
        proxySocket: Socket,
        connectionKey: String,
        sourceIp: String,
        sourcePort: Int,
        destIp: String,
        destPort: Int,
        vpnOutput: FileOutputStream
    ) = withContext(Dispatchers.IO) {
        try {
            val input = proxySocket.getInputStream()
            val buffer = ByteArray(VPN_MTU)
            
            Log.d("CocoProxyVpnService", "Starting proxy response handler for $connectionKey")
            
            while (!proxySocket.isClosed && _isRunning.value) {
                try {
                    val bytesRead = input.read(buffer)
                    if (bytesRead <= 0) {
                        Log.d("CocoProxyVpnService", "Proxy connection closed for $connectionKey")
                        break
                    }
                    
                    Log.d("CocoProxyVpnService", "Received $bytesRead bytes from proxy for $connectionKey")

                    // 获取TCP状态
                    val tcpState = tcpConnections[connectionKey]
                    if (tcpState != null) {
                        // 如果状态是SYN_RECEIVED，说明握手还没完成，但我们可以处理数据并转换状态
                        if (tcpState.state == TcpState.SYN_RECEIVED) {
                            tcpState.state = TcpState.ESTABLISHED
                            Log.d("CocoProxyVpnService", "TCP connection established via proxy response (SYN_RECEIVED -> ESTABLISHED) for $connectionKey")
                        }

                        if (tcpState.state == TcpState.ESTABLISHED) {
                            // 发送数据包给客户端
                            val responseData = ByteArray(bytesRead)
                            System.arraycopy(buffer, 0, responseData, 0, bytesRead)

                            Log.d("CocoProxyVpnService", "Sending proxy response: seq=${tcpState.localSeq}, ack=${tcpState.remoteSeq}, data=$bytesRead bytes")

                            sendTcpResponse(
                                destIp, destPort, sourceIp, sourcePort,
                                tcpState.localSeq, tcpState.remoteSeq,
                                0x18, // PSH + ACK
                                responseData, vpnOutput
                            )

                            // 更新序列号
                            tcpState.localSeq += bytesRead
                            Log.d("CocoProxyVpnService", "Updated TCP state for $connectionKey: localSeq=${tcpState.localSeq}, remoteSeq=${tcpState.remoteSeq}")
                            Log.d("CocoProxyVpnService", "Sent proxy response to client: $bytesRead bytes, seq=${tcpState.localSeq - bytesRead}, ack=${tcpState.remoteSeq}")
                        } else {
                            Log.w("CocoProxyVpnService", "Received proxy data but TCP state is ${tcpState.state} for $connectionKey")
                        }
                    } else {
                        Log.w("CocoProxyVpnService", "Received proxy data but no TCP state found for $connectionKey")
                    }
                    
                } catch (e: java.net.SocketTimeoutException) {
                    continue
                } catch (e: Exception) {
                    Log.d("CocoProxyVpnService", "Error in proxy response handler: ${e.message}")
                    break
                }
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error in handleProxyResponseNew: ${e.message}", e)
        } finally {
            try {
                proxySocket.close()
                activeProxyConnections.remove(connectionKey)
                tcpConnections.remove(connectionKey)
            } catch (e: Exception) {
                // Ignore
            }
        }
    }
    
    /**
     * 处理代理服务器响应并构建IP包
     */
    private suspend fun handleProxyResponse(
        proxySocket: Socket,
        originalPacket: ByteBuffer,
        vpnOutput: FileOutputStream,
        sourceIp: String,
        sourcePort: Int,
        destIp: String,
        destPort: Int
    ) = withContext(Dispatchers.IO) {
        try {
            val input = proxySocket.getInputStream()
            val responseBuffer = ByteArray(VPN_MTU)
            
            // 设置读取超时
            proxySocket.soTimeout = 5000 // 5秒超时
            
            Log.d("CocoProxyVpnService", "Starting response handler for $destIp:$destPort")
            
            while (!proxySocket.isClosed && _isRunning.value) {
                try {
                    val bytesRead = input.read(responseBuffer)
                    if (bytesRead <= 0) {
                        Log.d("CocoProxyVpnService", "Proxy connection closed for $destIp:$destPort (bytesRead: $bytesRead)")
                        break
                    }
                    
                    Log.d("CocoProxyVpnService", "Received $bytesRead bytes from CocoProxy server for $destIp:$destPort")
                    
                    // 构建响应IP包
                    val responsePacket = buildTcpResponsePacket(
                        destIp, destPort, sourceIp, sourcePort,
                        responseBuffer, bytesRead, originalPacket
                    )
                    
                    if (responsePacket != null) {
                        vpnOutput.write(responsePacket)
                        vpnOutput.flush()
                        Log.d("CocoProxyVpnService", "Sent response packet to VPN interface: ${responsePacket.size} bytes")
                    }
                    
                } catch (e: java.net.SocketTimeoutException) {
                    // 超时是正常的，继续等待
                    Log.v("CocoProxyVpnService", "Read timeout for $destIp:$destPort, continuing...")
                    continue
                } catch (e: Exception) {
                    Log.d("CocoProxyVpnService", "Error reading proxy response for $destIp:$destPort: ${e.message}")
                    break
                }
            }
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error handling proxy response for $destIp:$destPort", e)
        } finally {
            try {
                proxySocket.close()
            } catch (e: Exception) {
                // Ignore close errors
            }
        }
    }
    
    /**
     * 构建TCP响应包
     */
    private fun buildTcpResponsePacket(
        sourceIp: String, sourcePort: Int,
        destIp: String, destPort: Int,
        data: ByteArray, dataLength: Int,
        originalPacket: ByteBuffer
    ): ByteArray? {
        try {
            // 简化的IP包构建 - 实际应用中需要更完整的TCP状态管理
            val packet = ByteArrayOutputStream()
            
            // IP头部 (20字节)
            packet.write(0x45) // 版本(4) + 头部长度(5*4=20字节)
            packet.write(0x00) // 服务类型
            
            val totalLength = 20 + 20 + dataLength // IP头 + TCP头 + 数据
            packet.write((totalLength shr 8) and 0xFF) // 总长度高字节
            packet.write(totalLength and 0xFF) // 总长度低字节
            
            packet.write(0x00) // 标识高字节
            packet.write(0x00) // 标识低字节
            packet.write(0x40) // 标志(DF=1) + 片偏移高3位
            packet.write(0x00) // 片偏移低8位
            
            packet.write(0x40) // TTL
            packet.write(0x06) // 协议(TCP)
            packet.write(0x00) // 校验和(暂时为0)
            packet.write(0x00)
            
            // 源IP (服务器IP)
            sourceIp.split(".").forEach { octet ->
                packet.write(octet.toInt())
            }
            
            // 目标IP (客户端IP)
            destIp.split(".").forEach { octet ->
                packet.write(octet.toInt())
            }
            
            // TCP头部 (20字节)
            packet.write((sourcePort shr 8) and 0xFF) // 源端口高字节
            packet.write(sourcePort and 0xFF) // 源端口低字节
            packet.write((destPort shr 8) and 0xFF) // 目标端口高字节
            packet.write(destPort and 0xFF) // 目标端口低字节
            
            // 序列号 (简化处理)
            packet.write(0x00)
            packet.write(0x00)
            packet.write(0x00)
            packet.write(0x01)
            
            // 确认号 (简化处理)
            packet.write(0x00)
            packet.write(0x00)
            packet.write(0x00)
            packet.write(0x01)
            
            packet.write(0x50) // 数据偏移(5*4=20字节) + 保留位
            packet.write(0x18) // 标志位(PSH + ACK)
            packet.write(0xFF) // 窗口大小高字节
            packet.write(0xFF) // 窗口大小低字节
            packet.write(0x00) // 校验和(暂时为0)
            packet.write(0x00)
            packet.write(0x00) // 紧急指针
            packet.write(0x00)
            
            // 数据
            packet.write(data, 0, dataLength)
            
            return packet.toByteArray()
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error building TCP response packet", e)
            return null
        }
    }
    
    /**
     * 连接到CocoProxy服务器进行TCP代理
     */
    private fun connectToCocoProxyServerForTcp(targetHost: String, targetPort: Int): Socket? {
        try {
            // 连接到CocoProxy服务器
            val socket = Socket()
            socket.connect(InetSocketAddress(serverHost, serverPort), 10000)
            
            Log.d("CocoProxyVpnService", "Connected to CocoProxy server $serverHost:$serverPort")
            
            // 发送CocoProxy协议请求
            if (!sendCocoProxyRequestForTcp(socket, targetHost, targetPort)) {
                socket.close()
                return null
            }
            
            return socket
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error connecting to CocoProxy server for TCP", e)
            return null
        }
    }
    


    /**
     * 连接到CocoProxy服务器进行SOCKS5代理
     */
    private fun connectToCocoProxyServerForSocks5(targetHost: String, targetPort: Int): Socket? {
        try {
            // 连接到CocoProxy服务器
            val socket = Socket()
            socket.connect(InetSocketAddress(serverHost, serverPort), 10000)
            socket.soTimeout = 5000 // 设置5秒读取超时

            // 发送CocoProxy请求
            if (!sendCocoProxyRequestForSocks5(socket, targetHost, targetPort)) {
                socket.close()
                return null
            }

            return socket
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error connecting to CocoProxy server", e)
            return null
        }
    }
    




    /**
     * 发送CocoProxy请求用于TCP代理
     */
    private fun sendCocoProxyRequestForTcp(socket: Socket, targetHost: String, targetPort: Int): Boolean {
        try {
            val output = socket.getOutputStream()
            
            Log.d("CocoProxyVpnService", "Sending CocoProxy TCP request for $targetHost:$targetPort")
            
            // 构建CocoProxy协议请求（参考CocoProxyClient的实现）
            val protocolType: Byte = 0x03 // SOCKS5协议（与Python客户端保持一致）
            val encryptType: Byte = 0x00 // 无加密
            val usernameBytes = serverUsername.toByteArray(Charsets.UTF_8)
            val usernameLen = usernameBytes.size
            
            if (usernameLen > 255) {
                Log.e("CocoProxyVpnService", "Username too long: $usernameLen")
                return false
            }
            
            // 准备目标数据
            val targetData = "$targetHost:$targetPort".toByteArray(Charsets.UTF_8)
            
            // 创建数据包
            val packet = ByteArrayOutputStream()
            packet.write(protocolType.toInt())      // 1 byte protocol type
            packet.write(encryptType.toInt())       // 1 byte encrypt type  
            packet.write(usernameLen)               // 1 byte username length
            packet.write(usernameBytes)             // N bytes username
            packet.write(ByteArray(4))              // 4 bytes random padding
            
            // 写入数据长度（4字节，大端序）
            val dataLengthBytes = java.nio.ByteBuffer.allocate(4).putInt(targetData.size).array()
            packet.write(dataLengthBytes)           // 4 bytes data length
            packet.write(targetData)                // Target data
            
            val packetBytes = packet.toByteArray()
            
            Log.d("CocoProxyVpnService", "Sending CocoProxy packet: ${packetBytes.size} bytes")
            Log.d("CocoProxyVpnService", "Protocol: $protocolType, Target: $targetHost:$targetPort, Data: ${targetData.size} bytes")
            
            // 发送数据包
            output.write(packetBytes)
            output.flush()
            
            Log.d("CocoProxyVpnService", "CocoProxy TCP request sent successfully")
            
            // 对于SOCKS5协议，需要进行SOCKS5握手（参考Python版本）
            return performSocks5HandshakeForVpn(socket, targetHost, targetPort)
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error sending CocoProxy TCP request", e)
            return false
        }
    }
    
    /**
     * 为VPN执行SOCKS5握手（参考Python版本的实现）
     */
    private fun performSocks5HandshakeForVpn(socket: Socket, targetHost: String, targetPort: Int): Boolean {
        try {
            val input = socket.getInputStream()
            val output = socket.getOutputStream()
            
            Log.d("CocoProxyVpnService", "Starting SOCKS5 handshake for VPN: $targetHost:$targetPort")
            
            // Step 1: Send SOCKS5 authentication request
            val authRequest = byteArrayOf(0x05, 0x01, 0x00) // VER=5, NMETHODS=1, METHOD=0 (no auth)
            output.write(authRequest)
            output.flush()
            Log.d("CocoProxyVpnService", "Sent SOCKS5 auth request")
            
            // Step 2: Receive SOCKS5 authentication response
            socket.soTimeout = 10000 // 10秒超时
            val authResponse = ByteArray(2)
            val authBytesRead = input.read(authResponse)
            
            if (authBytesRead != 2 || authResponse[0] != 0x05.toByte() || authResponse[1] != 0x00.toByte()) {
                Log.e("CocoProxyVpnService", "SOCKS5 auth failed for VPN: $targetHost:$targetPort, response: ${authResponse.joinToString { "%02x".format(it) }}")
                return false
            }
            Log.d("CocoProxyVpnService", "SOCKS5 auth successful for VPN")
            
            // Step 3: Send SOCKS5 connect request
            val connectRequest = ByteArrayOutputStream()
            connectRequest.write(0x05) // VER
            connectRequest.write(0x01) // CMD (CONNECT)
            connectRequest.write(0x00) // RSV
            connectRequest.write(0x03) // ATYP (DOMAIN)
            connectRequest.write(targetHost.length) // Domain length
            connectRequest.write(targetHost.toByteArray()) // Domain
            connectRequest.write((targetPort shr 8) and 0xFF) // Port high byte
            connectRequest.write(targetPort and 0xFF) // Port low byte
            
            output.write(connectRequest.toByteArray())
            output.flush()
            Log.d("CocoProxyVpnService", "Sent SOCKS5 connect request for VPN: $targetHost:$targetPort")
            
            // Step 4: Receive SOCKS5 connect response
            val connectResponse = ByteArray(10) // Minimum response size
            val connectBytesRead = try {
                input.read(connectResponse, 0, 4) // Read at least 4 bytes
            } catch (e: java.net.SocketTimeoutException) {
                Log.w("CocoProxyVpnService", "SOCKS5 connect response timeout for VPN: $targetHost:$targetPort, assuming success")
                // 如果超时，可能连接已经建立，继续处理
                return true
            }
            
            if (connectBytesRead < 4 || connectResponse[0] != 0x05.toByte() || connectResponse[1] != 0x00.toByte()) {
                Log.e("CocoProxyVpnService", "SOCKS5 connect failed for VPN: $targetHost:$targetPort, response: ${connectResponse.sliceArray(0..3).joinToString { "%02x".format(it) }}")
                return false
            }
            
            // Read remaining response data based on address type
            val addressType = connectResponse[3].toInt() and 0xFF
            val remainingBytes = when (addressType) {
                0x01 -> 6 // IPv4: 4 bytes IP + 2 bytes port
                0x03 -> {
                    // Domain: 1 byte length + domain + 2 bytes port
                    val domainLength = input.read()
                    domainLength + 2
                }
                0x04 -> 18 // IPv6: 16 bytes IP + 2 bytes port
                else -> 6 // Default to IPv4
            }
            
            // Read remaining response
            val remaining = ByteArray(remainingBytes)
            input.read(remaining)
            
            Log.d("CocoProxyVpnService", "SOCKS5 connect successful for VPN: $targetHost:$targetPort")
            return true
            
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "SOCKS5 handshake error for VPN: $targetHost:$targetPort: ${e.message}", e)
            return false
        }
    }
    
    /**
     * 发送CocoProxy请求用于SOCKS5代理（使用正确的CocoProxy协议格式）
     */
    private fun sendCocoProxyRequestForSocks5(socket: Socket, targetHost: String, targetPort: Int): Boolean {
        try {
            val output = socket.getOutputStream()
            val input = socket.getInputStream()

            Log.d("CocoProxyVpnService", "Sending CocoProxy SOCKS5 request for $targetHost:$targetPort")

            // 准备目标数据
            val targetData = "$targetHost:$targetPort".toByteArray(Charsets.UTF_8)
            val usernameBytes = serverUsername.toByteArray(Charsets.UTF_8)
            val usernameLen = usernameBytes.size

            if (usernameLen > 255) {
                Log.e("CocoProxyVpnService", "Username too long: $usernameLen")
                return false
            }

            // 构建CocoProxy协议包
            val packet = ByteArrayOutputStream()
            packet.write(0x03) // 协议类型：SOCKS5
            packet.write(0x00) // 加密类型：无加密
            packet.write(usernameLen) // 用户名长度
            packet.write(usernameBytes) // 用户名
            packet.write(byteArrayOf(0x00, 0x00, 0x00, 0x00)) // 4字节随机填充

            // 数据长度（网络字节序）
            val dataLength = targetData.size
            packet.write((dataLength shr 24) and 0xFF)
            packet.write((dataLength shr 16) and 0xFF)
            packet.write((dataLength shr 8) and 0xFF)
            packet.write(dataLength and 0xFF)

            // 目标数据
            packet.write(targetData)

            // 发送请求
            val requestData = packet.toByteArray()
            output.write(requestData)
            output.flush()

            Log.d("CocoProxyVpnService", "Sent CocoProxy SOCKS5 request: ${requestData.size} bytes")

            // 执行SOCKS5握手
            if (!performSocks5HandshakeForVpn(socket, targetHost, targetPort)) {
                Log.e("CocoProxyVpnService", "SOCKS5 handshake failed for $targetHost:$targetPort")
                return false
            }

            Log.d("CocoProxyVpnService", "CocoProxy SOCKS5 connection established for $targetHost:$targetPort")
            return true

        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error sending CocoProxy SOCKS5 request for $targetHost:$targetPort", e)
            return false
        }
    }
    
    /**
     * 提取UDP数据
     */
    private fun extractUdpData(packet: ByteBuffer): ByteArray? {
        try {
            // 获取IP头部长度
            val ipHeaderLength = (packet.get(0).toInt() and 0x0F) * 4
            
            // UDP头部长度固定为8字节
            val udpHeaderLength = 8
            
            // 计算数据起始位置和长度
            val dataOffset = ipHeaderLength + udpHeaderLength
            val dataLength = packet.limit() - dataOffset
            
            if (dataLength <= 0) {
                return null
            }
            
            // 提取数据
            val data = ByteArray(dataLength)
            packet.position(dataOffset)
            packet.get(data, 0, dataLength)
            
            return data
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error extracting UDP data", e)
            return null
        }
    }
    
    /**
     * 提取TCP数据
     */
    private fun extractTcpData(packet: ByteBuffer): ByteArray? {
        try {
            // 获取IP头部长度
            val ipHeaderLength = (packet.get(0).toInt() and 0x0F) * 4
            
            // 获取TCP头部长度
            val tcpHeaderOffset = ipHeaderLength + 12 // TCP头部中数据偏移字段的位置
            val tcpHeaderLength = ((packet.get(tcpHeaderOffset).toInt() and 0xF0) shr 4) * 4
            
            // 计算数据起始位置和长度
            val dataOffset = ipHeaderLength + tcpHeaderLength
            val dataLength = packet.limit() - dataOffset
            
            if (dataLength <= 0) {
                return null
            }
            
            // 提取数据
            val data = ByteArray(dataLength)
            packet.position(dataOffset)
            packet.get(data, 0, dataLength)
            
            return data
        } catch (e: Exception) {
            Log.e("CocoProxyVpnService", "Error extracting TCP data", e)
            return null
        }
    }
    
    /**
     * 创建通知渠道
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "CocoProxy VPN Service"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val stopIntent = Intent(this, CocoProxyVpnService::class.java).apply {
            action = ACTION_STOP_VPN
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("CocoProxy VPN")
            .setContentText("VPN service is running")
            .setSmallIcon(R.drawable.ic_proxy)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_stop,
                "Stop",
                stopPendingIntent
            )
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
}