package com.cocoproxy.client.ui.navigation;

/**
 * Main navigation destinations
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001aB!\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0004\b\u0007\u0010\bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\nR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u0082\u0001\r\u001b\u001c\u001d\u001e\u001f !\"#$%&\'\u00a8\u0006("}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen;", "", "route", "", "title", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "<init>", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;)V", "getRoute", "()Ljava/lang/String;", "getTitle", "getIcon", "()Landroidx/compose/ui/graphics/vector/ImageVector;", "Login", "Home", "Traffic", "Settings", "Logs", "About", "ServerConfig", "ProxyConfig", "AppSelection", "AppSplitMain", "SplitModeConfig", "AppSelectionCompose", "AppTrafficStats", "Lcom/cocoproxy/client/ui/navigation/Screen$About;", "Lcom/cocoproxy/client/ui/navigation/Screen$AppSelection;", "Lcom/cocoproxy/client/ui/navigation/Screen$AppSelectionCompose;", "Lcom/cocoproxy/client/ui/navigation/Screen$AppSplitMain;", "Lcom/cocoproxy/client/ui/navigation/Screen$AppTrafficStats;", "Lcom/cocoproxy/client/ui/navigation/Screen$Home;", "Lcom/cocoproxy/client/ui/navigation/Screen$Login;", "Lcom/cocoproxy/client/ui/navigation/Screen$Logs;", "Lcom/cocoproxy/client/ui/navigation/Screen$ProxyConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen$ServerConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen$Settings;", "Lcom/cocoproxy/client/ui/navigation/Screen$SplitModeConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen$Traffic;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String route = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String title = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.compose.ui.graphics.vector.ImageVector icon = null;
    
    private Screen(java.lang.String route, java.lang.String title, androidx.compose.ui.graphics.vector.ImageVector icon) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getRoute() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.compose.ui.graphics.vector.ImageVector getIcon() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$About;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class About extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.About INSTANCE = null;
        
        private About() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$AppSelection;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class AppSelection extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.AppSelection INSTANCE = null;
        
        private AppSelection() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$AppSelectionCompose;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class AppSelectionCompose extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.AppSelectionCompose INSTANCE = null;
        
        private AppSelectionCompose() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$AppSplitMain;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class AppSplitMain extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.AppSplitMain INSTANCE = null;
        
        private AppSplitMain() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$AppTrafficStats;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class AppTrafficStats extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.AppTrafficStats INSTANCE = null;
        
        private AppTrafficStats() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$Home;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class Home extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.Home INSTANCE = null;
        
        private Home() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$Login;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class Login extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.Login INSTANCE = null;
        
        private Login() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$Logs;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class Logs extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.Logs INSTANCE = null;
        
        private Logs() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$ProxyConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class ProxyConfig extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.ProxyConfig INSTANCE = null;
        
        private ProxyConfig() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$ServerConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class ServerConfig extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.ServerConfig INSTANCE = null;
        
        private ServerConfig() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$Settings;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class Settings extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.Settings INSTANCE = null;
        
        private Settings() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$SplitModeConfig;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class SplitModeConfig extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.SplitModeConfig INSTANCE = null;
        
        private SplitModeConfig() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/navigation/Screen$Traffic;", "Lcom/cocoproxy/client/ui/navigation/Screen;", "<init>", "()V", "app_debug"})
    public static final class Traffic extends com.cocoproxy.client.ui.navigation.Screen {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.navigation.Screen.Traffic INSTANCE = null;
        
        private Traffic() {
        }
    }
}