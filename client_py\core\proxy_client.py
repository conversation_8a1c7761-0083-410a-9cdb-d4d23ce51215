#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import json
import logging
import socket
import threading
import requests
from PySide6.QtCore import QObject, Signal
from .local_proxy import LocalProxyServer

class ProxyClient(QObject):
    """Client for CocoProxy server"""
    
    # Signals
    connection_status_changed = Signal(bool, str)  # connected, message
    traffic_updated = Signal(float, float)  # used, limit (using float to avoid overflow)
    proxy_status_changed = Signal(bool, str)  # proxy running, message
    error_occurred = Signal(str)  # error message
    
    def __init__(self, config):
        """Initialize proxy client"""
        super().__init__()
        self.config = config
        self.logger = logging.getLogger("CocoProxy.Client")
        self.connected = False
        self.session = requests.Session()
        self.traffic_monitor_thread = None
        self.stop_monitor = threading.Event()
        
        # Local proxy server
        self.local_proxy = LocalProxyServer(config)
        self.local_proxy.proxy_status_changed.connect(self.proxy_status_changed.emit)
    
    def login(self, username, password, remember=False):
        """Login to proxy server (async)"""
        # Always save username for current session (needed for proxy protocol)
        self.config.set("user", "username", username)
        
        # Save credentials if remember is True
        if remember:
            self.config.set("user", "password", password)
            self.config.set("user", "remember_me", True)
        else:
            self.config.set("user", "password", "")
            self.config.set("user", "remember_me", False)
        
        # Start login in background thread to avoid blocking UI
        def login_task():
            try:
                # Get server info
                server_host = self.config.get("server", "host")
                admin_port = self.config.get("server", "admin_port")
                
                # Login to admin API
                url = f"http://{server_host}:{admin_port}/api/login"
                payload = {
                    "username": username,
                    "password": password
                }
                
                response = self.session.post(url, json=payload, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    token = data.get("token")
                    
                    if token:
                        self.config.set("user", "token", token)
                        self.connection_status_changed.emit(True, "Login successful")
                        self.logger.info("Login successful")
                        
                        # Start traffic monitor
                        self.start_traffic_monitor()
                        
                        # Start local proxy server
                        proxy_success, proxy_message = self.local_proxy.start()
                        if not proxy_success:
                            self.logger.warning(f"Failed to start local proxy: {proxy_message}")
                    else:
                        self.connection_status_changed.emit(False, "Invalid response from server")
                        self.logger.error("Invalid response from server: %s", response.text)
                else:
                    error_msg = response.json().get("error", "Unknown error")
                    self.connection_status_changed.emit(False, f"Login failed: {error_msg}")
                    self.logger.error("Login failed: %s", error_msg)
            
            except requests.exceptions.ConnectionError:
                self.connection_status_changed.emit(False, "Connection error: Server unreachable")
                self.logger.error("Connection error: Server unreachable")
            except Exception as e:
                self.connection_status_changed.emit(False, f"Login error: {str(e)}")
                self.logger.error("Login error: %s", str(e))
        
        # Start login task in background thread
        login_thread = threading.Thread(target=login_task, daemon=True)
        login_thread.start()
        
        return True, "Login started..."
    
    def logout(self):
        """Logout from proxy server"""
        self.config.set("user", "token", "")
        self.stop_traffic_monitor()
        
        # Stop local proxy server
        if self.local_proxy.is_running():
            self.local_proxy.stop()
        
        self.connection_status_changed.emit(False, "Logged out")
        self.logger.info("Logged out")
        return True, "Logged out"
    
    def get_server_status(self):
        """Get server status"""
        try:
            # Get server info
            server_host = self.config.get("server", "host")
            admin_port = self.config.get("server", "admin_port")
            token = self.config.get("user", "token")
            
            if not token:
                return False, "Not logged in"
            
            # Get server status
            url = f"http://{server_host}:{admin_port}/api/status"
            headers = {"Authorization": token}
            
            response = self.session.get(url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                return True, response.json()
            else:
                error_msg = response.json().get("error", "Unknown error")
                self.logger.error("Failed to get server status: %s", error_msg)
                return False, f"Failed to get server status: {error_msg}"
        
        except requests.exceptions.ConnectionError:
            self.logger.error("Connection error: Server unreachable")
            return False, "Connection error: Server unreachable"
        except Exception as e:
            self.logger.error("Error getting server status: %s", str(e))
            return False, f"Error getting server status: {str(e)}"
    
    def get_user_info(self):
        """Get user information"""
        try:
            # Get server info
            server_host = self.config.get("server", "host")
            admin_port = self.config.get("server", "admin_port")
            token = self.config.get("user", "token")
            
            if not token:
                return False, "Not logged in"
            
            # Get user info using /api/me endpoint
            url = f"http://{server_host}:{admin_port}/api/me"
            headers = {"Authorization": token}
            
            response = self.session.get(url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                return True, response.json()
            else:
                try:
                    error_msg = response.json().get("error", "Unknown error")
                except:
                    error_msg = f"HTTP {response.status_code}"
                self.logger.error("Failed to get user info: %s", error_msg)
                return False, f"Failed to get user info: {error_msg}"
        
        except requests.exceptions.ConnectionError:
            self.logger.error("Connection error: Server unreachable")
            return False, "Connection error: Server unreachable"
        except Exception as e:
            self.logger.error("Error getting user info: %s", str(e))
            return False, f"Error getting user info: {str(e)}"
    
    def start_traffic_monitor(self):
        """Start traffic monitor thread"""
        if self.traffic_monitor_thread and self.traffic_monitor_thread.is_alive():
            return
        
        self.stop_monitor.clear()
        self.traffic_monitor_thread = threading.Thread(target=self._traffic_monitor_task)
        self.traffic_monitor_thread.daemon = True
        self.traffic_monitor_thread.start()
    
    def stop_traffic_monitor(self):
        """Stop traffic monitor thread"""
        if self.traffic_monitor_thread and self.traffic_monitor_thread.is_alive():
            self.stop_monitor.set()
            self.traffic_monitor_thread.join(2)
    
    def _traffic_monitor_task(self):
        """Traffic monitor task"""
        while not self.stop_monitor.is_set():
            try:
                success, data = self.get_user_info()
                
                if success and isinstance(data, dict):
                    # Safely convert to float with bounds checking
                    used_traffic_raw = data.get("used_traffic", 0)
                    traffic_limit_raw = data.get("traffic_limit", 0)
                    
                    # Convert to float and ensure reasonable bounds
                    try:
                        used_traffic = float(used_traffic_raw)
                        traffic_limit = float(traffic_limit_raw)
                        
                        # Sanity check: ensure values are not negative
                        used_traffic = max(0, used_traffic)
                        traffic_limit = max(0, traffic_limit)
                        
                        # Log large values for debugging
                        if used_traffic > 1e12 or traffic_limit > 1e12:  # > 1TB
                            self.logger.warning(f"Large traffic values detected: used={used_traffic}, limit={traffic_limit}")
                        
                        self.traffic_updated.emit(used_traffic, traffic_limit)
                        
                    except (ValueError, TypeError) as e:
                        self.logger.error(f"Invalid traffic data: used={used_traffic_raw}, limit={traffic_limit_raw}, error={e}")
                        # Emit safe default values
                        self.traffic_updated.emit(0.0, 0.0)
                        
            except Exception as e:
                self.logger.error("Error in traffic monitor: %s", str(e))
            
            # Sleep for 10 seconds
            for _ in range(10):
                if self.stop_monitor.is_set():
                    break
                time.sleep(1)
    
    def test_connection(self):
        """Test connection to proxy server"""
        try:
            # Get server info
            server_host = self.config.get("server", "host")
            server_port = self.config.get("server", "port")
            
            # Try to connect to server
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_host, server_port))
            sock.close()
            
            if result == 0:
                self.logger.info("Connection test successful")
                return True, "Connection successful"
            else:
                self.logger.error("Connection test failed: %s", os.strerror(result))
                return False, f"Connection failed: {os.strerror(result)}"
        
        except Exception as e:
            self.logger.error("Connection test error: %s", str(e))
            return False, f"Connection test error: {str(e)}"