package com.cocoproxy.client.data.model;

/**
 * Proxy configuration data class
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b2\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u009d\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\b\b\u0002\u0010\n\u001a\u00020\b\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\b\b\u0002\u0010\r\u001a\u00020\b\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u000f\u001a\u00020\b\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0014\u0010\u0015J\t\u0010(\u001a\u00020\u0003H\u00c6\u0003J\t\u0010)\u001a\u00020\u0005H\u00c6\u0003J\t\u0010*\u001a\u00020\u0005H\u00c6\u0003J\t\u0010+\u001a\u00020\bH\u00c6\u0003J\t\u0010,\u001a\u00020\bH\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\u0005H\u00c6\u0003J\t\u0010/\u001a\u00020\u0005H\u00c6\u0003J\t\u00100\u001a\u00020\bH\u00c6\u0003J\t\u00101\u001a\u00020\u0005H\u00c6\u0003J\t\u00102\u001a\u00020\bH\u00c6\u0003J\t\u00103\u001a\u00020\u0005H\u00c6\u0003J\t\u00104\u001a\u00020\bH\u00c6\u0003J\t\u00105\u001a\u00020\u0003H\u00c6\u0003J\t\u00106\u001a\u00020\u0003H\u00c6\u0003J\u009f\u0001\u00107\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\b2\b\b\u0002\u0010\u000e\u001a\u00020\u00052\b\b\u0002\u0010\u000f\u001a\u00020\b2\b\b\u0002\u0010\u0010\u001a\u00020\u00052\b\b\u0002\u0010\u0011\u001a\u00020\b2\b\b\u0002\u0010\u0012\u001a\u00020\u00032\b\b\u0002\u0010\u0013\u001a\u00020\u0003H\u00c6\u0001J\u0006\u00108\u001a\u00020\u0003J\u0013\u00109\u001a\u00020\b2\b\u0010:\u001a\u0004\u0018\u00010;H\u00d6\u0003J\t\u0010<\u001a\u00020\u0003H\u00d6\u0001J\t\u0010=\u001a\u00020\u0005H\u00d6\u0001J\u0016\u0010>\u001a\u00020?2\u0006\u0010@\u001a\u00020A2\u0006\u0010B\u001a\u00020\u0003R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001cR\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001cR\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0011\u0010\r\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001cR\u0011\u0010\u000e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u000f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001cR\u0011\u0010\u0010\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u0019R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u001cR\u0011\u0010\u0012\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u0017R\u0011\u0010\u0013\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010\u0017\u00a8\u0006C"}, d2 = {"Lcom/cocoproxy/client/data/model/ProxyConfig;", "Landroid/os/Parcelable;", "localPort", "", "protocol", "", "encryption", "autoStart", "", "useVpn", "enableAuth", "authUsername", "authPassword", "forwardConnection", "proxyType", "enableDNS", "dnsServer", "enableUDP", "bufferSize", "maxConnections", "<init>", "(ILjava/lang/String;Ljava/lang/String;ZZZLjava/lang/String;Ljava/lang/String;ZLjava/lang/String;ZLjava/lang/String;ZII)V", "getLocalPort", "()I", "getProtocol", "()Ljava/lang/String;", "getEncryption", "getAutoStart", "()Z", "getUseVpn", "getEnableAuth", "getAuthUsername", "getAuthPassword", "getForwardConnection", "getProxyType", "getEnableDNS", "getDnsServer", "getEnableUDP", "getBufferSize", "getMaxConnections", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "component15", "copy", "describeContents", "equals", "other", "", "hashCode", "toString", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
@androidx.compose.runtime.Stable()
public final class ProxyConfig implements android.os.Parcelable {
    private final int localPort = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String protocol = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String encryption = null;
    private final boolean autoStart = false;
    private final boolean useVpn = false;
    private final boolean enableAuth = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String authUsername = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String authPassword = null;
    private final boolean forwardConnection = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String proxyType = null;
    private final boolean enableDNS = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dnsServer = null;
    private final boolean enableUDP = false;
    private final int bufferSize = 0;
    private final int maxConnections = 0;
    
    /**
     * Proxy configuration data class
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * Proxy configuration data class
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public ProxyConfig(int localPort, @org.jetbrains.annotations.NotNull()
    java.lang.String protocol, @org.jetbrains.annotations.NotNull()
    java.lang.String encryption, boolean autoStart, boolean useVpn, boolean enableAuth, @org.jetbrains.annotations.NotNull()
    java.lang.String authUsername, @org.jetbrains.annotations.NotNull()
    java.lang.String authPassword, boolean forwardConnection, @org.jetbrains.annotations.NotNull()
    java.lang.String proxyType, boolean enableDNS, @org.jetbrains.annotations.NotNull()
    java.lang.String dnsServer, boolean enableUDP, int bufferSize, int maxConnections) {
        super();
    }
    
    public final int getLocalPort() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getProtocol() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEncryption() {
        return null;
    }
    
    public final boolean getAutoStart() {
        return false;
    }
    
    public final boolean getUseVpn() {
        return false;
    }
    
    public final boolean getEnableAuth() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAuthUsername() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getAuthPassword() {
        return null;
    }
    
    public final boolean getForwardConnection() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getProxyType() {
        return null;
    }
    
    public final boolean getEnableDNS() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDnsServer() {
        return null;
    }
    
    public final boolean getEnableUDP() {
        return false;
    }
    
    public final int getBufferSize() {
        return 0;
    }
    
    public final int getMaxConnections() {
        return 0;
    }
    
    public ProxyConfig() {
        super();
    }
    
    public final int component1() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component10() {
        return null;
    }
    
    public final boolean component11() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component12() {
        return null;
    }
    
    public final boolean component13() {
        return false;
    }
    
    public final int component14() {
        return 0;
    }
    
    public final int component15() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.ProxyConfig copy(int localPort, @org.jetbrains.annotations.NotNull()
    java.lang.String protocol, @org.jetbrains.annotations.NotNull()
    java.lang.String encryption, boolean autoStart, boolean useVpn, boolean enableAuth, @org.jetbrains.annotations.NotNull()
    java.lang.String authUsername, @org.jetbrains.annotations.NotNull()
    java.lang.String authPassword, boolean forwardConnection, @org.jetbrains.annotations.NotNull()
    java.lang.String proxyType, boolean enableDNS, @org.jetbrains.annotations.NotNull()
    java.lang.String dnsServer, boolean enableUDP, int bufferSize, int maxConnections) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}