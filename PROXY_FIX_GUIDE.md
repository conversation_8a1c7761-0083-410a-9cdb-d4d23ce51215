# CocoProxy 代理连接问题修复指南

## 问题诊断

根据你的日志，问题是：

1. **配置问题**：客户端配置为本地服务器，但实际服务器在 `**************`
2. **连接问题**：代理尝试直接连接目标网站，导致超时

## 解决步骤

### 步骤1：更新客户端配置

运行配置修复脚本：
```bash
cd client_py
python fix_config.py
```

或者手动修改配置：
1. 打开客户端设置 (Settings → Preferences)
2. 在 "Server" 标签页中：
   - Host: `**************`
   - Port: `28888`
   - Admin Port: `28080`
3. 点击 "Apply"

### 步骤2：重启客户端

1. 关闭当前客户端
2. 重新运行：
   ```bash
   cd client_py
   python main.py
   ```

### 步骤3：重新登录

1. 使用相同的凭据登录：
   - 用户名：test
   - 密码：test

### 步骤4：验证连接

登录成功后，检查状态：
- **Connection Status**: Connected ✅
- **Server**: **************:8888 ✅
- **Proxy Status**: Running ✅

## 当前代理限制

⚠️ **重要说明**：当前的代理实现是**直连模式**，它会：

1. 接收客户端的SOCKS5请求
2. **直接连接**目标网站（不通过CocoProxy服务器）
3. 转发数据

这就是为什么你看到 `Failed to connect to www.google.com:443: timed out` 错误的原因。

## 解决方案选项

### 选项1：使用现有直连代理（推荐用于测试）

如果你的网络环境允许直接访问目标网站，可以测试：

```bash
# 测试访问国内网站
curl --socks5 127.0.0.1:1080 http://www.baidu.com

# 测试访问可达的国际网站
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip
```

### 选项2：实现完整的CocoProxy协议转发

需要修改代理实现，使其通过CocoProxy服务器转发流量：

1. **认证阶段**：使用用户凭据连接到CocoProxy服务器
2. **协议转换**：将SOCKS5请求转换为CocoProxy协议
3. **加密传输**：通过加密隧道转发数据

## 临时解决方案

### 方案1：测试可达网站

使用代理访问可以直接连接的网站：

```bash
# 测试代理功能
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip
curl --socks5 127.0.0.1:1080 http://www.baidu.com
```

### 方案2：修改目标网站

在浏览器中通过代理访问：
- http://httpbin.org
- http://www.baidu.com
- 其他可直接访问的网站

## 完整CocoProxy协议实现

如果需要完整的CocoProxy协议支持，需要：

### 1. 协议分析

研究CocoProxy服务器的协议格式：
- 认证机制
- 数据包格式
- 加密方式

### 2. 客户端修改

修改 `simple_proxy.py` 中的 `_connect_direct` 方法：

```python
def _connect_through_cocoproxy(self, target_host, target_port):
    """通过CocoProxy服务器连接目标"""
    # 1. 连接到CocoProxy服务器
    # 2. 发送认证信息
    # 3. 发送目标地址
    # 4. 建立加密隧道
    pass
```

### 3. 协议实现

实现完整的CocoProxy客户端协议：
- 用户认证
- 请求格式
- 数据加密
- 错误处理

## 调试信息

### 检查服务器连接

```bash
# 测试服务器端口
telnet ************** 28888
telnet ************** 28080

# 测试API连接
curl -X POST http://**************:28080/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

### 查看详细日志

客户端日志位置：
- Windows: `%USERPROFILE%\.cocoproxy\logs\cocoproxy.log`
- Linux/macOS: `~/.cocoproxy/logs/cocoproxy.log`

## 下一步计划

1. **立即**：修复配置，测试直连代理功能
2. **短期**：实现基本的CocoProxy协议转发
3. **长期**：完整的协议支持和加密

## 联系支持

如果问题持续存在，请提供：
1. 完整的错误日志
2. 网络环境信息
3. 服务器配置详情
4. 测试结果截图