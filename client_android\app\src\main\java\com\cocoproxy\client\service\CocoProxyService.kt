package com.cocoproxy.client.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
// import com.cocoproxy.client.ui.MainActivity
import com.cocoproxy.client.R
import com.cocoproxy.client.core.CocoProxyClient
import com.cocoproxy.client.data.model.*
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.ServerSocket
import java.net.Socket
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject

@AndroidEntryPoint
class CocoProxyService : Service() {
    
    companion object {
        private const val NOTIFICATION_ID = 1002
        private const val CHANNEL_ID = "proxy_service_channel"
        
        const val ACTION_START_PROXY = "com.cocoproxy.client.START_PROXY"
        const val ACTION_STOP_PROXY = "com.cocoproxy.client.STOP_PROXY"
    }
    
    @Inject
    lateinit var cocoProxyClient: CocoProxyClient
    
    private val binder = ProxyServiceBinder()
    private var serverSocket: ServerSocket? = null
    private var serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private val _isRunning = MutableStateFlow(false)
    val isRunning: StateFlow<Boolean> = _isRunning.asStateFlow()
    
    private val _trafficData = MutableStateFlow(TrafficData())
    val trafficData: StateFlow<TrafficData> = _trafficData.asStateFlow()
    
    private val _connectionStatus = MutableStateFlow(CocoProxyClient.ConnectionState.DISCONNECTED)
    val connectionStatus: StateFlow<CocoProxyClient.ConnectionState> = _connectionStatus.asStateFlow()
    
    private val activeConnections = ConcurrentHashMap<String, ProxyConnection>()
    private val bytesUploaded = AtomicLong(0)
    private val bytesDownloaded = AtomicLong(0)
    private val totalConnections = AtomicLong(0)
    
    private var proxyConfig = ProxyConfig()
    private var serverConfig = ServerConfig()
    
    inner class ProxyServiceBinder : Binder() {
        fun getService(): CocoProxyService = this@CocoProxyService
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        
        // Observe CocoProxy client status
        serviceScope.launch {
            cocoProxyClient.connectionState.collect { status ->
                _connectionStatus.value = status
            }
        }
        
        serviceScope.launch {
            cocoProxyClient.trafficData.collect { traffic ->
                updateTrafficData(traffic)
            }
        }
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_PROXY -> {
                val config = intent.getParcelableExtra<ProxyConfig>("proxy_config")
                val server = intent.getParcelableExtra<ServerConfig>("server_config")
                if (config != null && server != null) {
                    startProxy(config, server)
                }
            }
            ACTION_STOP_PROXY -> {
                stopProxy()
            }
        }
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopProxy()
        serviceScope.cancel()
    }
    
    fun startProxy(proxyConfig: ProxyConfig, serverConfig: ServerConfig) {
        if (_isRunning.value) return
        
        this.proxyConfig = proxyConfig
        this.serverConfig = serverConfig
        
        // Start foreground service immediately to avoid crash
        startForeground(NOTIFICATION_ID, createNotification())
        
        serviceScope.launch {
            try {
                // Start CocoProxy client connection
                cocoProxyClient.connect(serverConfig, proxyConfig)
                
                // Start local SOCKS5 server
                startSocksServer()
                
                _isRunning.value = true
                
            } catch (e: Exception) {
                stopProxy()
                throw e
            }
        }
    }
    
    fun stopProxy() {
        if (!_isRunning.value) return
        
        _isRunning.value = false
        
        // Close all active connections
        activeConnections.values.forEach { connection ->
            connection.close()
        }
        activeConnections.clear()
        
        // Stop SOCKS server
        serverSocket?.close()
        serverSocket = null
        
        // Stop CocoProxy client
        serviceScope.launch {
            cocoProxyClient.disconnect()
        }
        
        serviceScope.coroutineContext.cancelChildren()
        stopForeground(STOP_FOREGROUND_REMOVE)
        stopSelf()
    }
    
    private suspend fun startSocksServer() = withContext(Dispatchers.IO) {
        try {
            serverSocket = ServerSocket(proxyConfig.localPort)
            
            while (_isRunning.value && !currentCoroutineContext().isActive.not()) {
                try {
                    val clientSocket = serverSocket?.accept() ?: break
                    
                    // Handle client connection in separate coroutine
                    launch {
                        handleClientConnection(clientSocket)
                    }
                } catch (e: IOException) {
                    if (_isRunning.value) {
                        // Log error
                        break
                    }
                }
            }
        } catch (e: Exception) {
            if (_isRunning.value) {
                stopProxy()
            }
        }
    }
    
    private suspend fun handleClientConnection(clientSocket: Socket) = withContext(Dispatchers.IO) {
        val connectionId = "${clientSocket.remoteSocketAddress}_${System.currentTimeMillis()}"
        val connection = ProxyConnection(connectionId, clientSocket)
        
        try {
            activeConnections[connectionId] = connection
            totalConnections.incrementAndGet()
            updateTrafficStats()
            
            // Handle SOCKS5 protocol
            when (proxyConfig.protocol) {
                "SOCKS5" -> handleSocks5Connection(connection)
                "HTTP" -> handleHttpConnection(connection)
                "HTTPS" -> handleHttpsConnection(connection)
                else -> throw IllegalArgumentException("Unsupported proxy type: ${proxyConfig.protocol}")
            }
            
        } catch (e: Exception) {
            // Log connection error
        } finally {
            activeConnections.remove(connectionId)
            connection.close()
            updateTrafficStats()
        }
    }
    
    private suspend fun handleSocks5Connection(connection: ProxyConnection) = withContext(Dispatchers.IO) {
        val input = connection.socket.getInputStream()
        val output = connection.socket.getOutputStream()
        
        try {
            // SOCKS5 handshake
            val handshake = ByteArray(2)
            input.read(handshake)
            
            if (handshake[0] != 0x05.toByte()) {
                throw IOException("Invalid SOCKS version")
            }
            
            val methodCount = handshake[1].toInt() and 0xFF
            val methods = ByteArray(methodCount)
            input.read(methods)
            
            // Send method selection response
            if (proxyConfig.enableAuth) {
                output.write(byteArrayOf(0x05, 0x02)) // Username/password auth
                
                // Handle authentication
                if (!handleSocks5Auth(input, output)) {
                    return@withContext
                }
            } else {
                output.write(byteArrayOf(0x05, 0x00)) // No authentication
            }
            
            // Handle connection request
            val request = ByteArray(4)
            input.read(request)
            
            if (request[0] != 0x05.toByte() || request[1] != 0x01.toByte()) {
                throw IOException("Invalid SOCKS request")
            }
            
            val addressType = request[3].toInt() and 0xFF
            val targetHost: String
            val targetPort: Int
            
            when (addressType) {
                0x01 -> { // IPv4
                    val address = ByteArray(4)
                    input.read(address)
                    targetHost = address.joinToString(".") { (it.toInt() and 0xFF).toString() }
                }
                0x03 -> { // Domain name
                    val domainLength = input.read()
                    val domain = ByteArray(domainLength)
                    input.read(domain)
                    targetHost = String(domain)
                }
                else -> {
                    throw IOException("Unsupported address type: $addressType")
                }
            }
            
            val portBytes = ByteArray(2)
            input.read(portBytes)
            targetPort = ((portBytes[0].toInt() and 0xFF) shl 8) or (portBytes[1].toInt() and 0xFF)
            
            // Send success response
            output.write(byteArrayOf(
                0x05, 0x00, 0x00, 0x01, // SOCKS5, success, reserved, IPv4
                0x00, 0x00, 0x00, 0x00, // Bind address (0.0.0.0)
                0x00, 0x00 // Bind port (0)
            ))
            
            // Forward data through CocoProxy
            forwardData(connection, targetHost, targetPort)
            
        } catch (e: Exception) {
            // Send error response
            try {
                output.write(byteArrayOf(0x05, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00))
            } catch (ignored: Exception) {}
        }
    }
    
    private suspend fun handleSocks5Auth(input: java.io.InputStream, output: java.io.OutputStream): Boolean {
        try {
            val authRequest = ByteArray(2)
            input.read(authRequest)
            
            if (authRequest[0] != 0x01.toByte()) {
                output.write(byteArrayOf(0x01, 0x01)) // Auth failed
                return false
            }
            
            val usernameLength = authRequest[1].toInt() and 0xFF
            val username = ByteArray(usernameLength)
            input.read(username)
            
            val passwordLength = input.read()
            val password = ByteArray(passwordLength)
            input.read(password)
            
            val usernameStr = String(username)
            val passwordStr = String(password)
            
            if (usernameStr == proxyConfig.authUsername && passwordStr == proxyConfig.authPassword) {
                output.write(byteArrayOf(0x01, 0x00)) // Auth success
                return true
            } else {
                output.write(byteArrayOf(0x01, 0x01)) // Auth failed
                return false
            }
        } catch (e: Exception) {
            return false
        }
    }
    
    private suspend fun handleHttpConnection(connection: ProxyConnection) {
        // Implement HTTP proxy protocol
        // This is a simplified placeholder
    }
    
    private suspend fun handleHttpsConnection(connection: ProxyConnection) {
        // Implement HTTPS proxy protocol (CONNECT method)
        // This is a simplified placeholder
    }
    
    private suspend fun forwardData(connection: ProxyConnection, targetHost: String, targetPort: Int) {
        // Forward data through CocoProxy client
        withContext(Dispatchers.IO) {
            var proxySocket: Socket? = null
            try {
                Log.d("CocoProxyService", "Starting data forwarding for $targetHost:$targetPort")
                
                // Create proxy connection through CocoProxy server
                proxySocket = cocoProxyClient.createProxyConnection(targetHost, targetPort)
                if (proxySocket == null) {
                    Log.e("CocoProxyService", "Failed to create proxy connection to $targetHost:$targetPort")
                    return@withContext
                }
                
                val clientSocket = connection.socket
                val clientInput = clientSocket.getInputStream()
                val clientOutput = clientSocket.getOutputStream()
                val proxyInput = proxySocket.getInputStream()
                val proxyOutput = proxySocket.getOutputStream()
                
                Log.d("CocoProxyService", "Proxy connection established, starting bidirectional forwarding")
                
                // Start bidirectional data forwarding
                val clientToProxy = async {
                    forwardDataStream(
                        clientInput, 
                        proxyOutput, 
                        "client->proxy",
                        targetHost,
                        targetPort
                    )
                }
                
                val proxyToClient = async {
                    forwardDataStream(
                        proxyInput, 
                        clientOutput, 
                        "proxy->client",
                        targetHost,
                        targetPort
                    )
                }
                
                // Wait for either direction to complete (connection closed)
                try {
                    awaitAll(clientToProxy, proxyToClient)
                } catch (e: Exception) {
                    Log.d("CocoProxyService", "Data forwarding completed for $targetHost:$targetPort: ${e.message}")
                }
                
                Log.d("CocoProxyService", "Data forwarding ended for $targetHost:$targetPort")
                
            } catch (e: Exception) {
                Log.e("CocoProxyService", "Error in data forwarding for $targetHost:$targetPort: ${e.message}", e)
            } finally {
                // Clean up connections
                try {
                    proxySocket?.close()
                } catch (e: Exception) {
                    Log.w("CocoProxyService", "Error closing proxy socket: ${e.message}")
                }
            }
        }
    }
    
    /**
     * Forward data from source to destination stream
     */
    private suspend fun forwardDataStream(
        source: InputStream,
        destination: OutputStream,
        direction: String,
        targetHost: String,
        targetPort: Int
    ) = withContext(Dispatchers.IO) {
        try {
            val buffer = ByteArray(8192)
            var totalBytes = 0
            
            while (true) {
                val bytesRead = source.read(buffer)
                if (bytesRead == -1) {
                    Log.d("CocoProxyService", "Stream closed ($direction) for $targetHost:$targetPort, total: $totalBytes bytes")
                    break
                }
                
                if (bytesRead > 0) {
                    destination.write(buffer, 0, bytesRead)
                    destination.flush()
                    totalBytes += bytesRead
                    
                    // Update traffic statistics
                    if (direction.contains("client->proxy")) {
                        bytesUploaded.addAndGet(bytesRead.toLong())
                    } else {
                        bytesDownloaded.addAndGet(bytesRead.toLong())
                    }
                    
                    // Update traffic data
                    updateTrafficStats()
                    
                    Log.v("CocoProxyService", "Forwarded $bytesRead bytes ($direction) for $targetHost:$targetPort")
                }
            }
            
        } catch (e: Exception) {
            Log.d("CocoProxyService", "Stream forwarding error ($direction) for $targetHost:$targetPort: ${e.message}")
        } finally {
            try {
                // Close streams gracefully
                source.close()
                destination.close()
            } catch (e: Exception) {
                // Ignore close errors
            }
        }
    }
    
    private fun updateTrafficData(traffic: TrafficData) {
        _trafficData.value = traffic.copy(
            activeConnections = activeConnections.size,
            totalConnections = totalConnections.get()
        )
    }
    
    private fun updateTrafficStats() {
        val currentTraffic = _trafficData.value
        _trafficData.value = currentTraffic.copy(
            bytesUploaded = bytesUploaded.get(),
            bytesDownloaded = bytesDownloaded.get(),
            activeConnections = activeConnections.size,
            totalConnections = totalConnections.get()
        )
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Proxy Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "CocoProxy Service"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(): Notification {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val stopIntent = Intent(this, CocoProxyService::class.java).apply {
            action = ACTION_STOP_PROXY
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("CocoProxy")
            .setContentText("Proxy service is running")
            .setSmallIcon(R.drawable.ic_proxy)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_stop,
                "Stop",
                stopPendingIntent
            )
            .setOngoing(true)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    private data class ProxyConnection(
        val id: String,
        val socket: Socket
    ) {
        fun close() {
            try {
                socket.close()
            } catch (e: Exception) {
                // Ignore
            }
        }
    }
}