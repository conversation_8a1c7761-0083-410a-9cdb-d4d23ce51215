package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.ui.viewmodel.AppSplitViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSplitMainScreen(
    onNavigateBack: () -> Unit,
    onNavigateToModeConfig: () -> Unit,
    onNavigateToAppSelection: () -> Unit,
    onNavigateToTrafficStats: () -> Unit,
    viewModel: AppSplitViewModel = hiltViewModel()
) {
    val splitConfig by viewModel.splitConfig.collectAsStateWithLifecycle()
    val selectedAppsCount by viewModel.selectedAppsCount.collectAsStateWithLifecycle()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("应用分流") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 当前配置状态卡片
            item {
                CurrentConfigCard(
                    splitMode = splitConfig.mode,
                    selectedAppsCount = selectedAppsCount,
                    isEnabled = splitConfig.enabled
                )
            }
            
            // 功能操作列表
            item {
                Text(
                    "配置选项",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            item {
                AppSplitActionCard(
                    title = "分流模式",
                    description = getSplitModeDescription(splitConfig.mode),
                    icon = Icons.Filled.FilterList,
                    onClick = onNavigateToModeConfig
                )
            }
            
            item {
                AppSplitActionCard(
                    title = "选择应用",
                    description = if (splitConfig.mode == SplitMode.DISABLED) {
                        "请先选择分流模式"
                    } else {
                        "已选择 $selectedAppsCount 个应用"
                    },
                    icon = Icons.Filled.Apps,
                    enabled = true, // 始终启用，但行为不同
                    onClick = {
                        if (splitConfig.mode != SplitMode.DISABLED) {
                            onNavigateToAppSelection()
                        } else {
                            // 引导用户去配置分流模式
                            onNavigateToModeConfig()
                        }
                    }
                )
            }
            
            item {
                AppSplitActionCard(
                    title = "流量统计",
                    description = "查看各应用的流量使用情况",
                    icon = Icons.Filled.Analytics,
                    onClick = onNavigateToTrafficStats
                )
            }
            
            // 快速操作
            item {
                Text(
                    "快速操作",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }
            
            item {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    OutlinedButton(
                        onClick = { viewModel.enableSplitMode() },
                        modifier = Modifier.weight(1f),
                        enabled = !splitConfig.enabled
                    ) {
                        Text("启用分流")
                    }
                    
                    OutlinedButton(
                        onClick = { viewModel.disableSplitMode() },
                        modifier = Modifier.weight(1f),
                        enabled = splitConfig.enabled
                    ) {
                        Text("禁用分流")
                    }
                }
            }
        }
    }
}

@Composable
private fun CurrentConfigCard(
    splitMode: SplitMode,
    selectedAppsCount: Int,
    isEnabled: Boolean
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isEnabled) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Icon(
                    if (isEnabled) Icons.Filled.CheckCircle else Icons.Filled.Cancel,
                    contentDescription = null,
                    tint = if (isEnabled) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                Text(
                    "当前状态",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            }
            
            Text(
                "分流模式: ${getSplitModeDisplayName(splitMode)}",
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (splitMode != SplitMode.DISABLED) {
                Text(
                    "已选择应用: $selectedAppsCount 个",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
            
            Text(
                "状态: ${if (isEnabled) "已启用" else "已禁用"}",
                style = MaterialTheme.typography.bodyMedium,
                color = if (isEnabled) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                }
            )
        }
    }
}

@Composable
private fun AppSplitActionCard(
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = if (enabled) {
                MaterialTheme.colorScheme.surface
            } else {
                MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                icon,
                contentDescription = null,
                tint = if (enabled) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier.size(24.dp)
            )
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (enabled) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurfaceVariant
                    }
                )
                Text(
                    description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            if (enabled) {
                Icon(
                    Icons.Filled.ChevronRight,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

private fun getSplitModeDisplayName(splitMode: SplitMode): String {
    return when (splitMode) {
        SplitMode.DISABLED -> "禁用分流"
        SplitMode.WHITELIST -> "白名单模式"
        SplitMode.BLACKLIST -> "黑名单模式"
    }
}

private fun getSplitModeDescription(splitMode: SplitMode): String {
    return when (splitMode) {
        SplitMode.DISABLED -> "所有应用都通过代理"
        SplitMode.WHITELIST -> "仅选中的应用通过代理"
        SplitMode.BLACKLIST -> "除选中应用外都通过代理"
    }
}