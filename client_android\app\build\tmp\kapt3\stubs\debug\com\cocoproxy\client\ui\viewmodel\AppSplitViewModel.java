package com.cocoproxy.client.ui.viewmodel;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\b\u0010&\u001a\u00020\'H\u0002J\u0010\u0010(\u001a\u00020\'2\b\b\u0002\u0010)\u001a\u00020\u001cJ\u000e\u0010*\u001a\u00020\'2\u0006\u0010+\u001a\u00020,J\u0006\u0010-\u001a\u00020\'J\u0006\u0010.\u001a\u00020\'J\u000e\u0010/\u001a\u00020\'2\u0006\u00100\u001a\u00020\u0018J\u0006\u00101\u001a\u00020\'J\u0006\u00102\u001a\u00020\'J\u000e\u00103\u001a\u00020\'2\u0006\u00104\u001a\u000205J\u0014\u00106\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u00107\u001a\u00020\u0018J\u0006\u00108\u001a\u00020\'J\u0006\u00109\u001a\u00020\'J\u0006\u0010:\u001a\u00020\'J\u0012\u0010;\u001a\u000e\u0012\u0004\u0012\u000205\u0012\u0004\u0012\u00020\"0<J\u0014\u0010=\u001a\u00020\'2\f\u0010>\u001a\b\u0012\u0004\u0012\u00020\u00180\u0012J\f\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00180\u0012R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\f0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001a\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0010R\u001a\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00170\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00180\u00170\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0010R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001c0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0010R\u0016\u0010\u001e\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00180\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00180\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0010R\u0017\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0010R\u001d\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0010\u00a8\u0006@"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/AppSplitViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "configRepository", "Lcom/cocoproxy/client/data/repository/ConfigRepository;", "appManager", "Lcom/cocoproxy/client/core/AppManager;", "<init>", "(Landroid/app/Application;Lcom/cocoproxy/client/data/repository/ConfigRepository;Lcom/cocoproxy/client/core/AppManager;)V", "_splitConfig", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/cocoproxy/client/core/AppSplitConfig;", "splitConfig", "Lkotlinx/coroutines/flow/StateFlow;", "getSplitConfig", "()Lkotlinx/coroutines/flow/StateFlow;", "_allApps", "", "Lcom/cocoproxy/client/data/model/InstalledApp;", "allApps", "getAllApps", "_selectedApps", "", "", "selectedApps", "getSelectedApps", "_isLoading", "", "isLoading", "_errorMessage", "errorMessage", "getErrorMessage", "selectedAppsCount", "", "getSelectedAppsCount", "filteredApps", "getFilteredApps", "loadConfiguration", "", "loadInstalledApps", "includeSystemApps", "setSplitMode", "mode", "Lcom/cocoproxy/client/core/SplitMode;", "enableSplitMode", "disableSplitMode", "toggleAppSelection", "packageName", "selectAllApps", "deselectAllApps", "selectAppsByCategory", "category", "Lcom/cocoproxy/client/data/model/AppCategory;", "searchApps", "query", "saveConfiguration", "resetConfiguration", "clearErrorMessage", "getAppCategoryStats", "", "importAppList", "packageNames", "exportAppList", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class AppSplitViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.repository.ConfigRepository configRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.AppManager appManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.core.AppSplitConfig> _splitConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.AppSplitConfig> splitConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> _allApps = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> allApps = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.Set<java.lang.String>> _selectedApps = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.Set<java.lang.String>> selectedApps = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> selectedAppsCount = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> filteredApps = null;
    
    @javax.inject.Inject()
    public AppSplitViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.repository.ConfigRepository configRepository, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.AppManager appManager) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.AppSplitConfig> getSplitConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> getAllApps() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.Set<java.lang.String>> getSelectedApps() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getSelectedAppsCount() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> getFilteredApps() {
        return null;
    }
    
    /**
     * 加载当前配置
     */
    private final void loadConfiguration() {
    }
    
    /**
     * 加载已安装应用
     */
    public final void loadInstalledApps(boolean includeSystemApps) {
    }
    
    /**
     * 设置分流模式
     */
    public final void setSplitMode(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.SplitMode mode) {
    }
    
    /**
     * 启用分流模式
     */
    public final void enableSplitMode() {
    }
    
    /**
     * 禁用分流模式
     */
    public final void disableSplitMode() {
    }
    
    /**
     * 切换应用选择状态
     */
    public final void toggleAppSelection(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName) {
    }
    
    /**
     * 选择所有应用
     */
    public final void selectAllApps() {
    }
    
    /**
     * 取消选择所有应用
     */
    public final void deselectAllApps() {
    }
    
    /**
     * 根据类别选择应用
     */
    public final void selectAppsByCategory(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.AppCategory category) {
    }
    
    /**
     * 搜索应用
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.InstalledApp> searchApps(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
    
    /**
     * 保存配置
     */
    public final void saveConfiguration() {
    }
    
    /**
     * 重置配置
     */
    public final void resetConfiguration() {
    }
    
    /**
     * 清除错误消息
     */
    public final void clearErrorMessage() {
    }
    
    /**
     * 获取应用统计信息
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<com.cocoproxy.client.data.model.AppCategory, java.lang.Integer> getAppCategoryStats() {
        return null;
    }
    
    /**
     * 导入应用列表
     */
    public final void importAppList(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> packageNames) {
    }
    
    /**
     * 导出应用列表
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> exportAppList() {
        return null;
    }
}