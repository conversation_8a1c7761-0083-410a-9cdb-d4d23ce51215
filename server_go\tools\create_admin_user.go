package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"

	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: create_admin_user <username> <password>")
		fmt.Println("Example: create_admin_user test test")
		os.Exit(1)
	}

	username := os.Args[1]
	password := os.Args[2]

	// 打开数据库
	db, err := sql.Open("sqlite3", "../coco.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}
	defer db.Close()

	// 检查用户是否已存在
	var existingID int
	err = db.QueryRow("SELECT id FROM users WHERE username = ?", username).Scan(&existingID)
	if err == nil {
		// 用户已存在，更新为管理员
		_, err = db.Exec("UPDATE users SET is_admin = 1 WHERE username = ?", username)
		if err != nil {
			log.Fatal("Failed to update user to admin:", err)
		}
		fmt.Printf("User '%s' has been updated to admin status.\n", username)
	} else if err == sql.ErrNoRows {
		// 用户不存在，创建新的管理员用户
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if err != nil {
			log.Fatal("Failed to hash password:", err)
		}

		stmt, err := db.Prepare("INSERT INTO users(username, password_hash, encrypt_key, traffic_limit, is_admin) VALUES(?, ?, ?, ?, ?)")
		if err != nil {
			log.Fatal("Failed to prepare statement:", err)
		}
		defer stmt.Close()

		_, err = stmt.Exec(username, string(hashedPassword), "your-secret-key-16", 0, true)
		if err != nil {
			log.Fatal("Failed to create admin user:", err)
		}

		fmt.Printf("Admin user '%s' created successfully!\n", username)
	} else {
		log.Fatal("Database error:", err)
	}

	// 验证创建的用户
	row := db.QueryRow("SELECT id, username, is_admin FROM users WHERE username = ?", username)
	var id int
	var dbUsername string
	var isAdmin bool

	err = row.Scan(&id, &dbUsername, &isAdmin)
	if err != nil {
		log.Fatal("Failed to verify admin user:", err)
	}

	fmt.Printf("Verification: ID=%d, Username=%s, IsAdmin=%t\n", id, dbUsername, isAdmin)

	// 测试密码验证
	row = db.QueryRow("SELECT password_hash FROM users WHERE username = ?", username)
	var passwordHash string
	err = row.Scan(&passwordHash)
	if err != nil {
		log.Fatal("Failed to get password hash:", err)
	}

	err = bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(password))
	if err != nil {
		fmt.Printf("Password verification FAILED: %v\n", err)
	} else {
		fmt.Println("Password verification SUCCESS!")
	}

	fmt.Printf("\nYou can now login to the client with:\n")
	fmt.Printf("Username: %s\n", username)
	fmt.Printf("Password: %s\n", password)
}