package com.cocoproxy.client.ui.screen;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\u001aL\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\u000e\b\u0002\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a\u0018\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\rH\u0007\u001a\'\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0012\u001a\u00020\u0013H\u0007\u00a2\u0006\u0004\b\u0014\u0010\u0015\u001a\u0016\u0010\u0016\u001a\u00020\u00102\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\r\u001a\u001d\u0010\u0018\u001a\u00020\u00132\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\u0017\u001a\u00020\rH\u0007\u00a2\u0006\u0002\u0010\u0019\u001a\u000e\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\n\u001a\u00020\u000b\u00a8\u0006\u001c"}, d2 = {"HomeScreen", "", "onNavigateToServerConfig", "Lkotlin/Function0;", "onNavigateToProxyConfig", "onVpnPermissionRequest", "onNavigateToLogin", "viewModel", "Lcom/cocoproxy/client/ui/viewmodel/MainViewModel;", "ConnectionStatusCircle", "connectionState", "Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;", "isLoading", "", "TrafficItem", "label", "", "value", "color", "Landroidx/compose/ui/graphics/Color;", "TrafficItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "getStatusText", "isRunning", "getStatusColor", "(Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;Z)J", "getStatusIcon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "app_debug"})
public final class HomeScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void HomeScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToServerConfig, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToProxyConfig, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onVpnPermissionRequest, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToLogin, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.viewmodel.MainViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ConnectionStatusCircle(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient.ConnectionState connectionState, boolean isLoading) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String getStatusText(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient.ConnectionState connectionState, boolean isRunning) {
        return null;
    }
    
    @androidx.compose.runtime.Composable()
    public static final long getStatusColor(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient.ConnectionState connectionState, boolean isRunning) {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final androidx.compose.ui.graphics.vector.ImageVector getStatusIcon(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient.ConnectionState connectionState) {
        return null;
    }
}