#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox, QComboBox,
    QSpinBox, QGroupBox, QTabWidget, QWidget, QMessageBox, QFormLayout
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIntValidator

class SettingsDialog(QDialog):
    """Settings dialog for CocoProxy client"""
    
    # Signal emitted when settings are applied and proxy needs restart
    settings_applied = Signal()
    
    def __init__(self, config, parent=None):
        """Initialize settings dialog"""
        super().__init__(parent)
        self.config = config
        
        # Dialog properties
        self.setWindowTitle("CocoProxy Settings")
        self.setModal(True)
        self.setFixedSize(500, 400)
        
        # Setup UI
        self.setup_ui()
        
        # Load current settings
        self.load_settings()
    
    def setup_ui(self):
        """Setup user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Tab widget
        self.tab_widget = QTabWidget()
        
        # Server settings tab
        self.setup_server_tab()
        
        # Proxy settings tab
        self.setup_proxy_tab()
        
        # UI settings tab
        self.setup_ui_tab()
        
        main_layout.addWidget(self.tab_widget)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Reset button
        self.reset_button = QPushButton("Reset to Defaults")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # Apply button
        self.apply_button = QPushButton("Apply")
        self.apply_button.clicked.connect(self.apply_settings)
        button_layout.addWidget(self.apply_button)
        
        # OK button
        self.ok_button = QPushButton("OK")
        self.ok_button.setDefault(True)
        self.ok_button.clicked.connect(self.accept_settings)
        button_layout.addWidget(self.ok_button)
        
        main_layout.addLayout(button_layout)
    
    def setup_server_tab(self):
        """Setup server settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Server connection group
        server_group = QGroupBox("Server Connection")
        server_layout = QGridLayout(server_group)
        
        # Server host
        server_layout.addWidget(QLabel("Host:"), 0, 0)
        self.host_edit = QLineEdit()
        self.host_edit.setPlaceholderText("127.0.0.1")
        server_layout.addWidget(self.host_edit, 0, 1)
        
        # Server port
        server_layout.addWidget(QLabel("Port:"), 1, 0)
        self.port_spin = QSpinBox()
        self.port_spin.setRange(1, 65535)
        self.port_spin.setValue(8888)
        server_layout.addWidget(self.port_spin, 1, 1)
        
        # Admin port
        server_layout.addWidget(QLabel("Admin Port:"), 2, 0)
        self.admin_port_spin = QSpinBox()
        self.admin_port_spin.setRange(1, 65535)
        self.admin_port_spin.setValue(28080)
        server_layout.addWidget(self.admin_port_spin, 2, 1)
        
        layout.addWidget(server_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Server")
    
    def setup_proxy_tab(self):
        """Setup proxy settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Proxy configuration group
        proxy_group = QGroupBox("Proxy Configuration")
        proxy_layout = QGridLayout(proxy_group)
        
        # Local port
        proxy_layout.addWidget(QLabel("Local Port:"), 0, 0)
        self.local_port_spin = QSpinBox()
        self.local_port_spin.setRange(1, 65535)
        self.local_port_spin.setValue(1080)
        proxy_layout.addWidget(self.local_port_spin, 0, 1)
        
        # Protocol
        proxy_layout.addWidget(QLabel("Protocol:"), 1, 0)
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["SOCKS5", "HTTP"])
        proxy_layout.addWidget(self.protocol_combo, 1, 1)
        
        # Encryption
        proxy_layout.addWidget(QLabel("Encryption:"), 2, 0)
        self.encryption_combo = QComboBox()
        self.encryption_combo.addItems(["None", "AES", "ChaCha20"])
        proxy_layout.addWidget(self.encryption_combo, 2, 1)
        
        layout.addWidget(proxy_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Proxy")
    
    def setup_ui_tab(self):
        """Setup UI settings tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Appearance group
        appearance_group = QGroupBox("Appearance")
        appearance_layout = QGridLayout(appearance_group)
        
        # Theme
        appearance_layout.addWidget(QLabel("Theme:"), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["System", "Light", "Dark"])
        appearance_layout.addWidget(self.theme_combo, 0, 1)
        
        # Language
        appearance_layout.addWidget(QLabel("Language:"), 1, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["English", "中文"])
        appearance_layout.addWidget(self.language_combo, 1, 1)
        
        layout.addWidget(appearance_group)
        
        # Behavior group
        behavior_group = QGroupBox("Behavior")
        behavior_layout = QVBoxLayout(behavior_group)
        
        # Minimize to tray
        self.minimize_tray_check = QCheckBox("Minimize to system tray")
        behavior_layout.addWidget(self.minimize_tray_check)
        
        # Start on boot
        self.start_boot_check = QCheckBox("Start on system boot")
        behavior_layout.addWidget(self.start_boot_check)
        
        # Check updates
        self.check_updates_check = QCheckBox("Check for updates automatically")
        behavior_layout.addWidget(self.check_updates_check)
        
        layout.addWidget(behavior_group)
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "UI")
    
    def load_settings(self):
        """Load current settings from config"""
        # Server settings
        self.host_edit.setText(self.config.get("server", "host"))
        self.port_spin.setValue(int(self.config.get("server", "port")))
        self.admin_port_spin.setValue(int(self.config.get("server", "admin_port")))
        
        # Proxy settings
        self.local_port_spin.setValue(int(self.config.get("proxy", "local_port")))
        
        protocol = self.config.get("proxy", "protocol").lower()
        if protocol == "socks5":
            self.protocol_combo.setCurrentIndex(0)
        elif protocol == "http":
            self.protocol_combo.setCurrentIndex(1)
        
        encryption = self.config.get("proxy", "encryption").lower()
        if encryption == "none":
            self.encryption_combo.setCurrentIndex(0)
        elif encryption == "aes":
            self.encryption_combo.setCurrentIndex(1)
        elif encryption == "chacha20":
            self.encryption_combo.setCurrentIndex(2)
        
        # UI settings
        theme = self.config.get("ui", "theme").lower()
        if theme == "system":
            self.theme_combo.setCurrentIndex(0)
        elif theme == "light":
            self.theme_combo.setCurrentIndex(1)
        elif theme == "dark":
            self.theme_combo.setCurrentIndex(2)
        
        language = self.config.get("ui", "language").lower()
        if language == "en":
            self.language_combo.setCurrentIndex(0)
        elif language == "zh":
            self.language_combo.setCurrentIndex(1)
        
        self.minimize_tray_check.setChecked(self.config.get("ui", "minimize_to_tray"))
        self.start_boot_check.setChecked(self.config.get("ui", "start_on_boot"))
        self.check_updates_check.setChecked(self.config.get("ui", "check_updates"))
    
    def apply_settings(self):
        """Apply current settings"""
        # Validate input
        if not self.validate_input():
            return
        
        # Prepare all settings for batch update
        protocol_map = {0: "socks5", 1: "http"}
        encryption_map = {0: "none", 1: "aes", 2: "chacha20"}
        theme_map = {0: "system", 1: "light", 2: "dark"}
        language_map = {0: "en", 1: "zh"}
        
        settings_batch = {
            "server": {
                "host": self.host_edit.text().strip(),
                "port": self.port_spin.value(),
                "admin_port": self.admin_port_spin.value()
            },
            "proxy": {
                "local_port": self.local_port_spin.value(),
                "protocol": protocol_map[self.protocol_combo.currentIndex()],
                "encryption": encryption_map[self.encryption_combo.currentIndex()]
            },
            "ui": {
                "theme": theme_map[self.theme_combo.currentIndex()],
                "language": language_map[self.language_combo.currentIndex()],
                "minimize_to_tray": self.minimize_tray_check.isChecked(),
                "start_on_boot": self.start_boot_check.isChecked(),
                "check_updates": self.check_updates_check.isChecked()
            }
        }
        
        # Apply all settings in one batch operation (single file I/O)
        self.config.set_batch(settings_batch)
        
        # Emit signal to notify that settings have been applied
        self.settings_applied.emit()
        
        # Use QTimer to show message after settings are applied to avoid blocking
        from PySide6.QtCore import QTimer
        QTimer.singleShot(200, lambda: QMessageBox.information(self, "Settings", "Settings applied successfully!"))
    
    def accept_settings(self):
        """Accept and apply settings"""
        self.apply_settings()
        self.accept()
    
    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        reply = QMessageBox.question(
            self, "Reset Settings",
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.config.reset()
            self.load_settings()
            QMessageBox.information(self, "Settings", "Settings reset to defaults!")
    
    def validate_input(self):
        """Validate user input"""
        # Check host
        host = self.host_edit.text().strip()
        if not host:
            QMessageBox.warning(self, "Invalid Input", "Please enter a server host.")
            self.tab_widget.setCurrentIndex(0)
            self.host_edit.setFocus()
            return False
        
        # Check port conflicts
        server_port = self.port_spin.value()
        admin_port = self.admin_port_spin.value()
        local_port = self.local_port_spin.value()
        
        if server_port == admin_port:
            QMessageBox.warning(self, "Invalid Input", "Server port and admin port cannot be the same.")
            self.tab_widget.setCurrentIndex(0)
            self.admin_port_spin.setFocus()
            return False
        
        if local_port == server_port or local_port == admin_port:
            QMessageBox.warning(self, "Invalid Input", "Local port conflicts with server ports.")
            self.tab_widget.setCurrentIndex(1)
            self.local_port_spin.setFocus()
            return False
        
        return True