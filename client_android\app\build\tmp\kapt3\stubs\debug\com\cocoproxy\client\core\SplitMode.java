package com.cocoproxy.client.core;

/**
 * Split mode enumeration for app split tunneling
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/cocoproxy/client/core/SplitMode;", "", "<init>", "(Ljava/lang/String;I)V", "DISABLED", "WHITELIST", "BLACKLIST", "app_debug"})
public enum SplitMode {
    /*public static final*/ DISABLED /* = new DISABLED() */,
    /*public static final*/ WHITELIST /* = new WHITELIST() */,
    /*public static final*/ BLACKLIST /* = new BLACKLIST() */;
    
    SplitMode() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.cocoproxy.client.core.SplitMode> getEntries() {
        return null;
    }
}