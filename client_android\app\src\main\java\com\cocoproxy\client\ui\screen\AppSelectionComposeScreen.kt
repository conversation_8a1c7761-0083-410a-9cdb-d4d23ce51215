package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.data.model.AppCategory
import com.cocoproxy.client.data.model.InstalledApp
import com.cocoproxy.client.ui.viewmodel.AppSplitViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppSelectionComposeScreen(
    onNavigateBack: () -> Unit,
    viewModel: AppSplitViewModel = hiltViewModel()
) {
    val splitConfig by viewModel.splitConfig.collectAsStateWithLifecycle()
    val allApps by viewModel.allApps.collectAsStateWithLifecycle()
    val selectedApps by viewModel.selectedApps.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    
    var searchQuery by remember { mutableStateOf("") }
    var selectedCategory by remember { mutableStateOf<AppCategory?>(null) }
    var includeSystemApps by remember { mutableStateOf(false) }
    
    // 过滤应用
    val filteredApps = remember(allApps, searchQuery, selectedCategory, includeSystemApps) {
        allApps.filter { app ->
            // 搜索过滤
            val matchesSearch = if (searchQuery.isBlank()) true else {
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
            
            // 类别过滤
            val matchesCategory = selectedCategory?.let { app.category == it } ?: true
            
            // 系统应用过滤
            val matchesSystemFilter = if (includeSystemApps) true else !app.isSystemApp
            
            matchesSearch && matchesCategory && matchesSystemFilter
        }
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { 
                    Text(
                        when (splitConfig.mode) {
                            SplitMode.WHITELIST -> "选择代理应用"
                            SplitMode.BLACKLIST -> "选择直连应用"
                            else -> "应用选择"
                        }
                    ) 
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            viewModel.saveConfiguration()
                            onNavigateBack()
                        }
                    ) {
                        Text("保存")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 模式说明卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        getModeTitle(splitConfig.mode),
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    Text(
                        getModeDescription(splitConfig.mode),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
            
            // 筛选和搜索
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 搜索框
                    OutlinedTextField(
                        value = searchQuery,
                        onValueChange = { searchQuery = it },
                        label = { Text("搜索应用") },
                        leadingIcon = {
                            Icon(Icons.Filled.Search, contentDescription = null)
                        },
                        trailingIcon = {
                            if (searchQuery.isNotEmpty()) {
                                IconButton(onClick = { searchQuery = "" }) {
                                    Icon(Icons.Filled.Clear, contentDescription = "清除")
                                }
                            }
                        },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text)
                    )
                    
                    // 类别筛选
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            "类别:",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(40.dp)
                        )
                        
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            item {
                                FilterChip(
                                    onClick = { selectedCategory = null },
                                    label = { Text("全部") },
                                    selected = selectedCategory == null
                                )
                            }
                            
                            items(AppCategory.values()) { category ->
                                FilterChip(
                                    onClick = { 
                                        selectedCategory = if (selectedCategory == category) null else category 
                                    },
                                    label = { Text(getCategoryDisplayName(category)) },
                                    selected = selectedCategory == category
                                )
                            }
                        }
                    }
                    
                    // 系统应用开关
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            "包含系统应用",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Switch(
                            checked = includeSystemApps,
                            onCheckedChange = { 
                                includeSystemApps = it
                                viewModel.loadInstalledApps(it)
                            }
                        )
                    }
                }
            }
            
            // 统计信息和快速操作
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    "显示 ${filteredApps.size} 个应用，已选择 ${selectedApps.size} 个",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    TextButton(onClick = { viewModel.selectAllApps() }) {
                        Text("全选")
                    }
                    TextButton(onClick = { viewModel.deselectAllApps() }) {
                        Text("全不选")
                    }
                }
            }
            
            // 应用列表
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredApps) { app ->
                        AppSelectionItem(
                            app = app,
                            isSelected = selectedApps.contains(app.packageName),
                            onToggle = { viewModel.toggleAppSelection(app.packageName) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun AppSelectionItem(
    app: InstalledApp,
    isSelected: Boolean,
    onToggle: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        onClick = onToggle,
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 应用图标占位符
            Surface(
                modifier = Modifier.size(48.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surfaceVariant
            ) {
                Icon(
                    Icons.Filled.Apps,
                    contentDescription = null,
                    modifier = Modifier.padding(12.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 应用信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    app.appName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                Text(
                    app.packageName,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    if (app.isSystemApp) {
                        AssistChip(
                            onClick = { },
                            label = { Text("系统", style = MaterialTheme.typography.labelSmall) },
                            modifier = Modifier.height(24.dp)
                        )
                    }
                    AssistChip(
                        onClick = { },
                        label = { Text(getCategoryDisplayName(app.category), style = MaterialTheme.typography.labelSmall) },
                        modifier = Modifier.height(24.dp)
                    )
                }
            }
            
            // 选择状态
            Checkbox(
                checked = isSelected,
                onCheckedChange = null // handled by card click
            )
        }
    }
}

@Composable
private fun LazyRow(
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    content: androidx.compose.foundation.lazy.LazyListScope.() -> Unit
) {
    androidx.compose.foundation.lazy.LazyRow(
        modifier = modifier,
        horizontalArrangement = horizontalArrangement,
        content = content
    )
}

private fun getModeTitle(mode: SplitMode): String {
    return when (mode) {
        SplitMode.WHITELIST -> "白名单模式"
        SplitMode.BLACKLIST -> "黑名单模式"
        SplitMode.DISABLED -> "分流已禁用"
    }
}

private fun getModeDescription(mode: SplitMode): String {
    return when (mode) {
        SplitMode.WHITELIST -> "选中的应用将通过代理连接，其他应用直连网络"
        SplitMode.BLACKLIST -> "选中的应用将直连网络，其他应用通过代理连接"
        SplitMode.DISABLED -> "所有应用都通过代理连接"
    }
}

private fun getCategoryDisplayName(category: AppCategory): String {
    return when (category) {
        AppCategory.SOCIAL -> "社交"
        AppCategory.ENTERTAINMENT -> "娱乐"
        AppCategory.GAMES -> "游戏"
        AppCategory.SHOPPING -> "购物"
        AppCategory.TRAVEL -> "旅行"
        AppCategory.NEWS -> "新闻"
        AppCategory.FINANCE -> "金融"
        AppCategory.EDUCATION -> "教育"
        AppCategory.HEALTH -> "健康"
        AppCategory.PRODUCTIVITY -> "效率"
        AppCategory.SYSTEM -> "系统"
        AppCategory.OTHER -> "其他"
    }
}