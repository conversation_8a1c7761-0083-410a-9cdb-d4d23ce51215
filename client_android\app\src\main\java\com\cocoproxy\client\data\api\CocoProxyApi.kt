package com.cocoproxy.client.data.api

import retrofit2.Response
import retrofit2.http.*

/**
 * CocoProxy API interface for HTTP communication with server
 */
interface CocoProxyApi {
    
    /**
     * Login to the server
     */
    @POST("/api/login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    /**
     * Get server status
     */
    @GET("/api/status")
    suspend fun getStatus(@Header("Authorization") token: String): Response<ServerStatusResponse>
    
    /**
     * Get user profile
     */
    @GET("/api/me")
    suspend fun getUserProfile(@Header("Authorization") token: String): Response<UserProfileResponse>
    
    /**
     * Logout from server
     */
    @POST("/api/logout")
    suspend fun logout(@Header("Authorization") token: String): Response<LogoutResponse>
}

/**
 * Login request data
 */
data class LoginRequest(
    val username: String,
    val password: String
)

/**
 * Login response data
 */
data class LoginResponse(
    val token: String? = null,
    val username: String? = null,
    val expires: String? = null,
    val is_admin: Boolean = false,
    val error: String? = null,
    val message: String? = null
) {
    val success: Boolean
        get() = !token.isNullOrEmpty()
}

/**
 * Server status response
 */
data class ServerStatusResponse(
    val status: String,
    val uptime: Long,
    val connections: Int,
    val version: String
)

/**
 * User profile response
 */
data class UserProfileResponse(
    val username: String,
    val email: String?,
    val role: String,
    val created_at: String
)

/**
 * Logout response
 */
data class LogoutResponse(
    val success: Boolean = false,
    val message: String? = null
)