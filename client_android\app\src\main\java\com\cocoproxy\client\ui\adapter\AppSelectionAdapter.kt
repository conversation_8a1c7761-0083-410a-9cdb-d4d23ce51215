package com.cocoproxy.client.ui.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.cocoproxy.client.R
import com.cocoproxy.client.data.model.InstalledApp
import com.cocoproxy.client.core.AppManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AppSelectionAdapter(
    private val onAppSelected: (InstalledApp, Boolean) -> Unit,
    private val onAppLongClick: (InstalledApp) -> Unit,
    private val appManager: AppManager
) : RecyclerView.Adapter<AppSelectionAdapter.AppViewHolder>() {
    
    private var apps = listOf<InstalledApp>()
    private var selectedApps = setOf<String>()
    
    fun updateApps(newApps: List<InstalledApp>) {
        val diffCallback = AppDiffCallback(apps, newApps)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        
        apps = newApps
        diffResult.dispatchUpdatesTo(this)
    }
    
    fun updateSelectedApps(newSelectedApps: Set<String>) {
        val oldSelectedApps = selectedApps
        selectedApps = newSelectedApps
        
        // Update only changed items
        apps.forEachIndexed { index, app ->
            val wasSelected = oldSelectedApps.contains(app.packageName)
            val isSelected = selectedApps.contains(app.packageName)
            
            if (wasSelected != isSelected) {
                notifyItemChanged(index)
            }
        }
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AppViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_app_selection, parent, false)
        return AppViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: AppViewHolder, position: Int) {
        val app = apps[position]
        holder.bind(app, selectedApps.contains(app.packageName))
    }
    
    override fun getItemCount(): Int = apps.size
    
    inner class AppViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageViewIcon: ImageView = itemView.findViewById(R.id.image_view_app_icon)
        private val textViewAppName: TextView = itemView.findViewById(R.id.text_view_app_name)
        private val textViewPackageName: TextView = itemView.findViewById(R.id.text_view_package_name)
        private val textViewCategory: TextView = itemView.findViewById(R.id.text_view_category)
        private val checkBoxSelected: CheckBox = itemView.findViewById(R.id.checkbox_selected)
        private val textViewSystemApp: TextView = itemView.findViewById(R.id.text_view_system_app)
        
        fun bind(app: InstalledApp, isSelected: Boolean) {
            // Set app icon
            CoroutineScope(Dispatchers.Main).launch {
                val icon = withContext(Dispatchers.IO) {
                    appManager.getAppIcon(app.packageName)
                }
                imageViewIcon.setImageDrawable(icon)
            }
            
            // Set app name
            textViewAppName.text = app.appName
            
            // Set package name
            textViewPackageName.text = app.packageName
            
            // Set category
            textViewCategory.text = getCategoryDisplayName(app.category)
            
            // Set system app indicator
            if (app.isSystemApp) {
                textViewSystemApp.visibility = View.VISIBLE
                textViewSystemApp.text = "系统"
            } else {
                textViewSystemApp.visibility = View.GONE
            }
            
            // Set checkbox state
            checkBoxSelected.setOnCheckedChangeListener(null) // Remove listener to avoid triggering
            checkBoxSelected.isChecked = isSelected
            
            // Set click listeners
            itemView.setOnClickListener {
                val newState = !checkBoxSelected.isChecked
                checkBoxSelected.isChecked = newState
                onAppSelected(app, newState)
            }
            
            itemView.setOnLongClickListener {
                onAppLongClick(app)
                true
            }
            
            checkBoxSelected.setOnCheckedChangeListener { _, isChecked ->
                onAppSelected(app, isChecked)
            }
            
            // Style based on app type
            if (app.isSystemApp) {
                itemView.alpha = 0.7f
                textViewAppName.setTextColor(
                    itemView.context.getColor(android.R.color.darker_gray)
                )
            } else {
                itemView.alpha = 1.0f
                textViewAppName.setTextColor(
                    itemView.context.getColor(android.R.color.primary_text_light)
                )
            }
        }
        
        private fun getCategoryDisplayName(category: com.cocoproxy.client.data.model.AppCategory): String {
            return when (category) {
                com.cocoproxy.client.data.model.AppCategory.SOCIAL -> "社交"
                com.cocoproxy.client.data.model.AppCategory.ENTERTAINMENT -> "娱乐"
                com.cocoproxy.client.data.model.AppCategory.PRODUCTIVITY -> "效率"
                com.cocoproxy.client.data.model.AppCategory.GAMES -> "游戏"
                com.cocoproxy.client.data.model.AppCategory.SHOPPING -> "购物"
                com.cocoproxy.client.data.model.AppCategory.TRAVEL -> "旅行"
                com.cocoproxy.client.data.model.AppCategory.NEWS -> "新闻"
                com.cocoproxy.client.data.model.AppCategory.FINANCE -> "金融"
                com.cocoproxy.client.data.model.AppCategory.EDUCATION -> "教育"
                com.cocoproxy.client.data.model.AppCategory.HEALTH -> "健康"
                com.cocoproxy.client.data.model.AppCategory.SYSTEM -> "系统"
                com.cocoproxy.client.data.model.AppCategory.OTHER -> "其他"
            }
        }
    }
    
    private class AppDiffCallback(
        private val oldList: List<InstalledApp>,
        private val newList: List<InstalledApp>
    ) : DiffUtil.Callback() {
        
        override fun getOldListSize(): Int = oldList.size
        
        override fun getNewListSize(): Int = newList.size
        
        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition].packageName == newList[newItemPosition].packageName
        }
        
        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            val oldApp = oldList[oldItemPosition]
            val newApp = newList[newItemPosition]
            
            return oldApp.packageName == newApp.packageName &&
                    oldApp.appName == newApp.appName &&
                    oldApp.versionName == newApp.versionName &&
                    oldApp.versionCode == newApp.versionCode &&
                    oldApp.category == newApp.category &&
                    oldApp.isSystemApp == newApp.isSystemApp
        }
    }
}