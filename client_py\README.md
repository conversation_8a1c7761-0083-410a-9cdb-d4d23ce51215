# CocoProxy Desktop Client

A modern desktop client for CocoProxy built with Python and PySide6.

## Features

- **User-friendly GUI**: Clean and intuitive interface
- **Secure Authentication**: Login with username and password
- **Real-time Monitoring**: Traffic usage and connection status
- **Multiple Protocols**: SOCKS5 and HTTP proxy support
- **Encryption Support**: AES and ChaCha20 encryption
- **System Tray Integration**: Minimize to system tray
- **Auto-login**: Remember credentials for convenience
- **Cross-platform**: Works on Windows, macOS, and Linux

## Requirements

- Python 3.8 or later
- PySide6
- requests
- python-dotenv

## Installation

### Windows

1. Run the installation script:
   ```batch
   install.bat
   ```

2. Start the client:
   ```batch
   run.bat
   ```

### Manual Installation

1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the client:
   ```bash
   python main.py
   ```

## Configuration

The client stores its configuration in:
- **Windows**: `%USERPROFILE%\.cocoproxy\config.json`
- **macOS/Linux**: `~/.cocoproxy/config.json`

### Configuration Options

```json
{
    "server": {
        "host": "127.0.0.1",
        "port": 28888,
        "admin_port": 28080
    },
    "proxy": {
        "local_port": 1080,
        "protocol": "socks5",
        "encryption": "aes"
    },
    "user": {
        "username": "",
        "password": "",
        "remember_me": false
    },
    "ui": {
        "theme": "system",
        "language": "en",
        "minimize_to_tray": true,
        "start_on_boot": false,
        "check_updates": true
    }
}
```

## Usage

### First Time Setup

1. Launch the client
2. Click "Connect" or use File → Login
3. Enter your username and password
4. Optionally check "Remember me" to save credentials
5. Click "Login"

### Main Interface

- **Connection Status**: Shows current connection state
- **Server Information**: Displays server details and uptime
- **Traffic Information**: Real-time traffic usage with progress bar
- **Control Buttons**: Connect/disconnect, test connection, refresh
- **Log Output**: Shows connection events and errors

### Settings

Access settings through Settings → Preferences:

- **Server Tab**: Configure server connection details
- **Proxy Tab**: Set local proxy port, protocol, and encryption
- **UI Tab**: Customize appearance and behavior

### System Tray

When minimized to system tray:
- **Double-click**: Show/hide main window
- **Right-click**: Access context menu with show/hide/quit options

## Troubleshooting

### Connection Issues

1. **Server Unreachable**:
   - Check server host and port in settings
   - Ensure the CocoProxy server is running
   - Check firewall settings

2. **Login Failed**:
   - Verify username and password
   - Check if user account exists on server
   - Ensure user has proper permissions

3. **Proxy Not Working**:
   - Check local proxy port configuration
   - Ensure no other applications are using the same port
   - Verify proxy settings in your applications

### Application Issues

1. **Won't Start**:
   - Check Python installation
   - Verify all dependencies are installed
   - Check log files in `~/.cocoproxy/logs/`

2. **UI Issues**:
   - Try different theme in settings
   - Check display scaling settings
   - Update graphics drivers

## Logs

Application logs are stored in:
- **Windows**: `%USERPROFILE%\.cocoproxy\logs\cocoproxy.log`
- **macOS/Linux**: `~/.cocoproxy/logs/cocoproxy.log`

## Building from Source

### Development Setup

1. Clone the repository
2. Install development dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python main.py
   ```

### Creating Executable

Use PyInstaller to create standalone executables:

```bash
pip install pyinstaller
pyinstaller --windowed --onefile --name "CocoProxy Client" main.py
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and bug reports, please visit the project repository or contact the development team.