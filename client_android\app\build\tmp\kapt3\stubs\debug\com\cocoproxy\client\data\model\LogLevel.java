package com.cocoproxy.client.data.model;

/**
 * Log level enumeration
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0019\b\u0002\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/cocoproxy/client/data/model/LogLevel;", "", "displayName", "", "priority", "", "<init>", "(Ljava/lang/String;ILjava/lang/String;I)V", "getDisplayName", "()Ljava/lang/String;", "getPriority", "()I", "VERBOSE", "DEBUG", "INFO", "WARN", "ERROR", "app_debug"})
public enum LogLevel {
    /*public static final*/ VERBOSE /* = new VERBOSE(null, 0) */,
    /*public static final*/ DEBUG /* = new DEBUG(null, 0) */,
    /*public static final*/ INFO /* = new INFO(null, 0) */,
    /*public static final*/ WARN /* = new WARN(null, 0) */,
    /*public static final*/ ERROR /* = new ERROR(null, 0) */;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String displayName = null;
    private final int priority = 0;
    
    LogLevel(java.lang.String displayName, int priority) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getPriority() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.cocoproxy.client.data.model.LogLevel> getEntries() {
        return null;
    }
}