设计一整套网络代理方案，类似于shadowsocks，要求如下
1. 基于TCP协议，支持多协议（HTTP、HTTPS、TCP、UDP）
2. 支持多平台（Windows、Linux、Mac）, 支持多用户
3. 支持两种私密加密通信方式，不容易检测到特征码，后续可以扩展其他加密方式
4. 要实现服务端、桌面客户端、移动客户端
5. 服务端使用分别使用go和python实现一份，要有后台管理，能监控客户端流量等信息
6. 桌面客户端要求跨平台，也使用python来实现，图形框架采用pyside6
7. 移动客户端使用native开发


client_android修复
当前项目server_go和client_py已经能够正常使用，server_go已经部署到远程服务器，client_android是参照client_py实现的，目前整体流程已经实现，但是现在有个问题，我将chrome添加到白名单，启动代理服务之后，chrome无法通过代理服务访问google.com，而这一块的功能实现是通过vpn拦截请求来处理的，client_py作为桌面客户端不需要这样做，因此没有参考性，现在需要你修复这个问题