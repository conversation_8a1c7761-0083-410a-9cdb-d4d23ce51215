package com.cocoproxy.client.ui.screen;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u00002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a>\u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\n2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\u0018\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\b2\u0006\u0010\u0013\u001a\u00020\bH\u0002\u00a8\u0006\u0014"}, d2 = {"SplitModeConfigScreen", "", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/cocoproxy/client/ui/viewmodel/AppSplitViewModel;", "SplitModeOption", "mode", "Lcom/cocoproxy/client/core/SplitMode;", "title", "", "description", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "isSelected", "", "onSelect", "getModeChangeDescription", "currentMode", "newMode", "app_debug"})
public final class SplitModeConfigScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SplitModeConfigScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.viewmodel.AppSplitViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void SplitModeOption(com.cocoproxy.client.core.SplitMode mode, java.lang.String title, java.lang.String description, androidx.compose.ui.graphics.vector.ImageVector icon, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onSelect) {
    }
    
    private static final java.lang.String getModeChangeDescription(com.cocoproxy.client.core.SplitMode currentMode, com.cocoproxy.client.core.SplitMode newMode) {
        return null;
    }
}