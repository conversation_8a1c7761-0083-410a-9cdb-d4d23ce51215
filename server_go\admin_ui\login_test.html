<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #4a6ee0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #4a6ee0;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #3a5ecc;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Login Test</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="admin123">
            </div>
            <button type="submit">Login</button>
        </form>
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.textContent = 'Attempting login...';
            resultDiv.className = 'result';
            resultDiv.style.display = 'block';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `Login successful! Token: ${data.token.substring(0, 20)}...`;
                    resultDiv.className = 'result success';
                    
                    // Store token in localStorage
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('username', data.username);
                    
                    // Redirect to index.html after 2 seconds
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                } else {
                    const errorData = await response.json();
                    resultDiv.textContent = `Login failed: ${errorData.error || 'Unknown error'}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `Network error: ${error.message}. Make sure the server is running.`;
                resultDiv.className = 'result error';
            }
        });
    </script>
</body>
</html>