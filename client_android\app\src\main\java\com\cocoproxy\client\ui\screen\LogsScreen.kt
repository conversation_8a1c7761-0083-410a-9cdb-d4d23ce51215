package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.data.model.LogEntry
import com.cocoproxy.client.data.model.LogLevel
import com.cocoproxy.client.ui.theme.*
import com.cocoproxy.client.ui.theme.ErrorColor
import com.cocoproxy.client.ui.theme.WarningColor
import com.cocoproxy.client.ui.theme.InfoColor
import com.cocoproxy.client.ui.theme.DebugColor
import com.cocoproxy.client.ui.theme.VerboseColor
import com.cocoproxy.client.ui.viewmodel.MainViewModel
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LogsScreen(
    viewModel: MainViewModel = hiltViewModel()
) {
    val logs by viewModel.logs.collectAsStateWithLifecycle()
    val uiConfig by viewModel.uiConfig.collectAsStateWithLifecycle()
    
    var selectedLogLevel by remember { mutableStateOf<LogLevel?>(null) }
    var showFilterMenu by remember { mutableStateOf(false) }
    var autoScroll by remember { mutableStateOf(true) }
    
    val listState = rememberLazyListState()
    
    // Auto scroll to bottom when new logs arrive
    LaunchedEffect(logs.size, autoScroll) {
        if (autoScroll && logs.isNotEmpty()) {
            listState.animateScrollToItem(logs.size - 1)
        }
    }
    
    val filteredLogs = remember(logs, selectedLogLevel) {
        if (selectedLogLevel == null) {
            logs
        } else {
            logs.filter { it.level == selectedLogLevel }
        }
    }
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Header with controls
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            ),
            shape = RoundedCornerShape(0.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Logs",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.weight(1f)
                )
                
                // Log count
                Text(
                    text = "${filteredLogs.size} entries",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                // Auto scroll toggle
                IconButton(
                    onClick = { autoScroll = !autoScroll }
                ) {
                    Icon(
                        imageVector = if (autoScroll) Icons.Filled.VerticalAlignBottom else Icons.Filled.PauseCircle,
                        contentDescription = if (autoScroll) "Disable auto scroll" else "Enable auto scroll",
                        tint = if (autoScroll) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Filter button
                Box {
                    IconButton(
                        onClick = { showFilterMenu = true }
                    ) {
                        Icon(
                            imageVector = Icons.Filled.FilterList,
                            contentDescription = "Filter logs",
                            tint = if (selectedLogLevel != null) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    DropdownMenu(
                        expanded = showFilterMenu,
                        onDismissRequest = { showFilterMenu = false }
                    ) {
                        DropdownMenuItem(
                            text = { Text("All Levels") },
                            onClick = {
                                selectedLogLevel = null
                                showFilterMenu = false
                            },
                            leadingIcon = {
                                if (selectedLogLevel == null) {
                                    Icon(
                                        imageVector = Icons.Filled.Check,
                                        contentDescription = null
                                    )
                                }
                            }
                        )
                        
                        LogLevel.values().forEach { level ->
                            DropdownMenuItem(
                                text = { Text(level.name) },
                                onClick = {
                                    selectedLogLevel = level
                                    showFilterMenu = false
                                },
                                leadingIcon = {
                                    if (selectedLogLevel == level) {
                                        Icon(
                                            imageVector = Icons.Filled.Check,
                                            contentDescription = null
                                        )
                                    }
                                }
                            )
                        }
                    }
                }
                
                // Clear logs
                IconButton(
                    onClick = { viewModel.clearLogs() }
                ) {
                    Icon(
                        imageVector = Icons.Filled.Clear,
                        contentDescription = "Clear logs"
                    )
                }
            }
        }
        
        // Logs list
        if (filteredLogs.isEmpty()) {
            // Empty state
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        imageVector = Icons.Filled.Description,
                        contentDescription = null,
                        modifier = Modifier.size(64.dp),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = if (selectedLogLevel == null) "No logs available" else "No ${selectedLogLevel?.name?.lowercase()} logs",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Text(
                        text = "Logs will appear here when the proxy is running",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(8.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                items(
                    items = filteredLogs,
                    key = { it.timestamp }
                ) { logEntry ->
                    LogEntryItem(
                        logEntry = logEntry,
                        showDebugInfo = uiConfig.debugMode
                    )
                }
            }
        }
    }
}

@Composable
fun LogEntryItem(
    logEntry: LogEntry,
    showDebugInfo: Boolean
) {
    val timeFormatter = remember { SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when (logEntry.level) {
                LogLevel.ERROR -> ErrorColor.copy(alpha = 0.1f)
                LogLevel.WARN -> WarningColor.copy(alpha = 0.1f)
                LogLevel.INFO -> InfoColor.copy(alpha = 0.05f)
                LogLevel.DEBUG -> MaterialTheme.colorScheme.surfaceVariant
                LogLevel.VERBOSE -> VerboseColor.copy(alpha = 0.05f)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // Header row with timestamp and level
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Log level indicator
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .clip(RoundedCornerShape(4.dp))
                        .background(
                            when (logEntry.level) {
                                LogLevel.ERROR -> ErrorColor
                                LogLevel.WARN -> WarningColor
                                LogLevel.INFO -> InfoColor
                                LogLevel.DEBUG -> MaterialTheme.colorScheme.onSurfaceVariant
                                LogLevel.VERBOSE -> VerboseColor
                            }
                        )
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // Log level text
                Text(
                    text = logEntry.level.name,
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Bold,
                    color = when (logEntry.level) {
                        LogLevel.ERROR -> ErrorColor
                        LogLevel.WARN -> WarningColor
                        LogLevel.INFO -> InfoColor
                        LogLevel.DEBUG -> MaterialTheme.colorScheme.onSurfaceVariant
                        LogLevel.VERBOSE -> VerboseColor
                    },
                    modifier = Modifier.width(50.dp)
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // Timestamp
                Text(
                    text = timeFormatter.format(logEntry.timestamp),
                    style = MaterialTheme.typography.labelSmall,
                    fontFamily = FontFamily.Monospace,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f)
                )
                
                // Tag (if available)
                if (logEntry.tag.isNotEmpty()) {
                    Text(
                        text = logEntry.tag,
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier
                            .background(
                                MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                RoundedCornerShape(4.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(4.dp))
            
            // Log message
            Text(
                text = logEntry.message,
                style = MaterialTheme.typography.bodyMedium,
                fontFamily = FontFamily.Monospace,
                fontSize = 13.sp,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // Debug info (if enabled and available)
            if (showDebugInfo && (logEntry.thread.isNotEmpty() || logEntry.className.isNotEmpty())) {
                Spacer(modifier = Modifier.height(4.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    if (logEntry.thread.isNotEmpty()) {
                        Text(
                            text = "Thread: ${logEntry.thread}",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            fontFamily = FontFamily.Monospace
                        )
                    }
                    
                    if (logEntry.className.isNotEmpty()) {
                        Text(
                            text = "Class: ${logEntry.className}",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            fontFamily = FontFamily.Monospace
                        )
                    }
                }
            }
        }
    }
}