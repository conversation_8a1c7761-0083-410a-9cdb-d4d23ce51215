@echo off
echo Creating test admin user...

cd server_go\tools

REM Build the create_admin_user tool if it doesn't exist
if not exist "create_admin_user.exe" (
    echo Building create_admin_user tool...
    go build -o create_admin_user.exe create_admin_user.go
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to build create_admin_user tool
        pause
        exit /b 1
    )
)

REM Create test admin user
echo Creating admin user 'test' with password 'test'...
create_admin_user.exe test test

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Test admin user created successfully!
    echo You can now login to the client with:
    echo Username: test
    echo Password: test
    echo.
    echo Make sure the server is running and try logging in again.
) else (
    echo Failed to create test admin user
)

cd ..\..
pause