#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QPushButton, QCheckBox,
    QGroupBox, QMessageBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

class LoginDialog(QDialog):
    """Login dialog for CocoProxy client"""
    
    def __init__(self, config, parent=None):
        """Initialize login dialog"""
        super().__init__(parent)
        self.config = config
        
        # Dialog properties
        self.setWindowTitle("Login to CocoProxy")
        self.setModal(True)
        self.setFixedSize(350, 200)
        
        # Setup UI
        self.setup_ui()
        
        # Load saved credentials
        self.load_saved_credentials()
    
    def setup_ui(self):
        """Setup user interface"""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Login form group
        form_group = QGroupBox("Login Credentials")
        form_layout = QGridLayout(form_group)
        
        # Username field
        form_layout.addWidget(QLabel("Username:"), 0, 0)
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter your username")
        form_layout.addWidget(self.username_edit, 0, 1)
        
        # Password field
        form_layout.addWidget(QLabel("Password:"), 1, 0)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Enter your password")
        form_layout.addWidget(self.password_edit, 1, 1)
        
        # Remember me checkbox
        self.remember_checkbox = QCheckBox("Remember me")
        form_layout.addWidget(self.remember_checkbox, 2, 0, 1, 2)
        
        main_layout.addWidget(form_group)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        # Cancel button
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        # Login button
        self.login_button = QPushButton("Login")
        self.login_button.setDefault(True)
        self.login_button.clicked.connect(self.accept_login)
        button_layout.addWidget(self.login_button)
        
        main_layout.addLayout(button_layout)
        
        # Connect Enter key to login
        self.password_edit.returnPressed.connect(self.accept_login)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
    
    def load_saved_credentials(self):
        """Load saved credentials from config"""
        if self.config.get("user", "remember_me"):
            username = self.config.get("user", "username")
            password = self.config.get("user", "password")
            
            if username:
                self.username_edit.setText(username)
                self.remember_checkbox.setChecked(True)
                
                if password:
                    self.password_edit.setText(password)
                    self.login_button.setFocus()
                else:
                    self.password_edit.setFocus()
            else:
                self.username_edit.setFocus()
        else:
            self.username_edit.setFocus()
    
    def accept_login(self):
        """Accept login if credentials are valid"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # Validate input
        if not username:
            QMessageBox.warning(self, "Invalid Input", "Please enter a username.")
            self.username_edit.setFocus()
            return
        
        if not password:
            QMessageBox.warning(self, "Invalid Input", "Please enter a password.")
            self.password_edit.setFocus()
            return
        
        # Accept dialog
        self.accept()
    
    def get_credentials(self):
        """Get entered credentials"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        remember = self.remember_checkbox.isChecked()
        
        return username, password, remember