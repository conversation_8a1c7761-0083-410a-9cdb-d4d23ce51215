<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- App Icon -->
        <ImageView
            android:id="@+id/image_view_app_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="12dp"
            android:contentDescription="应用图标"
            android:scaleType="centerCrop"
            tools:src="@mipmap/ic_launcher" />

        <!-- App Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- App Name and System Indicator -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/text_view_app_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="?android:attr/textColorPrimary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="微信" />

                <TextView
                    android:id="@+id/text_view_system_app"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_system_app_tag"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:text="系统"
                    android:textColor="?android:attr/textColorSecondary"
                    android:textSize="10sp"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- Package Name -->
            <TextView
                android:id="@+id/text_view_package_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:textColor="?android:attr/textColorSecondary"
                android:textSize="12sp"
                tools:text="com.tencent.mm" />

            <!-- Category -->
            <TextView
                android:id="@+id/text_view_category"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:background="@drawable/bg_category_tag"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:textColor="?android:attr/colorPrimary"
                android:textSize="10sp"
                tools:text="社交" />

        </LinearLayout>

        <!-- Selection Checkbox -->
        <CheckBox
            android:id="@+id/checkbox_selected"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:contentDescription="选择应用" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>