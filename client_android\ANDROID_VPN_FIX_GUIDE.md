# Android VPN 修复指南

## 修复内容

### 1. VPN权限处理修复
- 修复了VPN权限检查逻辑，避免应用崩溃
- 在ViewModel中先检查权限，再启动VPN服务
- 正确处理权限请求和响应流程

### 2. 协议格式修复
- 修复了 `sendCocoProxyRequestForTcp` 和 `sendCocoProxyRequestForSocks5` 方法
- 使用与Python客户端一致的CocoProxy协议格式
- 协议格式：`protocol_type + encrypt_type + username_length + username + padding + data_length + data`

### 3. VPN实现简化
- 添加了 `processPacketsWithProxy()` 方法，简化数据包处理
- 移除复杂的TCP包重构逻辑
- 使用本地SOCKS5代理重定向流量

### 4. 路由配置优化
- 添加HTTP代理设置，将HTTP流量重定向到本地SOCKS5代理
- 正确排除自身应用，避免无限循环
- 优化应用分流配置

## 测试步骤

### 1. 编译和安装
```bash
cd client_android
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. 配置服务器
确保服务器配置正确：
- 服务器地址：你的远程服务器IP
- 端口：28888
- 用户名：有效的用户名
- 本地代理端口：1080

### 3. 配置应用分流
1. 打开CocoProxy应用
2. 进入应用分流设置
3. 选择"白名单模式"
4. 添加Chrome到白名单：`com.android.chrome`
5. 确保其他应用不在白名单中（这样它们会直接连接）

### 4. 启动VPN服务
1. 点击"启动VPN"
2. 授予VPN权限（重要：必须授予）
3. 检查通知栏是否显示VPN已连接
4. 等待几秒钟让服务完全启动

### 5. 验证本地代理
在启动VPN后，检查本地SOCKS5代理是否运行：
```bash
adb logcat -s CocoProxyVpnService | grep "Started local SOCKS5"
```
应该看到：`Started local SOCKS5 proxy server on 127.0.0.1:1080`

### 6. 测试Chrome访问
1. 打开Chrome浏览器
2. 清除Chrome缓存（设置 -> 隐私和安全 -> 清除浏览数据）
3. 尝试访问 `https://google.com`
4. 检查是否能正常访问
5. 尝试访问其他被墙网站如 `https://youtube.com`

### 7. 验证分流效果
1. 打开其他应用（如系统浏览器）
2. 尝试访问相同网站
3. 确认其他应用无法访问被墙网站（说明分流正常工作）

## 日志检查

使用以下命令查看日志：
```bash
adb logcat -s CocoProxyVpnService
```

### 关键日志信息
1. **协议发送**：
   ```
   Sending CocoProxy packet: X bytes
   Protocol: 3, Encrypt: 0, User: username, Target: host:port
   ```

2. **SOCKS5握手**：
   ```
   SOCKS5 auth successful
   SOCKS5 connect successful
   ```

3. **VPN状态**：
   ```
   VPN interface established successfully
   Starting simplified packet processing with proxy
   ```

4. **应用分流**：
   ```
   App will use VPN/proxy: com.android.chrome
   ```

## 故障排除

### 1. 协议连接失败
- 检查服务器地址和端口是否正确
- 确认用户名是否有效
- 查看服务器端日志

### 2. VPN无法启动
- 检查VPN权限是否授予
- 确认没有其他VPN应用在运行
- 重启应用重试

### 3. Chrome无法访问
- 确认Chrome在白名单中
- 检查本地SOCKS5代理是否启动（端口1080）
- 查看数据包处理日志

### 4. 无限循环问题
- 确认CocoProxy应用自身被排除在VPN之外
- 检查路由配置是否正确

## 预期结果

修复成功后，你应该看到：

1. **连接建立**：
   - CocoProxy协议握手成功
   - SOCKS5握手成功
   - VPN接口建立成功

2. **流量代理**：
   - Chrome的HTTP/HTTPS流量通过代理
   - 可以访问被墙的网站（如google.com）
   - 其他应用直接连接（如果不在白名单中）

3. **日志正常**：
   - 没有协议错误
   - 没有连接失败
   - 数据包正确处理

## 技术细节

### 协议格式
```
+----------+----------+----------+----------+----------+----------+----------+
| Protocol | Encrypt  | Username | Username | Padding  | Data Len | Data     |
| Type(1)  | Type(1)  | Len(1)   | (N)      | (4)      | (4)      | (N)      |
+----------+----------+----------+----------+----------+----------+----------+
```

### VPN工作原理
1. VPN拦截白名单应用的网络流量
2. 设置HTTP代理指向本地SOCKS5代理（127.0.0.1:1080）
3. 本地SOCKS5代理接收连接请求
4. 通过CocoProxy协议连接到远程服务器
5. 建立SOCKS5隧道进行数据转发

这种方式比直接处理TCP包更简单、更可靠。
