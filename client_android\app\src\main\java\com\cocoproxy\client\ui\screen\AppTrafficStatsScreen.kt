package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.data.model.AppTrafficStats
import com.cocoproxy.client.ui.viewmodel.AppTrafficViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppTrafficStatsScreen(
    onNavigateBack: () -> Unit,
    viewModel: AppTrafficViewModel = hiltViewModel()
) {
    val trafficStats by viewModel.trafficStats.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    val totalUpload by viewModel.totalUpload.collectAsStateWithLifecycle()
    val totalDownload by viewModel.totalDownload.collectAsStateWithLifecycle()
    
    var sortBy by remember { mutableStateOf(TrafficSortType.TOTAL_DESC) }
    var showOnlyProxied by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        viewModel.loadTrafficStats()
    }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("应用流量统计") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    IconButton(onClick = { viewModel.refreshStats() }) {
                        Icon(Icons.Filled.Refresh, contentDescription = "刷新")
                    }
                    IconButton(onClick = { viewModel.clearStats() }) {
                        Icon(Icons.Filled.Clear, contentDescription = "清除")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // 总流量统计卡片
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    Text(
                        "总流量统计",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.primary
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        TrafficStatItem(
                            label = "上传",
                            value = formatBytes(totalUpload),
                            icon = Icons.Filled.Upload
                        )
                        TrafficStatItem(
                            label = "下载", 
                            value = formatBytes(totalDownload),
                            icon = Icons.Filled.Download
                        )
                        TrafficStatItem(
                            label = "总计",
                            value = formatBytes(totalUpload + totalDownload),
                            icon = Icons.Filled.Analytics
                        )
                    }
                }
            }
            
            // 筛选和排序选项
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 排序选项
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            "排序:",
                            style = MaterialTheme.typography.bodyMedium,
                            modifier = Modifier.width(40.dp)
                        )
                        
                        LazyRow(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(TrafficSortType.values()) { sortType ->
                                FilterChip(
                                    onClick = { sortBy = sortType },
                                    label = { Text(getSortTypeDisplayName(sortType)) },
                                    selected = sortBy == sortType
                                )
                            }
                        }
                    }
                    
                    // 筛选选项
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            "仅显示代理应用",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Switch(
                            checked = showOnlyProxied,
                            onCheckedChange = { showOnlyProxied = it }
                        )
                    }
                }
            }
            
            // 应用流量列表
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                val filteredAndSortedStats = remember(trafficStats, sortBy, showOnlyProxied) {
                    trafficStats
                        .filter { if (showOnlyProxied) it.isProxied else true }
                        .sortedWith(getSortComparator(sortBy))
                }
                
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(filteredAndSortedStats) { stats ->
                        AppTrafficStatsItem(stats = stats)
                    }
                    
                    if (filteredAndSortedStats.isEmpty()) {
                        item {
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(
                                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                                )
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(32.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Icon(
                                        Icons.Filled.Analytics,
                                        contentDescription = null,
                                        modifier = Modifier.size(48.dp),
                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    Text(
                                        "暂无流量数据",
                                        style = MaterialTheme.typography.titleMedium,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                    Text(
                                        "开始使用应用后将显示流量统计",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun TrafficStatItem(
    label: String,
    value: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        Icon(
            icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        Text(
            value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onPrimaryContainer
        )
    }
}

@Composable
private fun AppTrafficStatsItem(
    stats: AppTrafficStats
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 应用图标占位符
            Surface(
                modifier = Modifier.size(48.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surfaceVariant
            ) {
                Icon(
                    Icons.Filled.Apps,
                    contentDescription = null,
                    modifier = Modifier.padding(12.dp),
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 应用信息
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        stats.appName,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    if (stats.isProxied) {
                        AssistChip(
                            onClick = { },
                            label = { Text("代理", style = MaterialTheme.typography.labelSmall) },
                            modifier = Modifier.height(24.dp),
                            colors = AssistChipDefaults.assistChipColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer
                            )
                        )
                    }
                }
                
                Text(
                    stats.packageName,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // 流量统计
            Column(
                horizontalAlignment = Alignment.End,
                verticalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                Text(
                    "↑ ${formatBytes(stats.uploadBytes)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    "↓ ${formatBytes(stats.downloadBytes)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.secondary
                )
                Text(
                    formatBytes(stats.totalBytes),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
private fun LazyRow(
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    content: androidx.compose.foundation.lazy.LazyListScope.() -> Unit
) {
    androidx.compose.foundation.lazy.LazyRow(
        modifier = modifier,
        horizontalArrangement = horizontalArrangement,
        content = content
    )
}

enum class TrafficSortType {
    TOTAL_DESC, TOTAL_ASC, UPLOAD_DESC, DOWNLOAD_DESC, NAME_ASC
}

private fun getSortTypeDisplayName(sortType: TrafficSortType): String {
    return when (sortType) {
        TrafficSortType.TOTAL_DESC -> "总流量↓"
        TrafficSortType.TOTAL_ASC -> "总流量↑"
        TrafficSortType.UPLOAD_DESC -> "上传↓"
        TrafficSortType.DOWNLOAD_DESC -> "下载↓"
        TrafficSortType.NAME_ASC -> "名称↑"
    }
}

private fun getSortComparator(sortType: TrafficSortType): Comparator<AppTrafficStats> {
    return when (sortType) {
        TrafficSortType.TOTAL_DESC -> compareByDescending { it.totalBytes }
        TrafficSortType.TOTAL_ASC -> compareBy { it.totalBytes }
        TrafficSortType.UPLOAD_DESC -> compareByDescending { it.uploadBytes }
        TrafficSortType.DOWNLOAD_DESC -> compareByDescending { it.downloadBytes }
        TrafficSortType.NAME_ASC -> compareBy { it.appName }
    }
}

private fun formatBytes(bytes: Long): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = bytes.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return if (unitIndex == 0) {
        "${size.toInt()} ${units[unitIndex]}"
    } else {
        "%.1f %s".format(size, units[unitIndex])
    }
}