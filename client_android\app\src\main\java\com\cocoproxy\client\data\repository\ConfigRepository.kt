package com.cocoproxy.client.data.repository

import android.content.Context
import android.util.Log
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.*
import androidx.datastore.preferences.preferencesDataStore
import com.cocoproxy.client.data.model.*
import com.cocoproxy.client.core.AppSplitConfig
import com.cocoproxy.client.core.SplitMode
import com.google.gson.Gson
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton

val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "cocoproxy_settings")

@Singleton
class ConfigRepository @Inject constructor(
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val TAG = "ConfigRepository"
        
        // Server config keys
        private val SERVER_HOST = stringPreferencesKey("server_host")
        private val SERVER_PORT = intPreferencesKey("server_port")
        private val SERVER_ADMIN_PORT = intPreferencesKey("server_admin_port")
        private val SERVER_USERNAME = stringPreferencesKey("server_username")
        private val SERVER_PASSWORD = stringPreferencesKey("server_password")
        private val SERVER_TOKEN = stringPreferencesKey("server_token")
        private val SERVER_REMEMBER_ME = booleanPreferencesKey("server_remember_me")
        
        // Proxy config keys
        private val PROXY_LOCAL_PORT = intPreferencesKey("proxy_local_port")
        private val PROXY_PROTOCOL = stringPreferencesKey("proxy_protocol")
        private val PROXY_ENCRYPTION = stringPreferencesKey("proxy_encryption")
        private val PROXY_AUTO_START = booleanPreferencesKey("proxy_auto_start")
        private val PROXY_USE_VPN = booleanPreferencesKey("proxy_use_vpn")
        
        // UI config keys
        private val UI_THEME = stringPreferencesKey("ui_theme")
        private val UI_LANGUAGE = stringPreferencesKey("ui_language")
        private val UI_MINIMIZE_TO_TRAY = booleanPreferencesKey("ui_minimize_to_tray")
        private val UI_START_ON_BOOT = booleanPreferencesKey("ui_start_on_boot")
        private val UI_CHECK_UPDATES = booleanPreferencesKey("ui_check_updates")
        private val UI_SHOW_NOTIFICATIONS = booleanPreferencesKey("ui_show_notifications")
        
        // VPN config keys
        private val VPN_ENABLED = booleanPreferencesKey("vpn_enabled")
        private val VPN_DNS_SERVERS = stringPreferencesKey("vpn_dns_servers")
        private val VPN_ROUTES = stringPreferencesKey("vpn_routes")
        private val VPN_EXCLUDED_APPS = stringPreferencesKey("vpn_excluded_apps")
        private val VPN_MTU = intPreferencesKey("vpn_mtu")
        private val VPN_SESSION_NAME = stringPreferencesKey("vpn_session_name")
        
        // App split config keys
        private val APP_SPLIT_MODE = stringPreferencesKey("app_split_mode")
        private val APP_SPLIT_SELECTED_APPS = stringPreferencesKey("app_split_selected_apps")
        private val APP_SPLIT_ENABLED = booleanPreferencesKey("app_split_enabled")
        
        // User profile keys
        private val USER_PROFILE = stringPreferencesKey("user_profile")
    }
    
    private val gson = Gson()
    
    /**
     * Get server configuration flow
     */
    val serverConfig: Flow<ServerConfig> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading server config", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            ServerConfig(
                host = preferences[SERVER_HOST] ?: "127.0.0.1",
                port = preferences[SERVER_PORT] ?: 28888,
                adminPort = preferences[SERVER_ADMIN_PORT] ?: 28080,
                username = preferences[SERVER_USERNAME] ?: "",
                password = preferences[SERVER_PASSWORD] ?: "",
                token = preferences[SERVER_TOKEN] ?: "",
                rememberMe = preferences[SERVER_REMEMBER_ME] ?: false
            )
        }
    
    /**
     * Get proxy configuration flow
     */
    val proxyConfig: Flow<ProxyConfig> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading proxy config", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            ProxyConfig(
                localPort = preferences[PROXY_LOCAL_PORT] ?: 1080,
                protocol = preferences[PROXY_PROTOCOL] ?: "socks5",
                encryption = preferences[PROXY_ENCRYPTION] ?: "aes",
                autoStart = preferences[PROXY_AUTO_START] ?: false,
                useVpn = preferences[PROXY_USE_VPN] ?: false
            )
        }
    
    /**
     * Get UI configuration flow
     */
    val uiConfig: Flow<UiConfig> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading UI config", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            UiConfig(
                theme = preferences[UI_THEME] ?: "system",
                language = preferences[UI_LANGUAGE] ?: "en",
                minimizeToTray = preferences[UI_MINIMIZE_TO_TRAY] ?: true,
                startOnBoot = preferences[UI_START_ON_BOOT] ?: false,
                checkUpdates = preferences[UI_CHECK_UPDATES] ?: true,
                showNotifications = preferences[UI_SHOW_NOTIFICATIONS] ?: true
            )
        }
    
    /**
     * Get VPN configuration flow
     */
    val vpnConfig: Flow<VpnConfig> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading VPN config", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val dnsServersJson = preferences[VPN_DNS_SERVERS] ?: "[\"8.8.8.8\",\"8.8.4.4\"]"
            val routesJson = preferences[VPN_ROUTES] ?: "[]"
            val excludedAppsJson = preferences[VPN_EXCLUDED_APPS] ?: "[]"
            
            val appSplitConfig = if (preferences[APP_SPLIT_ENABLED] == true) {
                val modeString = preferences[APP_SPLIT_MODE] ?: "DISABLED"
                val selectedAppsString = preferences[APP_SPLIT_SELECTED_APPS] ?: ""
                val selectedApps = if (selectedAppsString.isNotEmpty()) {
                    selectedAppsString.split(",").toSet()
                } else {
                    emptySet()
                }
                
                AppSplitConfig(
                    enabled = true,
                    mode = try {
                        SplitMode.valueOf(modeString)
                    } catch (e: Exception) {
                        SplitMode.DISABLED
                    },
                    selectedApps = selectedApps
                )
            } else {
                null
            }
            
            VpnConfig(
                enabled = preferences[VPN_ENABLED] ?: false,
                dnsServers = try {
                    gson.fromJson(dnsServersJson, Array<String>::class.java).toList()
                } catch (e: Exception) {
                    listOf("8.8.8.8", "8.8.4.4")
                },
                routes = try {
                    gson.fromJson(routesJson, Array<String>::class.java).toList()
                } catch (e: Exception) {
                    emptyList()
                },
                excludedApps = try {
                    gson.fromJson(excludedAppsJson, Array<String>::class.java).toList()
                } catch (e: Exception) {
                    emptyList()
                },
                mtu = preferences[VPN_MTU] ?: 1500,
                sessionName = preferences[VPN_SESSION_NAME] ?: "CocoProxy VPN",
                appSplitConfig = appSplitConfig ?: AppSplitConfig()
            )
        }
    
    /**
     * Get app split configuration flow
     */
    val appSplitConfig: Flow<AppSplitConfig> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading app split config", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val modeString = preferences[APP_SPLIT_MODE] ?: "DISABLED"
            val selectedAppsString = preferences[APP_SPLIT_SELECTED_APPS] ?: ""
            val selectedApps = if (selectedAppsString.isNotEmpty()) {
                selectedAppsString.split(",").toSet()
            } else {
                emptySet()
            }
            
            AppSplitConfig(
                enabled = preferences[APP_SPLIT_ENABLED] ?: false,
                mode = try {
                    SplitMode.valueOf(modeString)
                } catch (e: Exception) {
                    SplitMode.DISABLED
                },
                selectedApps = selectedApps
            )
        }
    
    /**
     * Get user profile flow
     */
    val userProfile: Flow<UserProfile?> = context.dataStore.data
        .catch { exception ->
            if (exception is IOException) {
                Log.e(TAG, "Error reading user profile", exception)
                emit(emptyPreferences())
            } else {
                throw exception
            }
        }
        .map { preferences ->
            val profileJson = preferences[USER_PROFILE]
            if (profileJson != null) {
                try {
                    gson.fromJson(profileJson, UserProfile::class.java)
                } catch (e: Exception) {
                    Log.e(TAG, "Error parsing user profile", e)
                    null
                }
            } else {
                null
            }
        }
    
    /**
     * Save server configuration
     */
    suspend fun saveServerConfig(config: ServerConfig) {
        try {
            context.dataStore.edit { preferences ->
                preferences[SERVER_HOST] = config.host
                preferences[SERVER_PORT] = config.port
                preferences[SERVER_ADMIN_PORT] = config.adminPort
                preferences[SERVER_USERNAME] = config.username
                preferences[SERVER_PASSWORD] = config.password
                preferences[SERVER_TOKEN] = config.token
                preferences[SERVER_REMEMBER_ME] = config.rememberMe
            }
            Log.d(TAG, "Server config saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving server config", e)
            throw e
        }
    }
    
    /**
     * Save proxy configuration
     */
    suspend fun saveProxyConfig(config: ProxyConfig) {
        try {
            context.dataStore.edit { preferences ->
                preferences[PROXY_LOCAL_PORT] = config.localPort
                preferences[PROXY_PROTOCOL] = config.protocol
                preferences[PROXY_ENCRYPTION] = config.encryption
                preferences[PROXY_AUTO_START] = config.autoStart
                preferences[PROXY_USE_VPN] = config.useVpn
            }
            Log.d(TAG, "Proxy config saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving proxy config", e)
            throw e
        }
    }
    
    /**
     * Save UI configuration
     */
    suspend fun saveUiConfig(config: UiConfig) {
        try {
            context.dataStore.edit { preferences ->
                preferences[UI_THEME] = config.theme
                preferences[UI_LANGUAGE] = config.language
                preferences[UI_MINIMIZE_TO_TRAY] = config.minimizeToTray
                preferences[UI_START_ON_BOOT] = config.startOnBoot
                preferences[UI_CHECK_UPDATES] = config.checkUpdates
                preferences[UI_SHOW_NOTIFICATIONS] = config.showNotifications
            }
            Log.d(TAG, "UI config saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving UI config", e)
            throw e
        }
    }
    
    /**
     * Save VPN configuration
     */
    suspend fun saveVpnConfig(config: VpnConfig) {
        try {
            context.dataStore.edit { preferences ->
                preferences[VPN_ENABLED] = config.enabled
                preferences[VPN_DNS_SERVERS] = gson.toJson(config.dnsServers)
                preferences[VPN_ROUTES] = gson.toJson(config.routes)
                preferences[VPN_EXCLUDED_APPS] = gson.toJson(config.excludedApps)
                preferences[VPN_MTU] = config.mtu
                preferences[VPN_SESSION_NAME] = config.sessionName
                
                // Save app split config
                config.appSplitConfig?.let { splitConfig ->
                    preferences[APP_SPLIT_MODE] = splitConfig.mode.name
                    preferences[APP_SPLIT_SELECTED_APPS] = splitConfig.selectedApps.joinToString(",")
                    preferences[APP_SPLIT_ENABLED] = splitConfig.enabled
                }
            }
            Log.d(TAG, "VPN config saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving VPN config", e)
            throw e
        }
    }
    
    /**
     * Save app split configuration
     */
    suspend fun saveAppSplitConfig(config: AppSplitConfig) {
        try {
            context.dataStore.edit { preferences ->
                preferences[APP_SPLIT_MODE] = config.mode.name
                preferences[APP_SPLIT_SELECTED_APPS] = config.selectedApps.joinToString(",")
                preferences[APP_SPLIT_ENABLED] = config.enabled
            }
            Log.d(TAG, "App split config saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving app split config", e)
            throw e
        }
    }
    
    /**
     * Save user profile
     */
    suspend fun saveUserProfile(profile: UserProfile) {
        try {
            context.dataStore.edit { preferences ->
                preferences[USER_PROFILE] = gson.toJson(profile)
            }
            Log.d(TAG, "User profile saved successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving user profile", e)
            throw e
        }
    }
    
    /**
     * Clear user profile
     */
    suspend fun clearUserProfile() {
        try {
            context.dataStore.edit { preferences ->
                preferences.remove(USER_PROFILE)
            }
            Log.d(TAG, "User profile cleared successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing user profile", e)
            throw e
        }
    }
    
    /**
     * Clear all configuration
     */
    suspend fun clearAllConfig() {
        try {
            context.dataStore.edit { preferences ->
                preferences.clear()
            }
            Log.d(TAG, "All configuration cleared successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all configuration", e)
            throw e
        }
    }
}