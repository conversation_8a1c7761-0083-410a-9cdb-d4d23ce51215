<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">CocoProxy</string>
    <string name="app_description">Secure proxy client for Android</string>
    
    <!-- Navigation -->
    <string name="nav_home">Home</string>
    <string name="nav_traffic">Traffic</string>
    <string name="nav_settings">Settings</string>
    <string name="nav_logs">Logs</string>
    <string name="nav_about">About</string>
    
    <!-- Home Screen -->
    <string name="connection_status">Connection Status</string>
    <string name="status_disconnected">Disconnected</string>
    <string name="status_connecting">Connecting</string>
    <string name="status_connected">Connected</string>
    <string name="status_error">Error</string>
    <string name="status_reconnecting">Reconnecting</string>
    
    <string name="btn_connect">Connect</string>
    <string name="btn_disconnect">Disconnect</string>
    <string name="btn_reconnect">Reconnect</string>
    
    <string name="server_info">Server Information</string>
    <string name="proxy_info">Proxy Configuration</string>
    <string name="traffic_summary">Traffic Summary</string>
    
    <string name="server_host">Host</string>
    <string name="server_port">Port</string>
    <string name="server_username">Username</string>
    <string name="server_ssl_enabled">SSL Enabled</string>
    
    <string name="proxy_type">Type</string>
    <string name="proxy_local_port">Local Port</string>
    <string name="proxy_auth_enabled">Authentication</string>
    
    <!-- Traffic Screen -->
    <string name="traffic_upload">Upload</string>
    <string name="traffic_download">Download</string>
    <string name="traffic_total">Total</string>
    <string name="traffic_speed">Speed</string>
    <string name="traffic_connections">Connections</string>
    <string name="traffic_active">Active</string>
    <string name="traffic_total_connections">Total</string>
    
    <string name="no_active_connections">No active connections</string>
    <string name="speed_chart_title">Network Speed</string>
    
    <!-- Settings Screen -->
    <string name="settings_connection">Connection Settings</string>
    <string name="settings_server_config">Server Configuration</string>
    <string name="settings_proxy_config">Proxy Configuration</string>
    
    <string name="settings_vpn">VPN Settings</string>
    <string name="settings_auto_connect">Auto Connect</string>
    <string name="settings_auto_connect_desc">Automatically connect on app start</string>
    <string name="settings_kill_switch">Kill Switch</string>
    <string name="settings_kill_switch_desc">Block internet when VPN is disconnected</string>
    <string name="settings_ipv6_support">IPv6 Support</string>
    <string name="settings_ipv6_support_desc">Enable IPv6 traffic routing</string>
    
    <string name="settings_ui">UI Settings</string>
    <string name="settings_dark_theme">Dark Theme</string>
    <string name="settings_dark_theme_desc">Use dark color scheme</string>
    <string name="settings_dynamic_colors">Dynamic Colors</string>
    <string name="settings_dynamic_colors_desc">Use system accent colors</string>
    
    <string name="settings_notifications">Notifications</string>
    <string name="settings_connection_status">Connection Status</string>
    <string name="settings_connection_status_desc">Show connection status notifications</string>
    <string name="settings_traffic_alerts">Traffic Alerts</string>
    <string name="settings_traffic_alerts_desc">Alert when traffic limit is reached</string>
    
    <string name="settings_advanced">Advanced Settings</string>
    <string name="settings_debug_mode">Debug Mode</string>
    <string name="settings_debug_mode_desc">Enable detailed logging</string>
    <string name="settings_clear_cache">Clear Cache</string>
    <string name="settings_clear_cache_desc">Clear application cache and logs</string>
    <string name="settings_export_config">Export Configuration</string>
    <string name="settings_export_config_desc">Export settings to file</string>
    
    <string name="settings_user_profile">User Profile</string>
    
    <!-- Server Config Screen -->
    <string name="server_config_title">Server Configuration</string>
    <string name="server_config_basic">Basic Settings</string>
    <string name="server_config_ssl">SSL/TLS Settings</string>
    <string name="server_config_advanced">Advanced Settings</string>
    
    <string name="field_host">Host</string>
    <string name="field_host_hint">Enter server hostname or IP</string>
    <string name="field_port">Port</string>
    <string name="field_port_hint">Enter server port</string>
    <string name="field_username">Username</string>
    <string name="field_username_hint">Enter username</string>
    <string name="field_password">Password</string>
    <string name="field_password_hint">Enter password</string>
    
    <string name="field_use_ssl">Use SSL/TLS</string>
    <string name="field_use_ssl_desc">Enable encrypted connection</string>
    <string name="field_verify_ssl">Verify SSL Certificate</string>
    <string name="field_verify_ssl_desc">Verify server certificate</string>
    
    <string name="field_connection_timeout">Connection Timeout</string>
    <string name="field_connection_timeout_hint">Timeout in seconds</string>
    <string name="field_retry_count">Retry Count</string>
    <string name="field_retry_count_hint">Number of retry attempts</string>
    
    <string name="btn_test_connection">Test Connection</string>
    <string name="btn_save_config">Save Configuration</string>
    
    <!-- Proxy Config Screen -->
    <string name="proxy_config_title">Proxy Configuration</string>
    <string name="proxy_config_basic">Basic Settings</string>
    <string name="proxy_config_auth">Authentication</string>
    <string name="proxy_config_dns">DNS Settings</string>
    <string name="proxy_config_advanced">Advanced Settings</string>
    
    <string name="field_local_port">Local Port</string>
    <string name="field_local_port_hint">Local proxy port</string>
    <string name="field_proxy_type">Proxy Type</string>
    
    <string name="field_enable_auth">Enable Authentication</string>
    <string name="field_enable_auth_desc">Require username and password</string>
    <string name="field_auth_username">Username</string>
    <string name="field_auth_username_hint">Proxy username</string>
    <string name="field_auth_password">Password</string>
    <string name="field_auth_password_hint">Proxy password</string>
    
    <string name="field_enable_custom_dns">Enable Custom DNS</string>
    <string name="field_enable_custom_dns_desc">Use custom DNS servers</string>
    <string name="field_dns_servers">DNS Servers</string>
    <string name="field_dns_servers_hint">Comma-separated DNS servers</string>
    
    <string name="field_enable_udp">Enable UDP</string>
    <string name="field_enable_udp_desc">Support UDP traffic</string>
    <string name="field_buffer_size">Buffer Size</string>
    <string name="field_buffer_size_hint">Buffer size in KB</string>
    <string name="field_max_connections">Max Connections</string>
    <string name="field_max_connections_hint">Maximum concurrent connections</string>
    
    <!-- Logs Screen -->
    <string name="logs_title">Application Logs</string>
    <string name="logs_filter">Filter</string>
    <string name="logs_all">All</string>
    <string name="logs_error">Error</string>
    <string name="logs_warning">Warning</string>
    <string name="logs_info">Info</string>
    <string name="logs_debug">Debug</string>
    
    <string name="logs_auto_scroll">Auto Scroll</string>
    <string name="logs_clear">Clear Logs</string>
    <string name="logs_export">Export Logs</string>
    
    <string name="no_logs_available">No logs available</string>
    
    <!-- About Screen -->
    <string name="about_title">About CocoProxy</string>
    <string name="about_app_info">Application Information</string>
    <string name="about_features">Key Features</string>
    <string name="about_tech_info">Technical Information</string>
    <string name="about_links">Links</string>
    <string name="about_legal">Legal</string>
    
    <string name="about_version">Version</string>
    <string name="about_build_type">Build Type</string>
    <string name="about_target_sdk">Target SDK</string>
    <string name="about_package_name">Package Name</string>
    
    <string name="feature_secure_proxy">Secure Proxy Connection</string>
    <string name="feature_vpn_support">VPN Support</string>
    <string name="feature_traffic_monitoring">Traffic Monitoring</string>
    <string name="feature_auto_connect">Auto Connect</string>
    <string name="feature_kill_switch">Kill Switch</string>
    <string name="feature_custom_dns">Custom DNS</string>
    
    <string name="link_github">GitHub Repository</string>
    <string name="link_issues">Report Issues</string>
    <string name="link_documentation">Documentation</string>
    <string name="link_contact">Contact Us</string>
    
    <string name="legal_privacy">Privacy Policy</string>
    <string name="legal_terms">Terms of Service</string>
    <string name="legal_licenses">Open Source Licenses</string>
    
    <!-- Common -->
    <string name="btn_ok">OK</string>
    <string name="btn_cancel">Cancel</string>
    <string name="btn_save">Save</string>
    <string name="btn_reset">Reset</string>
    <string name="btn_close">Close</string>
    <string name="btn_retry">Retry</string>
    
    <string name="loading">Loading...</string>
    <string name="error_occurred">An error occurred</string>
    <string name="success">Success</string>
    <string name="failed">Failed</string>
    
    <!-- Units -->
    <string name="unit_bytes">B</string>
    <string name="unit_kb">KB</string>
    <string name="unit_mb">MB</string>
    <string name="unit_gb">GB</string>
    <string name="unit_kbps">KB/s</string>
    <string name="unit_mbps">MB/s</string>
    
    <!-- Notifications -->
    <string name="notification_channel_vpn">VPN Service</string>
    <string name="notification_channel_proxy">Proxy Service</string>
    <string name="notification_channel_general">General</string>
    
    <string name="notification_vpn_connected">VPN Connected</string>
    <string name="notification_vpn_disconnected">VPN Disconnected</string>
    <string name="notification_proxy_running">Proxy Running</string>
    <string name="notification_proxy_stopped">Proxy Stopped</string>
    
    <!-- Errors -->
    <string name="error_connection_failed">Connection failed</string>
    <string name="error_authentication_failed">Authentication failed</string>
    <string name="error_server_unreachable">Server unreachable</string>
    <string name="error_invalid_config">Invalid configuration</string>
    <string name="error_vpn_permission_denied">VPN permission denied</string>
    <string name="error_network_unavailable">Network unavailable</string>
    
    <!-- VPN -->
    <string name="vpn_permission_title">VPN Permission Required</string>
    <string name="vpn_permission_message">CocoProxy needs VPN permission to route traffic securely. This permission allows the app to create a VPN connection.</string>
    <string name="vpn_permission_grant">Grant Permission</string>
    <string name="vpn_permission_deny">Deny</string>
    
    <string name="vpn_connected">VPN Connected</string>
    <string name="vpn_disconnected">VPN Disconnected</string>
    <string name="vpn_connecting">VPN Connecting</string>
    <string name="vpn_error">VPN Error</string>
</resources>