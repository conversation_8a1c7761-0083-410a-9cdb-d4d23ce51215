package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"database/sql"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"net"
	"strings"
	"time"

	_ "github.com/gin-gonic/gin"
	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/crypto/chacha20poly1305"
)

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

const (
	SERVER_HOST = "0.0.0.0"
	SERVER_PORT = "28888"
	SERVER_TYPE = "tcp"
	UDP_PORT    = "28889"
)

var (
	db              *sql.DB
	userManager     *UserManager
	serverStartTime time.Time
)

// 加密类型
const (
	ENCRYPT_NONE = iota
	ENCRYPT_AES
	ENCRYPT_CHACHA20
)

// 协议类型
const (
	PROTOCOL_HTTP_HTTPS byte = 0x01
	PROTOCOL_TCP        byte = 0x02
	PROTOCOL_SOCKS5     byte = 0x03
)

// 安全限制
const (
	MAX_DATA_LENGTH     = 10 * 1024 * 1024  // 10MB 最大数据长度
	MAX_USERNAME_LENGTH = 255               // 最大用户名长度
)

func main() {
	serverStartTime = time.Now()
	fmt.Println("Starting " + SERVER_TYPE + " server on " + SERVER_HOST + ":" + SERVER_PORT)

	listener, err := net.Listen(SERVER_TYPE, SERVER_HOST+":"+SERVER_PORT)
	if err != nil {
		log.Fatal("Error starting server:", err)
	}
	defer listener.Close()

	db, err = InitDB("./coco.db")
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	userManager = NewUserManager(db)

	// Add a default admin user if not exists
	// Check if admin user exists
	existingAdmin, err := userManager.GetUser("admin")
	if err != nil {
		log.Fatalf("Failed to check for default admin user: %v", err)
	}

	if existingAdmin == nil {
		// Create default admin user with password "admin123"
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
		if err != nil {
			log.Fatalf("Failed to hash default admin password: %v", err)
		}

		adminUser := User{
			Username:     "admin",
			PasswordHash: string(hashedPassword),
			EncryptKey:   "your-secret-key-16", // Replace with a strong 16-byte key
			IsAdmin:      true,
		}

		err = AddUser(db, &adminUser)
		if err != nil {
			log.Fatalf("Failed to add default admin user: %v", err)
		}
		log.Println("Default admin user added. Username: admin, Password: admin123")
	}

	// 启动管理界面服务器
	adminRouter := SetupAdminUI()
	adminPort := "28080" // 管理界面端口
	log.Printf("Admin UI server starting on :%s", adminPort)

	// 在单独的goroutine中启动代理服务器
	go func() {
		fmt.Println("Proxy server started, waiting for clients...")
		for {
			conn, err := listener.Accept()
			if err != nil {
				log.Printf("Error accepting: %v", err)
				continue
			}
			go handleConnection(conn)
		}
	}()

	// 在主goroutine中启动管理界面服务器
	fmt.Println("Admin UI available at: http://localhost:28080")
	if err := adminRouter.Run(":" + adminPort); err != nil {
		log.Fatalf("Admin UI server failed to start: %v", err)
	}

	// 主goroutine现在运行管理界面服务器
}

// 简单的密钥，实际应用中需要更安全的密钥管理
var aesKey = []byte("thisisasecretkey16") // 16, 24, or 32 bytes for AES-128, AES-192, or AES-256
var chachaKey = make([]byte, 32)          // 32 bytes for ChaCha20-Poly1305 key

func init() {
	// 随机生成ChaCha20密钥，实际应用中应从安全配置中读取
	if _, err := rand.Read(chachaKey); err != nil {
		log.Fatalf("Failed to generate ChaCha20 key: %v", err)
	}
}

// readRemainingData 从连接中读取剩余数据并解密
func readRemainingData(conn net.Conn) (protocolType byte, encryptType byte, username string, decryptedData []byte, err error) {
	// 读取混淆协议头
	// 新协议头结构: 1字节协议类型 + 1字节加密类型 + 1字节用户名长度 + N字节用户名 + 4字节随机填充 + 4字节数据长度

	// 先读取前3字节获取用户名长度
	headerPrefix := make([]byte, 3)
	_, err = io.ReadFull(conn, headerPrefix)
	if err != nil {
		log.Printf("failed to read confused header prefix: %v", err)
		return
	}

	protocolType = headerPrefix[0]
	encryptType = headerPrefix[1]
	usernameLen := int(headerPrefix[2])

	// 验证用户名长度
	if usernameLen > MAX_USERNAME_LENGTH {
		return protocolType, encryptType, "", nil, fmt.Errorf("username length too large: %d > %d", usernameLen, MAX_USERNAME_LENGTH)
	}

	usernameBytes := make([]byte, usernameLen)
	_, err = io.ReadFull(conn, usernameBytes)
	if err != nil {
		log.Printf("failed to read username: %v", err)
		return
	}
	username = string(usernameBytes)

	// 读取剩余部分(4字节随机填充 + 4字节数据长度)
	remainingHeader := make([]byte, 8)
	_, err = io.ReadFull(conn, remainingHeader)
	if err != nil {
		log.Printf("failed to read remaining header: %v", err)
		return
	}

	dataLen := binary.BigEndian.Uint32(remainingHeader[4:8])

	// 验证数据长度，防止内存溢出攻击
	if dataLen > MAX_DATA_LENGTH {
		return protocolType, encryptType, username, nil, fmt.Errorf("data length too large: %d > %d", dataLen, MAX_DATA_LENGTH)
	}

	encryptedData := make([]byte, dataLen)
	_, err = io.ReadFull(conn, encryptedData)
	if err != nil {
		return protocolType, encryptType, username, nil, fmt.Errorf("failed to read encrypted data: %w", err)
	}

	switch encryptType {
	case ENCRYPT_NONE:
		return protocolType, encryptType, username, encryptedData, nil
	case ENCRYPT_AES:
		decryptedData, decErr := decryptAES(encryptedData)
		if decErr != nil {
			return protocolType, encryptType, username, nil, decErr
		}
		return protocolType, encryptType, username, decryptedData, nil
	case ENCRYPT_CHACHA20:
		decryptedData, decErr := decryptChaCha20(encryptedData)
		if decErr != nil {
			return protocolType, encryptType, username, nil, decErr
		}
		return protocolType, encryptType, username, decryptedData, nil
	default:
		return protocolType, encryptType, username, nil, fmt.Errorf("unknown encryption type: %d", encryptType)
	}
}

// encryptAES 使用AES加密数据
func encryptAES(plaintext []byte) ([]byte, error) {
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// GCM模式需要一个nonce，这里使用随机生成的nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

// decryptAES 使用AES解密数据
func decryptAES(ciphertext []byte) ([]byte, error) {
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return nil, err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, encryptedMessage := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, encryptedMessage, nil)
	if err != nil {
		return nil, err
	}
	return plaintext, nil
}

// encryptChaCha20 使用ChaCha20-Poly1305加密数据
func encryptChaCha20(plaintext []byte) ([]byte, error) {
	aead, err := chacha20poly1305.New(chachaKey)
	if err != nil {
		return nil, err
	}

	nonce := make([]byte, aead.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, err
	}

	ciphertext := aead.Seal(nonce, nonce, plaintext, nil)
	return ciphertext, nil
}

// decryptChaCha20 使用ChaCha20-Poly1305解密数据
func decryptChaCha20(ciphertext []byte) ([]byte, error) {
	aead, err := chacha20poly1305.New(chachaKey)
	if err != nil {
		return nil, err
	}

	nonceSize := aead.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	nonce, encryptedMessage := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := aead.Open(nil, nonce, encryptedMessage, nil)
	if err != nil {
		return nil, err
	}
	return plaintext, nil
}

// handleTCPProxy 处理通用TCP代理请求
// encryptData 根据加密类型加密数据
func encryptData(plaintext []byte, encryptType byte) ([]byte, error) {
	switch encryptType {
	case ENCRYPT_NONE:
		return plaintext, nil
	case ENCRYPT_AES:
		return encryptAES(plaintext)
	case ENCRYPT_CHACHA20:
		return encryptChaCha20(plaintext)
	default:
		return nil, fmt.Errorf("unknown encryption type: %d", encryptType)
	}
}

// handleTCPProxy 处理通用TCP代理请求 - 修复版本
func handleTCPProxy(clientConn net.Conn, targetData []byte, username string) {
	// 解析目标地址，格式为 "host:port"
	targetAddr := string(targetData)
	
	log.Printf("TCP Proxy: Connecting to %s for user %s", targetAddr, username)

	// 连接到目标服务器
	serverConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		log.Printf("Error connecting to target %s: %v", targetAddr, err)
		clientConn.Write([]byte(fmt.Sprintf("Error: Failed to connect to %s", targetAddr)))
		return
	}
	defer serverConn.Close()

	log.Printf("TCP Proxy: Successfully connected to %s", targetAddr)

	// 双向数据转发
	go func() {
		defer func() {
			if tcpConn, ok := serverConn.(*net.TCPConn); ok {
				tcpConn.CloseWrite()
			}
		}()
		
		_, err := io.Copy(serverConn, clientConn)
		if err != nil && err != io.EOF {
			log.Printf("Error copying from client to server: %v", err)
		}
	}()

	_, err = io.Copy(clientConn, serverConn)
	if err != nil && err != io.EOF {
		log.Printf("Error copying from server to client: %v", err)
	}
	
	if tcpConn, ok := clientConn.(*net.TCPConn); ok {
		tcpConn.CloseWrite()
	}
	
	log.Printf("TCP Proxy: Connection to %s ended", targetAddr)
}

// handleCocoProxySOCKS5 处理通过CocoProxy协议传输的SOCKS5请求
func handleCocoProxySOCKS5(clientConn net.Conn, initialData []byte, username string) {
	log.Printf("CocoProxy SOCKS5: Handling SOCKS5 request for user %s", username)
	
	// 对于CocoProxy SOCKS5，我们需要处理标准的SOCKS5握手过程
	// 首先读取客户端的SOCKS5认证请求
	authBuffer := make([]byte, 1024)
	authN, err := clientConn.Read(authBuffer)
	if err != nil {
		log.Printf("CocoProxy SOCKS5: Failed to read auth request: %v", err)
		return
	}
	
	log.Printf("CocoProxy SOCKS5: Received auth request %d bytes: %x", authN, authBuffer[:authN])
	
	// 验证SOCKS5认证请求格式
	if authN < 3 || authBuffer[0] != 0x05 {
		log.Printf("CocoProxy SOCKS5: Invalid auth request")
		return
	}
	
	// 发送SOCKS5握手响应：接受无认证方法
	clientConn.Write([]byte{0x05, 0x00}) // VER=5, METHOD=0 (no auth)
	
	// 现在等待SOCKS5连接请求
	buffer := make([]byte, 1024)
	n, err := clientConn.Read(buffer)
	if err != nil {
		log.Printf("CocoProxy SOCKS5: Failed to read connect request: %v", err)
		return
	}
	
	log.Printf("CocoProxy SOCKS5: Received %d bytes: %x", n, buffer[:n])
	
	// 解析SOCKS5连接请求
	if n < 4 {
		log.Printf("CocoProxy SOCKS5: Connect request too short: %d bytes", n)
		return
	}
	
	if buffer[0] != 0x05 {
		log.Printf("CocoProxy SOCKS5: Invalid SOCKS version: %d", buffer[0])
		return
	}
	
	if buffer[2] != 0x00 {
		log.Printf("CocoProxy SOCKS5: Invalid RSV field: %d", buffer[2])
		return
	}
	
	cmd := buffer[1]
	atyp := buffer[3]
	
	var remoteAddr string
	var remotePort string
	offset := 4
	
	switch atyp {
	case 0x01: // IPv4
		if n < offset+4 {
			log.Printf("CocoProxy SOCKS5: Incomplete IPv4 address")
			return
		}
		remoteAddr = net.IPv4(buffer[offset], buffer[offset+1], buffer[offset+2], buffer[offset+3]).String()
		offset += 4
	case 0x03: // Domain name
		if n < offset+1 {
			log.Printf("CocoProxy SOCKS5: Missing domain length")
			return
		}
		domainLen := int(buffer[offset])
		offset++
		if n < offset+domainLen {
			log.Printf("CocoProxy SOCKS5: Incomplete domain name")
			return
		}
		remoteAddr = string(buffer[offset:offset+domainLen])
		offset += domainLen
	case 0x04: // IPv6
		if n < offset+16 {
			log.Printf("CocoProxy SOCKS5: Incomplete IPv6 address")
			return
		}
		remoteAddr = net.IP(buffer[offset:offset+16]).String()
		offset += 16
	default:
		log.Printf("CocoProxy SOCKS5: Unsupported ATYP: %x", atyp)
		clientConn.Write([]byte{0x05, 0x08, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Address type not supported
		return
	}
	
	if n < offset+2 {
		log.Printf("CocoProxy SOCKS5: Missing port")
		return
	}
	remotePort = fmt.Sprintf("%d", binary.BigEndian.Uint16(buffer[offset:offset+2]))
	
	log.Printf("CocoProxy SOCKS5: Connecting to %s:%s for user %s", remoteAddr, remotePort, username)
	
	switch cmd {
	case 0x01: // CONNECT
		serverConn, err := net.Dial("tcp", net.JoinHostPort(remoteAddr, remotePort))
		if err != nil {
			log.Printf("CocoProxy SOCKS5: Failed to connect to target %s:%s: %v", remoteAddr, remotePort, err)
			clientConn.Write([]byte{0x05, 0x05, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Connection refused
			return
		}
		defer serverConn.Close()
		
		log.Printf("CocoProxy SOCKS5: Successfully connected to %s:%s", remoteAddr, remotePort)
		
		// 发送成功响应
		localAddr := serverConn.LocalAddr().(*net.TCPAddr)
		bndAddr := localAddr.IP.To4()
		if bndAddr == nil {
			bndAddr = []byte{0x00, 0x00, 0x00, 0x00} // 使用0.0.0.0作为默认
		}
		bndPort := make([]byte, 2)
		binary.BigEndian.PutUint16(bndPort, uint16(localAddr.Port))
		
		resp := []byte{0x05, 0x00, 0x00, 0x01} // VER, REP (Success), RSV, ATYP (IPv4)
		resp = append(resp, bndAddr...)
		resp = append(resp, bndPort...)
		clientConn.Write(resp)
		
		// 双向数据转发
		go func() {
			_, err := io.Copy(serverConn, clientConn)
			if err != nil && err != io.EOF {
				log.Printf("CocoProxy SOCKS5: Error copying from client to server: %v", err)
			}
			if tcpConn, ok := serverConn.(*net.TCPConn); ok {
				tcpConn.CloseWrite()
			}
		}()
		
		_, err = io.Copy(clientConn, serverConn)
		if err != nil && err != io.EOF {
			log.Printf("CocoProxy SOCKS5: Error copying from server to client: %v", err)
		}
		if tcpConn, ok := clientConn.(*net.TCPConn); ok {
			tcpConn.CloseWrite()
		}
		
		log.Printf("CocoProxy SOCKS5: Connection to %s:%s ended", remoteAddr, remotePort)
	default:
		log.Printf("CocoProxy SOCKS5: Unsupported command: %x", cmd)
		clientConn.Write([]byte{0x05, 0x07, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Command not supported
	}
}

// handleSOCKS5Proxy 处理原始SOCKS5代理请求（保留用于直接SOCKS5连接）
func handleSOCKS5Proxy(clientConn net.Conn) {
	// SOCKS5 握手第一阶段：客户端发送问候
	// +----+----------+----------+
	// | VER| NMETHODS | METHODS  |
	// +----+----------+----------+
	// | 1  |    1     | 1 to 255 |
	// +----+----------+----------+
	buffer := make([]byte, 257) // VER + NMETHODS + METHODS
	_, err := io.ReadFull(clientConn, buffer[:2])
	if err != nil || buffer[0] != 0x05 {
		log.Printf("SOCKS5: Invalid VER/NMETHODS or read error: %v", err)
		return
	}
	numMethods := int(buffer[1])
	_, err = io.ReadFull(clientConn, buffer[2:2+numMethods])
	if err != nil {
		log.Printf("SOCKS5: Failed to read methods: %v", err)
		return
	}

	// SOCKS5 握手第二阶段：服务端选择认证方法
	// +----+--------+
	// | VER| METHOD |
	// +----+--------+
	// | 1  |   1    |
	// +----+--------+
	// 0x00: 无需认证
	// 0xFF: 无可接受方法
	clientConn.Write([]byte{0x05, 0x00}) // 接受无认证方法

	// SOCKS5 握手第三阶段：客户端发送连接请求
	// +----+-----+-------+------+----------+----------+
	// | VER| CMD |  RSV  | ATYP | DST.ADDR | DST.PORT |
	// +----+-----+-------+------+----------+----------+
	// | 1  |  1  | X'00' |  1   | Variable |    2     |
	// +----+-----+-------+------+----------+----------+
	_, err = io.ReadFull(clientConn, buffer[:4]) // VER, CMD, RSV, ATYP
	if err != nil || buffer[0] != 0x05 || buffer[2] != 0x00 {
		log.Printf("SOCKS5: Invalid VER/CMD/RSV or read error: %v", err)
		return
	}

	cmd := buffer[1]
	atyp := buffer[3]

	var remoteAddr string
	var remotePort string

	switch atyp {
	case 0x01: // IPv4
		_, err = io.ReadFull(clientConn, buffer[:4]) // DST.ADDR
		if err != nil {
			log.Printf("SOCKS5: Failed to read IPv4 address: %v", err)
			return
		}
		remoteAddr = net.IPv4(buffer[0], buffer[1], buffer[2], buffer[3]).String()
	case 0x03: // Domain name
		_, err = io.ReadFull(clientConn, buffer[:1]) // LEN
		if err != nil {
			log.Printf("SOCKS5: Failed to read domain length: %v", err)
			return
		}
		domainLen := int(buffer[0])
		_, err = io.ReadFull(clientConn, buffer[:domainLen]) // DST.ADDR
		if err != nil {
			log.Printf("SOCKS5: Failed to read domain name: %v", err)
			return
		}
		remoteAddr = string(buffer[:domainLen])
	case 0x04: // IPv6
		_, err = io.ReadFull(clientConn, buffer[:16]) // DST.ADDR
		if err != nil {
			log.Printf("SOCKS5: Failed to read IPv6 address: %v", err)
			return
		}
		remoteAddr = net.IP(buffer[:16]).String()
	default:
		log.Printf("SOCKS5: Unsupported ATYP: %x", atyp)
		clientConn.Write([]byte{0x05, 0x08, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Command not supported
		return
	}

	_, err = io.ReadFull(clientConn, buffer[:2]) // DST.PORT
	if err != nil {
		log.Printf("SOCKS5: Failed to read port: %v", err)
		return
	}
	remotePort = fmt.Sprintf("%d", binary.BigEndian.Uint16(buffer[:2]))

	// SOCKS5 握手第四阶段：服务端发送响应
	// +----+-----+-------+------+----------+----------+
	// | VER| REP |  RSV  | ATYP | BND.ADDR | BND.PORT |
	// +----+-----+-------+------+----------+----------+\n	// | 1  |  1  | X'00' |  1   | Variable |    2     |
	// +----+-----+-------+------+----------+----------+

	switch cmd {
	case 0x01: // CONNECT
		serverConn, err := net.Dial(SERVER_TYPE, net.JoinHostPort(remoteAddr, remotePort))
		if err != nil {
			log.Printf("SOCKS5: Failed to connect to target %s:%s: %v", remoteAddr, remotePort, err)
			clientConn.Write([]byte{0x05, 0x05, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Connection refused
			return
		}
		defer serverConn.Close()

		localAddr := serverConn.LocalAddr().(*net.TCPAddr)
		bndAddr := localAddr.IP.To4()
		if bndAddr == nil {
			bndAddr = localAddr.IP // IPv6
		}
		bndPort := make([]byte, 2)
		binary.BigEndian.PutUint16(bndPort, uint16(localAddr.Port))

		resp := []byte{0x05, 0x00, 0x00, 0x01} // VER, REP (Success), RSV, ATYP (IPv4)
		resp = append(resp, bndAddr...)
		resp = append(resp, bndPort...)
		clientConn.Write(resp)

		// 双向数据转发
		go func() {
			_, err := io.Copy(serverConn, clientConn)
			if err != nil && err != io.EOF {
				log.Printf("SOCKS5: Error copying from client to server: %v", err)
			}
			if tcpConn, ok := serverConn.(*net.TCPConn); ok {
				tcpConn.CloseWrite()
			}
		}()

		_, err = io.Copy(clientConn, serverConn)
		if err != nil && err != io.EOF {
			log.Printf("SOCKS5: Error copying from server to client: %v", err)
		}
		if tcpConn, ok := clientConn.(*net.TCPConn); ok {
			tcpConn.CloseWrite()
		}
	case 0x02: // BIND
		log.Printf("SOCKS5: BIND command not supported")
		clientConn.Write([]byte{0x05, 0x07, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Command not supported
	case 0x03: // UDP ASSOCIATE
		log.Printf("SOCKS5: UDP ASSOCIATE command not supported")
		clientConn.Write([]byte{0x05, 0x07, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Command not supported
	default:
		log.Printf("SOCKS5: Unknown command: %x", cmd)
		clientConn.Write([]byte{0x05, 0x07, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}) // Command not supported
	}
}

func handleConnection(clientConn net.Conn) {
	defer clientConn.Close()

	// 读取混淆协议头，获取协议类型、加密类型、用户名和解密后的初始数据
	protocolType, _, username, initialDecryptedData, err := readRemainingData(clientConn)
	if err != nil {
		log.Printf("failed to read initial data: %v", err)
		return
	}

	// 根据用户名获取用户
	user, err := userManager.GetUser(username)
	if err != nil || user == nil {
		log.Printf("Authentication failed: User %s not found or error: %v", username, err)
		clientConn.Write([]byte("Authentication failed: User not found"))
		return
	}

	// 使用用户的加密密钥更新全局密钥
	aesKey = []byte(user.EncryptKey)
	chachaKey = []byte(user.EncryptKey)
	if len(chachaKey) != 32 {
		log.Printf("Warning: ChaCha20 key for user %s is not 32 bytes. Adjusting or using default.", user.Username)
		newChaChaKey := make([]byte, 32)
		copy(newChaChaKey, []byte(user.EncryptKey))
		chachaKey = newChaChaKey
	}

	// TODO: Implement proper password hash comparison
	// For demonstration, we'll just check if the user exists.

	// TODO: Implement traffic usage tracking and limit enforcement
	// userManager.RecordTraffic(user.ID, trafficDelta)

	// 根据协议类型处理
	switch protocolType {
	case PROTOCOL_HTTP_HTTPS: // HTTP/HTTPS
		handleHTTPProxy(clientConn, initialDecryptedData, username)
	case PROTOCOL_TCP:
		go handleTCPProxy(clientConn, initialDecryptedData, username)
	case PROTOCOL_SOCKS5:
		handleCocoProxySOCKS5(clientConn, initialDecryptedData, username)
	default:
		log.Printf("Unsupported protocol type: %d from %s", protocolType, clientConn.RemoteAddr())
		clientConn.Write([]byte("Unsupported protocol type"))
	}

}

func handleHTTPProxy(clientConn net.Conn, initialDecryptedData []byte, username string) {
	// Parse the request to find the target host and port
	requestString := string(initialDecryptedData)
	var host string
	var port string = "80" // Default HTTP port

	log.Printf("Parsing HTTP request (%d bytes) from %s", len(initialDecryptedData), clientConn.RemoteAddr())
	log.Printf("Request data: %s", requestString[:min(200, len(requestString))])

	if len(requestString) > 0 {
		lines := strings.Split(requestString, "\r\n")
		if len(lines) > 0 {
			firstLine := lines[0]
			log.Printf("First line: %s", firstLine)
			
			if strings.HasPrefix(firstLine, "CONNECT") {
				// HTTPS CONNECT request
				parts := strings.Split(firstLine, " ")
				if len(parts) > 1 {
					hostPort := parts[1]
					hostParts := strings.Split(hostPort, ":")
					host = hostParts[0]
					if len(hostParts) > 1 {
						port = hostParts[1]
					} else {
						port = "443" // Default HTTPS port
					}
					log.Printf("CONNECT request to %s:%s", host, port)
				}
				// For CocoProxy protocol, don't send HTTP response here
				// The acknowledgment will be sent after establishing the connection
			} else {
				// Regular HTTP GET/POST etc.
				log.Printf("Parsing HTTP headers from %d lines", len(lines))
				for i, line := range lines {
					log.Printf("Line %d: %s", i, line)
					if strings.HasPrefix(strings.ToLower(line), "host:") {
						hostValue := strings.TrimSpace(line[5:]) // Remove "Host:" prefix
						log.Printf("Found Host header: %s", hostValue)
						// Check if port is specified in Host header
						hostParts := strings.Split(hostValue, ":")
						host = hostParts[0]
						if len(hostParts) > 1 {
							port = hostParts[1]
						}
						log.Printf("Extracted host: %s, port: %s", host, port)
						break
					}
				}
			}
		}
	}

	if host == "" {
		log.Printf("Could not determine host from request from %s", clientConn.RemoteAddr())
		log.Printf("Request content: %s", requestString)
		// Send error acknowledgment to client
		clientConn.Write([]byte{0x01}) // Error status
		return
	}

	remoteAddr := net.JoinHostPort(host, port)
	log.Printf("Proxying connection from %s to %s", clientConn.RemoteAddr(), remoteAddr)

	// Establish connection to the target server
	serverConn, err := net.Dial(SERVER_TYPE, remoteAddr)
	if err != nil {
		log.Printf("Error connecting to target %s: %v", remoteAddr, err)
		// Send error acknowledgment to client
		clientConn.Write([]byte{0x02}) // Connection error status
		return
	}
	defer serverConn.Close()

	// Send success acknowledgment to client
	clientConn.Write([]byte{0x00}) // Success status
	log.Printf("Sent acknowledgment to client for %s", remoteAddr)

	// Forward initial data to the target server for HTTP GET/POST etc.
	// For CONNECT, the initial data (CONNECT request line) has already been handled.
	// Subsequent data is the actual TLS handshake.
	if !strings.HasPrefix(requestString, "CONNECT") {
		// Send original HTTP request directly to target server (no encryption)
		_, err = serverConn.Write(initialDecryptedData)
		if err != nil {
			log.Printf("Error writing initial HTTP request to target: %v", err)
			return
		}
		log.Printf("Forwarded initial HTTP request to %s", remoteAddr)
	}

	// Bidirectional copy
	// Client to Server
	go func() {
		// Read from client and send directly to target server (no encryption)
		buf := make([]byte, 4096)
		for {
			n, err := clientConn.Read(buf)
			if n > 0 {
				// Forward data directly to target server
				_, writeErr := serverConn.Write(buf[:n])
				if writeErr != nil {
					log.Printf("Error writing data to target: %v", writeErr)
					break
				}
			}
			if err != nil {
				if err != io.EOF {
					log.Printf("Error reading from client: %v", err)
				}
				break
			}
		}
		// Close serverConn write end when clientConn closes or error occurs
		// This signals the server that no more data is coming from the client
		if tcpConn, ok := serverConn.(*net.TCPConn); ok {
			tcpConn.CloseWrite()
		}
	}()

	// Server to Client
	go func() {
		// Read from target server and send directly to client (no encryption)
		buf := make([]byte, 4096)
		for {
			n, err := serverConn.Read(buf)
			if n > 0 {
				// Forward data directly to client
				_, writeErr := clientConn.Write(buf[:n])
				if writeErr != nil {
					log.Printf("Error writing data to client: %v", writeErr)
					break
				}
			}
			if err != nil {
				if err != io.EOF {
					log.Printf("Error reading from target server: %v", err)
				}
				break
			}
		}
		// Close clientConn write end when serverConn closes or error occurs
		// This signals the client that no more data is coming from the server
		if tcpConn, ok := clientConn.(*net.TCPConn); ok {
			tcpConn.CloseWrite()
		}
	}()

	// Wait for both goroutines to finish
	select {}
}
