package com.cocoproxy.client.data.api;

/**
 * CocoProxy API interface for HTTP communication with server
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0007J\u001e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u00032\b\b\u0001\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001e\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u00032\b\b\u0001\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u001e\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\b\b\u0001\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\f\u00a8\u0006\u0011\u00c0\u0006\u0003"}, d2 = {"Lcom/cocoproxy/client/data/api/CocoProxyApi;", "", "login", "Lretrofit2/Response;", "Lcom/cocoproxy/client/data/api/LoginResponse;", "request", "Lcom/cocoproxy/client/data/api/LoginRequest;", "(Lcom/cocoproxy/client/data/api/LoginRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatus", "Lcom/cocoproxy/client/data/api/ServerStatusResponse;", "token", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserProfile", "Lcom/cocoproxy/client/data/api/UserProfileResponse;", "logout", "Lcom/cocoproxy/client/data/api/LogoutResponse;", "app_debug"})
public abstract interface CocoProxyApi {
    
    /**
     * Login to the server
     */
    @retrofit2.http.POST(value = "/api/login")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object login(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.api.LoginRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.cocoproxy.client.data.api.LoginResponse>> $completion);
    
    /**
     * Get server status
     */
    @retrofit2.http.GET(value = "/api/status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStatus(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String token, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.cocoproxy.client.data.api.ServerStatusResponse>> $completion);
    
    /**
     * Get user profile
     */
    @retrofit2.http.GET(value = "/api/me")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserProfile(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String token, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.cocoproxy.client.data.api.UserProfileResponse>> $completion);
    
    /**
     * Logout from server
     */
    @retrofit2.http.POST(value = "/api/logout")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object logout(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String token, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.cocoproxy.client.data.api.LogoutResponse>> $completion);
}