package com.cocoproxy.client.data.repository

import android.content.Context
import com.cocoproxy.client.core.AppManager
import com.cocoproxy.client.data.model.AppTrafficStats
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TrafficRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val appManager: AppManager
) {
    
    private val _trafficUpdates = MutableSharedFlow<Unit>()
    val trafficUpdates: Flow<Unit> = _trafficUpdates.asSharedFlow()
    
    // 内存中的流量统计数据
    private val trafficStatsMap = mutableMapOf<String, AppTrafficStats>()
    
    /**
     * 获取所有应用的流量统计
     */
    suspend fun getAppTrafficStats(): List<AppTrafficStats> {
        // 获取已安装的应用列表
        val installedApps = appManager.getInstalledApps(includeSystemApps = true)
        
        // 为每个应用创建或更新流量统计
        val stats = installedApps.map { app ->
            trafficStatsMap.getOrPut(app.packageName) {
                AppTrafficStats(
                    packageName = app.packageName,
                    appName = app.appName,
                    uploadBytes = 0L,
                    downloadBytes = 0L,
                    isProxied = false, // 这里需要根据实际的分流配置来确定
                    connectionsCount = 0L,
                    lastActiveTime = 0L,
                    isActive = false
                )
            }
        }
        
        return stats.sortedByDescending { it.totalBytes }
    }
    
    /**
     * 更新应用流量统计
     */
    fun updateAppTraffic(
        packageName: String,
        uploadBytes: Long,
        downloadBytes: Long,
        isProxied: Boolean
    ) {
        val currentStats = trafficStatsMap[packageName]
        val updatedStats = if (currentStats != null) {
            currentStats.copy(
                uploadBytes = currentStats.uploadBytes + uploadBytes,
                downloadBytes = currentStats.downloadBytes + downloadBytes,
                isProxied = isProxied,
                lastActiveTime = System.currentTimeMillis(),
                isActive = true,
                connectionsCount = currentStats.connectionsCount + 1
            )
        } else {
            AppTrafficStats(
                packageName = packageName,
                appName = getAppName(packageName),
                uploadBytes = uploadBytes,
                downloadBytes = downloadBytes,
                isProxied = isProxied,
                connectionsCount = 1,
                lastActiveTime = System.currentTimeMillis(),
                isActive = true
            )
        }
        
        trafficStatsMap[packageName] = updatedStats
        
        // 通知更新
        _trafficUpdates.tryEmit(Unit)
    }
    
    /**
     * 获取指定应用的流量统计
     */
    fun getAppTrafficStats(packageName: String): AppTrafficStats? {
        return trafficStatsMap[packageName]
    }
    
    /**
     * 清除所有流量统计
     */
    fun clearTrafficStats() {
        trafficStatsMap.clear()
        _trafficUpdates.tryEmit(Unit)
    }
    
    /**
     * 清除指定应用的流量统计
     */
    fun clearAppTrafficStats(packageName: String) {
        trafficStatsMap.remove(packageName)
        _trafficUpdates.tryEmit(Unit)
    }
    
    /**
     * 获取总上传流量
     */
    fun getTotalUploadBytes(): Long {
        return trafficStatsMap.values.sumOf { it.uploadBytes }
    }
    
    /**
     * 获取总下载流量
     */
    fun getTotalDownloadBytes(): Long {
        return trafficStatsMap.values.sumOf { it.downloadBytes }
    }
    
    /**
     * 获取总流量
     */
    fun getTotalBytes(): Long {
        return getTotalUploadBytes() + getTotalDownloadBytes()
    }
    
    /**
     * 获取代理应用的流量统计
     */
    fun getProxiedAppsTraffic(): List<AppTrafficStats> {
        return trafficStatsMap.values.filter { it.isProxied }
    }
    
    /**
     * 获取直连应用的流量统计
     */
    fun getDirectAppsTraffic(): List<AppTrafficStats> {
        return trafficStatsMap.values.filter { !it.isProxied }
    }
    
    /**
     * 根据包名获取应用名称
     */
    private fun getAppName(packageName: String): String {
        return try {
            val packageManager = context.packageManager
            val appInfo = packageManager.getApplicationInfo(packageName, 0)
            appInfo.loadLabel(packageManager).toString()
        } catch (e: Exception) {
            packageName
        }
    }
    
    /**
     * 模拟流量数据（用于测试）
     */
    fun generateMockTrafficData() {
        val mockData = listOf(
            Triple("com.android.chrome", 1024 * 1024 * 50L, 1024 * 1024 * 200L), // Chrome: 50MB up, 200MB down
            Triple("com.whatsapp", 1024 * 1024 * 10L, 1024 * 1024 * 30L), // WhatsApp: 10MB up, 30MB down
            Triple("com.instagram.android", 1024 * 1024 * 20L, 1024 * 1024 * 100L), // Instagram: 20MB up, 100MB down
            Triple("com.spotify.music", 1024 * 1024 * 5L, 1024 * 1024 * 150L), // Spotify: 5MB up, 150MB down
            Triple("com.netflix.mediaclient", 1024 * 1024 * 2L, 1024 * 1024 * 500L), // Netflix: 2MB up, 500MB down
        )
        
        mockData.forEach { (packageName, upload, download) ->
            updateAppTraffic(packageName, upload, download, true)
        }
    }
}