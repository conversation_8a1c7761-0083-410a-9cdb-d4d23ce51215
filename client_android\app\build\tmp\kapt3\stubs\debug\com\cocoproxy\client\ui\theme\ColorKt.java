package com.cocoproxy.client.ui.theme;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\u0012\n\u0000\n\u0002\u0018\u0002\n\u0002\bT\n\u0002\u0010 \n\u0002\b\u0003\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010\'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\"\u0013\u0010I\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bJ\u0010\u0003\"\u0013\u0010K\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bL\u0010\u0003\"\u0013\u0010M\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bN\u0010\u0003\"\u0013\u0010O\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bP\u0010\u0003\"\u0013\u0010Q\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bR\u0010\u0003\"\u0013\u0010S\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bT\u0010\u0003\"\u0017\u0010U\u001a\b\u0012\u0004\u0012\u00020\u00010V\u00a2\u0006\b\n\u0000\u001a\u0004\bW\u0010X\u00a8\u0006Y"}, d2 = {"Purple80", "Landroidx/compose/ui/graphics/Color;", "getPurple80", "()J", "J", "PurpleGrey80", "getPurpleGrey80", "Pink80", "getPink80", "Purple40", "getPurple40", "PurpleGrey40", "getPurpleGrey40", "Pink40", "getPink40", "Purple20", "getPurple20", "PurpleGrey20", "getPurpleGrey20", "Pink20", "getPink20", "CocoProxyPrimary", "getCocoProxyPrimary", "CocoProxyPrimaryVariant", "getCocoProxyPrimaryVariant", "CocoProxySecondary", "getCocoProxySecondary", "CocoProxySecondaryVariant", "getCocoProxySecondaryVariant", "StatusConnected", "getStatusConnected", "StatusConnecting", "getStatusConnecting", "StatusDisconnected", "getStatusDisconnected", "StatusError", "getStatusError", "SuccessColor", "getSuccessColor", "TrafficUpload", "getTrafficUpload", "TrafficDownload", "getTrafficDownload", "Light", "getLight", "Dark", "getDark", "LightSurface", "getLightSurface", "DarkSurface", "getDarkSurface", "CardLight", "getCardLight", "CardDark", "getCardDark", "BorderLight", "getBorderLight", "BorderDark", "getBorderDark", "TextPrimary", "getTextPrimary", "TextSecondary", "getTextSecondary", "TextOnDark", "getTextOnDark", "TextOnDarkSecondary", "getTextOnDarkSecondary", "Success", "getSuccess", "Warning", "getWarning", "Error", "getError", "Info", "getInfo", "ErrorColor", "getErrorColor", "WarningColor", "getWarningColor", "InfoColor", "getInfoColor", "DebugColor", "getDebugColor", "VerboseColor", "getVerboseColor", "ChartColors", "", "getChartColors", "()Ljava/util/List;", "app_debug"})
public final class ColorKt {
    private static final long Purple80 = 0L;
    private static final long PurpleGrey80 = 0L;
    private static final long Pink80 = 0L;
    private static final long Purple40 = 0L;
    private static final long PurpleGrey40 = 0L;
    private static final long Pink40 = 0L;
    private static final long Purple20 = 0L;
    private static final long PurpleGrey20 = 0L;
    private static final long Pink20 = 0L;
    private static final long CocoProxyPrimary = 0L;
    private static final long CocoProxyPrimaryVariant = 0L;
    private static final long CocoProxySecondary = 0L;
    private static final long CocoProxySecondaryVariant = 0L;
    private static final long StatusConnected = 0L;
    private static final long StatusConnecting = 0L;
    private static final long StatusDisconnected = 0L;
    private static final long StatusError = 0L;
    private static final long SuccessColor = 0L;
    private static final long TrafficUpload = 0L;
    private static final long TrafficDownload = 0L;
    private static final long Light = 0L;
    private static final long Dark = 0L;
    private static final long LightSurface = 0L;
    private static final long DarkSurface = 0L;
    private static final long CardLight = 0L;
    private static final long CardDark = 0L;
    private static final long BorderLight = 0L;
    private static final long BorderDark = 0L;
    private static final long TextPrimary = 0L;
    private static final long TextSecondary = 0L;
    private static final long TextOnDark = 0L;
    private static final long TextOnDarkSecondary = 0L;
    private static final long Success = 0L;
    private static final long Warning = 0L;
    private static final long Error = 0L;
    private static final long Info = 0L;
    private static final long ErrorColor = 0L;
    private static final long WarningColor = 0L;
    private static final long InfoColor = 0L;
    private static final long DebugColor = 0L;
    private static final long VerboseColor = 0L;
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<androidx.compose.ui.graphics.Color> ChartColors = null;
    
    public static final long getPurple80() {
        return 0L;
    }
    
    public static final long getPurpleGrey80() {
        return 0L;
    }
    
    public static final long getPink80() {
        return 0L;
    }
    
    public static final long getPurple40() {
        return 0L;
    }
    
    public static final long getPurpleGrey40() {
        return 0L;
    }
    
    public static final long getPink40() {
        return 0L;
    }
    
    public static final long getPurple20() {
        return 0L;
    }
    
    public static final long getPurpleGrey20() {
        return 0L;
    }
    
    public static final long getPink20() {
        return 0L;
    }
    
    public static final long getCocoProxyPrimary() {
        return 0L;
    }
    
    public static final long getCocoProxyPrimaryVariant() {
        return 0L;
    }
    
    public static final long getCocoProxySecondary() {
        return 0L;
    }
    
    public static final long getCocoProxySecondaryVariant() {
        return 0L;
    }
    
    public static final long getStatusConnected() {
        return 0L;
    }
    
    public static final long getStatusConnecting() {
        return 0L;
    }
    
    public static final long getStatusDisconnected() {
        return 0L;
    }
    
    public static final long getStatusError() {
        return 0L;
    }
    
    public static final long getSuccessColor() {
        return 0L;
    }
    
    public static final long getTrafficUpload() {
        return 0L;
    }
    
    public static final long getTrafficDownload() {
        return 0L;
    }
    
    public static final long getLight() {
        return 0L;
    }
    
    public static final long getDark() {
        return 0L;
    }
    
    public static final long getLightSurface() {
        return 0L;
    }
    
    public static final long getDarkSurface() {
        return 0L;
    }
    
    public static final long getCardLight() {
        return 0L;
    }
    
    public static final long getCardDark() {
        return 0L;
    }
    
    public static final long getBorderLight() {
        return 0L;
    }
    
    public static final long getBorderDark() {
        return 0L;
    }
    
    public static final long getTextPrimary() {
        return 0L;
    }
    
    public static final long getTextSecondary() {
        return 0L;
    }
    
    public static final long getTextOnDark() {
        return 0L;
    }
    
    public static final long getTextOnDarkSecondary() {
        return 0L;
    }
    
    public static final long getSuccess() {
        return 0L;
    }
    
    public static final long getWarning() {
        return 0L;
    }
    
    public static final long getError() {
        return 0L;
    }
    
    public static final long getInfo() {
        return 0L;
    }
    
    public static final long getErrorColor() {
        return 0L;
    }
    
    public static final long getWarningColor() {
        return 0L;
    }
    
    public static final long getInfoColor() {
        return 0L;
    }
    
    public static final long getDebugColor() {
        return 0L;
    }
    
    public static final long getVerboseColor() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<androidx.compose.ui.graphics.Color> getChartColors() {
        return null;
    }
}