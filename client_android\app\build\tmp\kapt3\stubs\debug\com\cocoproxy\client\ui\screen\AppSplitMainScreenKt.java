package com.cocoproxy.client.ui.screen;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000<\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\u001aJ\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a \u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0003\u001a8\u0010\u0010\u001a\u00020\u00012\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u000f2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0003\u001a\u0010\u0010\u0018\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000bH\u0002\u001a\u0010\u0010\u0019\u001a\u00020\u00122\u0006\u0010\n\u001a\u00020\u000bH\u0002\u00a8\u0006\u001a"}, d2 = {"AppSplitMainScreen", "", "onNavigateBack", "Lkotlin/Function0;", "onNavigateToModeConfig", "onNavigateToAppSelection", "onNavigateToTrafficStats", "viewModel", "Lcom/cocoproxy/client/ui/viewmodel/AppSplitViewModel;", "CurrentConfigCard", "splitMode", "Lcom/cocoproxy/client/core/SplitMode;", "selectedAppsCount", "", "isEnabled", "", "AppSplitActionCard", "title", "", "description", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "enabled", "onClick", "getSplitModeDisplayName", "getSplitModeDescription", "app_debug"})
public final class AppSplitMainScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AppSplitMainScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToModeConfig, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToAppSelection, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToTrafficStats, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.viewmodel.AppSplitViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CurrentConfigCard(com.cocoproxy.client.core.SplitMode splitMode, int selectedAppsCount, boolean isEnabled) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AppSplitActionCard(java.lang.String title, java.lang.String description, androidx.compose.ui.graphics.vector.ImageVector icon, boolean enabled, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    private static final java.lang.String getSplitModeDisplayName(com.cocoproxy.client.core.SplitMode splitMode) {
        return null;
    }
    
    private static final java.lang.String getSplitModeDescription(com.cocoproxy.client.core.SplitMode splitMode) {
        return null;
    }
}