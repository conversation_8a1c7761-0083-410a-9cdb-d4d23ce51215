package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// 打开数据库
	db, err := sql.Open("sqlite3", "../coco.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}
	defer db.Close()

	// 查询所有用户
	rows, err := db.Query("SELECT id, username, password_hash, is_admin FROM users")
	if err != nil {
		log.Fatal("Failed to query users:", err)
	}
	defer rows.Close()

	fmt.Println("Current users in database:")
	fmt.Println("ID | Username | Password Hash Length | Is Admin")
	fmt.Println("---|----------|---------------------|----------")

	for rows.Next() {
		var id int
		var username, passwordHash string
		var isAdmin bool

		err := rows.Scan(&id, &username, &passwordHash, &isAdmin)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}

		fmt.Printf("%d  | %-8s | %-19d | %t\n", id, username, len(passwordHash), isAdmin)

		// 测试密码验证
		if username == "admin" {
			err := bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte("admin123"))
			if err != nil {
				fmt.Printf("   Password verification FAILED for admin: %v\n", err)
			} else {
				fmt.Printf("   Password verification SUCCESS for admin\n")
			}
		}
	}

	if err = rows.Err(); err != nil {
		log.Fatal("Error iterating rows:", err)
	}
}