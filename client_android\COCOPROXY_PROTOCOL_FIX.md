# CocoProxy协议修复指南

## 问题根源分析

通过对比Python客户端和Android客户端的代码，发现了关键问题：

### 1. Android客户端使用了错误的协议格式
```kotlin
// 错误的协议格式（修复前）
request.write(byteArrayOf(0xC0.toByte(), 0xC0.toByte())) // 魔数
request.write(0x01) // 版本
request.write(0x01) // 命令（连接）
```

### 2. Python客户端使用正确的CocoProxy协议格式
```python
# 正确的协议格式
packet.append(protocol_type)      # 1 byte protocol type (0x03 for SOCKS5)
packet.append(encrypt_type)       # 1 byte encrypt type (0x00 for none)
packet.append(username_len)       # 1 byte username length
packet.extend(username_bytes)     # N bytes username
packet.extend(b'\x00\x00\x00\x00') # 4 bytes random padding
packet.extend(struct.pack('!I', len(target_data))) # 4 bytes data length
packet.extend(target_data)        # target data (host:port)
```

## 修复内容

### 1. 使用正确的CocoProxy协议格式
```kotlin
// 构建CocoProxy协议包
val packet = ByteArrayOutputStream()
packet.write(0x03) // 协议类型：SOCKS5
packet.write(0x00) // 加密类型：无加密
packet.write(usernameLen) // 用户名长度
packet.write(usernameBytes) // 用户名
packet.write(byteArrayOf(0x00, 0x00, 0x00, 0x00)) // 4字节随机填充

// 数据长度（网络字节序）
val dataLength = targetData.size
packet.write((dataLength shr 24) and 0xFF)
packet.write((dataLength shr 16) and 0xFF)
packet.write((dataLength shr 8) and 0xFF)
packet.write(dataLength and 0xFF)

// 目标数据
packet.write(targetData)
```

### 2. 添加详细的调试日志
```kotlin
Log.d("CocoProxyVpnService", "Sending CocoProxy SOCKS5 request for $targetHost:$targetPort")
Log.d("CocoProxyVpnService", "Sent CocoProxy SOCKS5 request: ${requestData.size} bytes")
Log.d("CocoProxyVpnService", "CocoProxy SOCKS5 connection established for $targetHost:$targetPort")
```

### 3. 正确的SOCKS5握手流程
```kotlin
// 执行SOCKS5握手
if (!performSocks5HandshakeForVpn(socket, targetHost, targetPort)) {
    Log.e("CocoProxyVpnService", "SOCKS5 handshake failed for $targetHost:$targetPort")
    return false
}
```

## 测试步骤

### 1. 重新编译安装
```bash
cd client_android
./gradlew clean
adb uninstall com.cocoproxy.client.debug
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. 启动详细DNS调试日志监控
```bash
adb logcat -c
adb logcat -s CocoProxyVpnService | grep -E "(DNS|SOCKS5|CocoProxy|auth|connect)"
```

### 3. 测试流程
1. 配置服务器信息（确保用户名正确）
2. 启用VPN模式
3. 配置应用分流（白名单模式，添加Chrome）
4. 启动VPN
5. 打开Chrome，访问google.com

## 预期的成功日志

### 完整的DNS SOCKS5流程：
```
CocoProxyVpnService: DNS query detected: ********:xxxxx -> *******:53
CocoProxyVpnService: DNS shouldUseProxy: true
CocoProxyVpnService: Handling DNS query through proxy: ********:xxxxx -> *******:53
CocoProxyVpnService: handleDnsQuery method called
CocoProxyVpnService: DNS query coroutine started
CocoProxyVpnService: Processing DNS query: ********:xxxxx -> *******:53
CocoProxyVpnService: Attempting to connect to CocoProxy server for DNS
CocoProxyVpnService: Successfully connected to CocoProxy server for DNS
CocoProxyVpnService: Sending CocoProxy SOCKS5 request for *******:53
CocoProxyVpnService: Sent CocoProxy SOCKS5 request: XX bytes
CocoProxyVpnService: Starting SOCKS5 handshake for VPN: *******:53
CocoProxyVpnService: Sent SOCKS5 auth request
CocoProxyVpnService: SOCKS5 auth successful for VPN
CocoProxyVpnService: Sent SOCKS5 connect request for VPN: *******:53
CocoProxyVpnService: SOCKS5 connect successful for VPN: *******:53
CocoProxyVpnService: CocoProxy SOCKS5 connection established for *******:53
CocoProxyVpnService: Received DNS response: XXX bytes
CocoProxyVpnService: Sent DNS response to client
```

### 然后应该看到TCP连接：
```
CocoProxyVpnService: TCP Packet: ********:xxxxx -> XXX.XXX.XXX.XXX:443
CocoProxyVpnService: Handling SYN packet for XXX.XXX.XXX.XXX:443
CocoProxyVpnService: Connected to CocoProxy server XXX.XXX.XXX.XXX:28888
```

## 关键变化

### 1. 协议格式修复
- **修复前**：使用错误的魔数和版本号
- **修复后**：使用正确的CocoProxy协议格式，与Python客户端一致

### 2. 数据格式修复
- **修复前**：直接发送IP地址和端口
- **修复后**：发送"host:port"字符串格式

### 3. 握手流程修复
- **修复前**：只发送协议头，没有SOCKS5握手
- **修复后**：发送协议头 + 完整的SOCKS5握手

## 故障排除

### 如果仍然连接失败
1. **检查用户名配置**：确保Android客户端和服务器的用户名一致
2. **检查协议包大小**：应该看到"Sent CocoProxy SOCKS5 request: XX bytes"
3. **检查SOCKS5握手**：应该看到完整的握手流程

### 如果SOCKS5握手失败
1. **检查服务器响应**：确认服务器正确处理CocoProxy协议
2. **检查网络连接**：确认到服务器的连接稳定
3. **检查超时设置**：可能需要调整超时时间

## 最新修复（改进的DNS处理 - 基于之前成功的方法）

### 回到直接DNS处理方法
经过测试发现简化的DNS处理方法（丢弃DNS包）不工作，因为Chrome没有使用HTTP代理设置。
所以我们回到之前接近成功的直接DNS处理方法，并进行了关键改进。

### 关键改进内容

#### 1. **DNS数据验证和异常处理**
```kotlin
// 验证DNS数据大小是否合理 (DNS查询通常小于512字节)
if (dnsDataLength > 512) {
    Log.w("CocoProxyVpnService", "DNS query too large: ${dnsDataLength} bytes, falling back to direct forwarding")
    // 对于异常大的DNS查询，直接转发原始包
    val length = packet.limit()
    packet.position(0)
    vpnOutput.write(packet.array(), 0, length)
    vpnOutput.flush()
    return@launch
}
```

#### 2. **改进的Socket超时设置**
```kotlin
// 设置socket超时
proxySocket.soTimeout = 10000 // 10秒超时
Log.d("CocoProxyVpnService", "Successfully connected to CocoProxy server for DNS with 10s timeout")
```

#### 3. **详细的响应时间监控**
```kotlin
val startTime = System.currentTimeMillis()
val length = proxySocket.getInputStream().read(responseBuffer)
val duration = System.currentTimeMillis() - startTime
Log.d("CocoProxyVpnService", "DNS response received in ${duration}ms, length: $length bytes")
```

#### 4. **增强的错误处理和Fallback**
```kotlin
} catch (e: Exception) {
    Log.e("CocoProxyVpnService", "Error in DNS query coroutine: ${e.message}", e)
    // 如果代理失败，尝试直接转发原始包
    try {
        packet.position(0)
        vpnOutput.write(packet.array(), 0, length)
        vpnOutput.flush()
        Log.d("CocoProxyVpnService", "Fallback: forwarded original DNS packet")
    } catch (fallbackError: Exception) {
        Log.e("CocoProxyVpnService", "Fallback DNS forwarding failed", fallbackError)
    }
}
```

#### 5. **响应数据验证**
```kotlin
// 验证响应数据
if (responseLength > 512) {
    Log.w("CocoProxyVpnService", "DNS response too large: $responseLength bytes")
    throw Exception("DNS response too large")
}
```

## 最新修复（回到SOCKS5协议 - 发现服务器端协议支持问题）

### 关键发现：服务器端协议支持
通过分析服务器端代码发现了根本问题：

#### 服务器端支持的协议类型：
```go
// 协议类型 (server_go/main.go)
const (
    PROTOCOL_HTTP_HTTPS byte = 0x01
    PROTOCOL_TCP        byte = 0x02  // 这是TCP代理，不是UDP代理！
    PROTOCOL_SOCKS5     byte = 0x03
)
```

#### 协议处理逻辑：
```go
switch protocolType {
case PROTOCOL_HTTP_HTTPS:
    handleHTTPProxy(clientConn, initialDecryptedData, username)
case PROTOCOL_TCP:
    go handleTCPProxy(clientConn, initialDecryptedData, username)  // TCP代理
case PROTOCOL_SOCKS5:
    handleCocoProxySOCKS5(clientConn, initialDecryptedData, username)
default:
    log.Printf("Unsupported protocol type: %d", protocolType)
}
```

### 问题根源
1. **协议类型0x02是TCP代理，不是UDP代理**
2. **服务器端没有专门的UDP代理协议实现**
3. **我们发送的UDP数据格式与TCP代理期望的格式不匹配**
4. **导致服务器无法正确处理DNS查询请求**

### 解决方案：回到SOCKS5协议

#### 1. **使用正确的SOCKS5协议**
```kotlin
// 通过CocoProxy代理服务器转发DNS查询（使用SOCKS5协议）
val proxySocket = connectToCocoProxyServerForSocks5(destIp, destPort)
```

#### 2. **SOCKS5协议处理DNS**
```kotlin
// 发送DNS查询数据（通过已建立的SOCKS5隧道）
proxySocket.getOutputStream().write(dnsData)
proxySocket.getOutputStream().flush()
```

#### 3. **工作原理**
1. **建立SOCKS5连接到DNS服务器**（如*******:53）
2. **通过SOCKS5隧道发送原始DNS查询数据**
3. **接收DNS响应数据**
4. **构建UDP响应包发送给客户端**

### 预期效果
- ✅ **使用服务器支持的协议** - SOCKS5协议（0x03）
- ✅ **标准的DNS处理** - 通过SOCKS5隧道传输DNS数据
- ✅ **与服务器端兼容** - 使用现有的SOCKS5处理逻辑
- ✅ **简化客户端实现** - 重用现有的SOCKS5连接方法

## 最终修复（DNS over TCP格式 - 解决协议格式问题）

### 发现的根本问题
通过分析服务器端代码发现：

#### 服务器端不支持UDP ASSOCIATE：
```go
case 0x03: // UDP ASSOCIATE
    log.Printf("SOCKS5: UDP ASSOCIATE command not supported")
    clientConn.Write([]byte{0x05, 0x07, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00})
```

#### 问题根源：
1. **我们建立的是TCP CONNECT连接**（命令0x01）到DNS服务器
2. **然后发送原始UDP DNS数据**到TCP连接
3. **这是错误的协议使用方式** - TCP连接期望TCP格式的数据

### 解决方案：使用DNS over TCP格式

#### 1. **发送TCP格式的DNS查询**
```kotlin
// DNS over TCP格式：2字节长度前缀 + DNS数据
val tcpDnsQuery = ByteArrayOutputStream()
// 写入长度前缀（2字节，大端序）
tcpDnsQuery.write((dnsData.size shr 8) and 0xFF)
tcpDnsQuery.write(dnsData.size and 0xFF)
// 写入DNS数据
tcpDnsQuery.write(dnsData)
```

#### 2. **接收TCP格式的DNS响应**
```kotlin
// 首先读取2字节长度前缀
val lengthBuffer = ByteArray(2)
// 解析长度
val responseDataLength = ((lengthBuffer[0].toInt() and 0xFF) shl 8) or (lengthBuffer[1].toInt() and 0xFF)
// 读取DNS响应数据
val responseBuffer = ByteArray(responseDataLength)
```

#### 3. **工作原理**
1. **建立SOCKS5 TCP连接到DNS服务器**（如*******:53）
2. **发送TCP格式的DNS查询**（长度前缀 + DNS数据）
3. **接收TCP格式的DNS响应**（长度前缀 + DNS数据）
4. **提取DNS数据并构建UDP响应包**发送给客户端

### 预期效果
- ✅ **正确的协议格式** - 使用DNS over TCP标准格式
- ✅ **与TCP连接兼容** - 符合TCP连接的数据传输要求
- ✅ **标准DNS处理** - 遵循RFC 1035 DNS over TCP规范
- ✅ **解决空响应问题** - 服务器能够正确处理和响应

## TCP状态管理修复（解决连接过早关闭问题）

### 发现的问题
虽然DNS查询已经正常工作，但是浏览器仍然无法打开网页，日志显示：

#### TCP连接建立后立即关闭：
```
SOCKS5 connect successful for VPN: 172.64.41.3:443
Sent TCP response: 172.64.41.3:443 -> ********:38084, flags: SYN|ACK, data: 0 bytes
SYN-ACK sent for 172.64.41.3:443
Starting proxy response handler for ********:38084-172.64.41.3:443
Received data packet but connection not established, state: CLOSED
Proxy connection closed for ********:38084-172.64.41.3:443
```

### 问题根源：TCP状态转换错误

#### 错误的状态转换：
```kotlin
// 错误的实现（修复前）
tcpState.state = TcpState.SYN_RECEIVED  // 正确
// 发送SYN-ACK后：
tcpState.state = TcpState.SYN_SENT      // 错误！应该保持SYN_RECEIVED
```

#### ACK处理逻辑问题：
```kotlin
// 错误的实现（修复前）
if (tcpState.state == TcpState.SYN_SENT && ackNum == tcpState.localSeq) {
    tcpState.state = TcpState.ESTABLISHED  // 只处理SYN_SENT状态
}
```

### 解决方案：正确的TCP状态机

#### 1. **修复状态转换**
```kotlin
// 正确的实现（修复后）
tcpState.state = TcpState.SYN_RECEIVED
// 发送SYN-ACK后保持SYN_RECEIVED状态，等待客户端ACK
Log.d("CocoProxyVpnService", "TCP state after SYN-ACK: ${tcpState.state}, waiting for ACK")
```

#### 2. **修复ACK处理**
```kotlin
// 正确的实现（修复后）
// 服务器端：SYN_RECEIVED -> ESTABLISHED
if (tcpState.state == TcpState.SYN_RECEIVED && ackNum == tcpState.localSeq) {
    tcpState.state = TcpState.ESTABLISHED
}
// 客户端：SYN_SENT -> ESTABLISHED
else if (tcpState.state == TcpState.SYN_SENT && ackNum == tcpState.localSeq) {
    tcpState.state = TcpState.ESTABLISHED
}
```

#### 3. **改进数据包处理**
```kotlin
// 支持两种状态的连接建立
if (tcpState.state == TcpState.SYN_RECEIVED) {
    tcpState.state = TcpState.ESTABLISHED
    Log.d("CocoProxyVpnService", "Connection established via data packet (SYN_RECEIVED -> ESTABLISHED)")
}
```

### 预期效果
- ✅ **正确的TCP三次握手** - SYN -> SYN_RECEIVED -> ESTABLISHED
- ✅ **连接状态一致性** - 客户端和服务器状态同步
- ✅ **数据传输正常** - 连接建立后能够正常传输HTTP数据
- ✅ **浏览器正常工作** - 网页能够正常加载

## 预期的新日志

### 成功的DNS查询流程：
```
DNS query detected: ********:xxxxx -> *******:53
handleDnsQuery method called
DNS query coroutine started
Processing DNS query: ********:xxxxx -> *******:53
Attempting to connect to CocoProxy server for DNS
Successfully connected to CocoProxy server for DNS
Sending CocoProxy SOCKS5 request for *******:53
Sent CocoProxy SOCKS5 request: XX bytes
Starting SOCKS5 handshake for VPN: *******:53
SOCKS5 auth successful for VPN
SOCKS5 connect successful for VPN: *******:53
CocoProxy SOCKS5 connection established for *******:53
Sending DNS query data: XX bytes
DNS query data sent, waiting for response...
Received DNS response: XX bytes
Sent DNS response to client
```

### 如果仍然超时：
```
DNS response timeout for *******:53
Error in DNS query coroutine: Read timed out
Fallback: forwarded original DNS packet
```

## 预期结果

修复成功后的完整流程：
1. **Chrome发起DNS查询** → VPN拦截
2. **VPN使用正确的CocoProxy协议连接代理** → 协议格式正确
3. **建立到DNS服务器的SOCKS5隧道** → 完整的握手流程
4. **通过隧道发送DNS查询** → 设置合理的超时时间
5. **接收DNS响应或超时处理** → 获得DNS响应或fallback
6. **VPN发送DNS响应给Chrome** → Chrome获得IP地址
7. **Chrome开始TCP连接** → VPN处理TCP代理
8. **Chrome成功访问google.com**

这个修复应该能解决DNS响应超时的问题，让DNS查询能够正确接收响应或进行fallback处理。
