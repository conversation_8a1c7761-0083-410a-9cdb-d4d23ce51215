package main

import (
	"log"
	"net/http"
	"os"
)

func main() {
	log.Println("Starting simple Admin UI server...")

	// 确保admin_ui目录存在
	adminUIDir := "../admin_ui"
	if _, err := os.Stat(adminUIDir); os.IsNotExist(err) {
		log.Printf("Creating admin_ui directory...")
		if err := os.MkdirAll(adminUIDir, 0755); err != nil {
			log.Fatalf("Failed to create admin_ui directory: %v", err)
		}
	}

	// 提供静态文件
	http.Handle("/ui/", http.StripPrefix("/ui/", http.FileServer(http.Dir(adminUIDir))))

	// 重定向根路径到UI
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path == "/" {
			http.Redirect(w, r, "/ui/", http.StatusMovedPermanently)
			return
		}
		http.NotFound(w, r)
	})

	// 简单的API测试端点
	http.HandleFunc("/api/test", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"status":"ok","message":"API is working"}`))
	})

	log.Println("Admin UI server running at http://localhost:8080")
	log.Println("Access the admin panel at http://localhost:8080/ui/")
	
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}