# CocoProxy Tools

This directory contains utility tools for managing the CocoProxy server.

## Building Tools

Run `build_tools.bat` to build all tools:

```batch
build_tools.bat
```

## Available Tools

### check_db.exe
Inspects the database and shows all users with their details.

**Usage:**
```batch
check_db.exe
```

**Features:**
- Lists all users in the database
- Shows password hash lengths
- Tests admin password verification

### reset_admin.exe
Resets the admin user with default credentials.

**Usage:**
```batch
reset_admin.exe
```

**Features:**
- Deletes existing admin user
- Creates new admin user with username "admin" and password "admin123"
- Verifies the created user

### start_admin.exe
Starts a simple admin UI server without the proxy functionality.

**Usage:**
```batch
start_admin.exe
```

**Features:**
- Serves static files from ../admin_ui directory
- Provides basic API test endpoint
- Runs on port 8080

### admin_only.exe
Starts the full admin UI server without the proxy functionality.

**Usage:**
```batch
admin_only.exe
```

**Features:**
- Full admin UI with login functionality
- User management capabilities
- Database integration
- Runs on port 8080

## Notes

- All tools expect the database file `coco.db` to be in the parent directory
- The admin UI files should be in the `../admin_ui` directory
- These tools are standalone and don't require the main server to be running