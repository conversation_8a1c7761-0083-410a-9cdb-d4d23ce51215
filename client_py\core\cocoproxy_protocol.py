#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import threading
import struct
import logging
import time
from PySide6.QtCore import QObject, Signal

class CocoProxyProtocolClient(QObject):
    """CocoProxy client with correct protocol implementation"""
    
    # Signals
    proxy_status_changed = Signal(bool, str)
    client_connected = Signal(str)
    client_disconnected = Signal(str)
    traffic_data = Signal(int)
    
    def __init__(self, config):
        """Initialize CocoProxy protocol client"""
        super().__init__()
        self.config = config
        self.logger = logging.getLogger("CocoProxy.SOCKS5Client")
        self.running = False
        self.server_socket = None
        self.server_thread = None
        self.client_threads = []
        self.active_connections = 0
        self.max_connections = 50
        
        # Protocol constants (from server_go/main.go)
        self.PROTOCOL_HTTP_HTTPS = 0x01
        self.PROTOCOL_TCP = 0x02
        self.PROTOCOL_SOCKS5 = 0x03
        self.ENCRYPT_NONE = 0x00
        self.ENCRYPT_AES = 0x01
        self.ENCRYPT_CHACHA20 = 0x02
        
    def start(self):
        """Start the SOCKS5 proxy server"""
        if self.running:
            return False, "Proxy server is already running"
        
        # Check if port is already in use
        local_port = self.config.get("proxy", "local_port")
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.bind(("127.0.0.1", local_port))
            test_socket.close()
        except OSError as e:
            return False, f"Port {local_port} is already in use. Another instance may be running."
        
        try:
            local_port = self.config.get("proxy", "local_port")
            
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(("127.0.0.1", local_port))
            self.server_socket.listen(10)
            
            self.running = True
            
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()
            
            message = f"CocoProxy client started on 127.0.0.1:{local_port}"
            self.logger.info(message)
            self.proxy_status_changed.emit(True, message)
            
            return True, message
            
        except Exception as e:
            self.running = False
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            error_msg = f"Failed to start proxy server: {str(e)}"
            self.logger.error(error_msg)
            self.proxy_status_changed.emit(False, error_msg)
            
            return False, error_msg
    
    def stop(self):
        """Stop the proxy server"""
        if not self.running:
            return True, "Proxy server is not running"
        
        try:
            self.running = False
            
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=2)
            
            for thread in self.client_threads[:]:
                if thread.is_alive():
                    thread.join(timeout=1)
                self.client_threads.remove(thread)
            
            message = "Proxy server stopped"
            self.logger.info(message)
            self.proxy_status_changed.emit(False, message)
            
            return True, message
            
        except Exception as e:
            error_msg = f"Error stopping proxy server: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _server_loop(self):
        """Main server loop"""
        self.logger.info("CocoProxy protocol client server loop started")
        
        while self.running:
            try:
                if not self.server_socket:
                    break
                
                client_socket, client_addr = self.server_socket.accept()
                client_addr_str = f"{client_addr[0]}:{client_addr[1]}"
                
                # Check connection limit
                if self.active_connections >= self.max_connections:
                    self.logger.warning(f"Connection limit reached ({self.max_connections}), rejecting {client_addr_str}")
                    client_socket.close()
                    continue
                
                self.logger.info(f"SOCKS5 client connected: {client_addr_str}")
                self.client_connected.emit(client_addr_str)
                
                client_thread = threading.Thread(
                    target=self._handle_socks5_client,
                    args=(client_socket, client_addr_str),
                    daemon=True
                )
                client_thread.start()
                self.client_threads.append(client_thread)
                
            except OSError:
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"Error in server loop: {e}")
                break
        
        self.logger.info("CocoProxy protocol client server loop ended")
    
    def _handle_socks5_client(self, client_socket, client_addr):
        """Handle SOCKS5 client connection"""
        self.active_connections += 1
        try:
            # SOCKS5 authentication
            if not self._socks5_auth(client_socket):
                self.logger.error(f"SOCKS5 auth failed for {client_addr}")
                return
            
            # SOCKS5 request
            target_host, target_port = self._socks5_request(client_socket)
            if not target_host:
                self.logger.error(f"SOCKS5 request failed for {client_addr}")
                return
            
            self.logger.info(f"SOCKS5 request: {target_host}:{target_port} from {client_addr}")
            
            # Connect to CocoProxy server and send request
            cocoproxy_socket = self._connect_and_request(target_host, target_port)
            if not cocoproxy_socket:
                self.logger.error(f"Failed to send CocoProxy request for {target_host}:{target_port}")
                self._socks5_reply(client_socket, 0x01)  # General failure
                return
            
            # Send success reply to SOCKS5 client
            self._socks5_reply(client_socket, 0x00)  # Success
            self.logger.info(f"SOCKS5 tunnel established: {client_addr} -> {target_host}:{target_port}")
            
            # Forward data
            self._forward_data(client_socket, cocoproxy_socket, client_addr, f"{target_host}:{target_port}")
            
        except Exception as e:
            self.logger.error(f"Error handling SOCKS5 client {client_addr}: {e}")
        finally:
            self.active_connections -= 1
            try:
                client_socket.close()
            except:
                pass
            self.client_disconnected.emit(client_addr)
    
    def _socks5_auth(self, client_socket):
        """Handle SOCKS5 authentication"""
        try:
            data = client_socket.recv(2)
            if len(data) != 2:
                return False
            
            version, nmethods = struct.unpack("BB", data)
            if version != 5:
                return False
            
            methods = client_socket.recv(nmethods)
            if len(methods) != nmethods:
                return False
            
            client_socket.send(b"\x05\x00")  # No auth required
            return True
            
        except Exception as e:
            self.logger.error(f"SOCKS5 auth error: {e}")
            return False
    
    def _socks5_request(self, client_socket):
        """Handle SOCKS5 connection request"""
        try:
            data = client_socket.recv(4)
            if len(data) != 4:
                return None, None
            
            version, cmd, rsv, atyp = struct.unpack("BBBB", data)
            
            if version != 5 or cmd != 1:
                return None, None
            
            if atyp == 1:  # IPv4
                addr_data = client_socket.recv(4)
                target_host = socket.inet_ntoa(addr_data)
            elif atyp == 3:  # Domain name
                addr_len = struct.unpack("B", client_socket.recv(1))[0]
                target_host = client_socket.recv(addr_len).decode('utf-8')
            else:
                return None, None
            
            port_data = client_socket.recv(2)
            target_port = struct.unpack("!H", port_data)[0]
            
            return target_host, target_port
            
        except Exception as e:
            self.logger.error(f"SOCKS5 request error: {e}")
            return None, None
    
    def _socks5_reply(self, client_socket, status):
        """Send SOCKS5 reply"""
        try:
            reply = struct.pack("BBBB", 5, status, 0, 1)
            reply += socket.inet_aton("0.0.0.0")
            reply += struct.pack("!H", 0)
            client_socket.send(reply)
        except Exception as e:
            self.logger.error(f"SOCKS5 reply error: {e}")
    
    def _connect_and_request(self, target_host, target_port):
        """Connect to CocoProxy server and send request"""
        server_host = self.config.get("server", "host")
        server_port = self.config.get("server", "port")
        username = self.config.get("user", "username")
        
        if not username:
            self.logger.error("No username configured")
            return None
        
        # Try connecting with retry logic
        max_retries = 2
        for attempt in range(max_retries):
            try:
                self.logger.debug(f"Connecting to CocoProxy server {server_host}:{server_port} (attempt {attempt + 1}/{max_retries})")
                
                cocoproxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                cocoproxy_socket.settimeout(15)  # Reduce timeout to 15 seconds for faster retries
                cocoproxy_socket.connect((server_host, server_port))
                
                # Send CocoProxy protocol request
                if not self._send_cocoproxy_protocol(cocoproxy_socket, target_host, target_port, username):
                    cocoproxy_socket.close()
                    if attempt < max_retries - 1:
                        self.logger.debug(f"Protocol request failed, retrying...")
                        continue
                    return None
                
                return cocoproxy_socket
                
            except socket.timeout:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Connection timeout, retrying... (attempt {attempt + 1}/{max_retries})")
                    continue
                else:
                    self.logger.error(f"Failed to send CocoProxy request: timed out")
                    return None
            except ConnectionRefusedError:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Connection refused, retrying... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(1)  # Wait a bit before retry
                    continue
                else:
                    self.logger.error(f"Failed to connect to CocoProxy server: connection refused")
                    return None
            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Connection error: {e}, retrying... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(1)  # Wait a bit before retry
                    continue
                else:
                    self.logger.error(f"Failed to connect to CocoProxy server: {e}")
                    return None
        
        return None
    
    def _send_cocoproxy_protocol(self, cocoproxy_socket, target_host, target_port, username):
        """Send request using CocoProxy protocol format"""
        try:
            self.logger.debug(f"Sending CocoProxy protocol request: {target_host}:{target_port} as {username}")
            
            # Protocol format from server_go:
            # 1 byte protocol type + 1 byte encrypt type + 1 byte username length + 
            # N bytes username + 4 bytes random padding + 4 bytes data length + data
            
            # Get protocol type from configuration
            proxy_protocol = self.config.get("proxy", "protocol")
            if proxy_protocol == "http":
                protocol_type = self.PROTOCOL_HTTP_HTTPS
            elif proxy_protocol == "socks5":
                protocol_type = self.PROTOCOL_SOCKS5
            else:
                # Default to SOCKS5 if unknown protocol
                protocol_type = self.PROTOCOL_SOCKS5
                self.logger.warning(f"Unknown protocol '{proxy_protocol}', defaulting to SOCKS5")
            
            # Prepare target data
            target_data = f"{target_host}:{target_port}".encode('utf-8')
            encrypt_type = self.ENCRYPT_NONE   # No encryption for now
            username_bytes = username.encode('utf-8')
            username_len = len(username_bytes)
            
            if username_len > 255:
                self.logger.error(f"Username too long: {username_len}")
                return False
            
            # Create packet
            packet = bytearray()
            packet.append(protocol_type)      # 1 byte protocol type
            packet.append(encrypt_type)       # 1 byte encrypt type  
            packet.append(username_len)       # 1 byte username length
            packet.extend(username_bytes)     # N bytes username
            packet.extend(b'\x00\x00\x00\x00') # 4 bytes random padding
            packet.extend(struct.pack('!I', len(target_data))) # 4 bytes data length
            packet.extend(target_data)        # Target data
            
            self.logger.debug(f"Sending packet: {len(packet)} bytes")
            self.logger.debug(f"Protocol: {protocol_type}, Encrypt: {encrypt_type}, User: {username}, Target: {target_data.decode('utf-8')}, Data: {len(target_data)} bytes")
            
            # Send packet
            cocoproxy_socket.send(packet)
            
            # Only perform SOCKS5 handshake if protocol is SOCKS5
            if protocol_type == self.PROTOCOL_SOCKS5:
                # After sending CocoProxy protocol, we need to perform SOCKS5 handshake
                # since the server expects standard SOCKS5 protocol after CocoProxy setup
                
                # Step 1: Send SOCKS5 authentication request
                auth_request = b'\x05\x01\x00'  # VER=5, NMETHODS=1, METHOD=0 (no auth)
                cocoproxy_socket.send(auth_request)
                self.logger.debug("Sent SOCKS5 auth request")
            
                # Step 2: Receive SOCKS5 authentication response
                cocoproxy_socket.settimeout(5)
                try:
                    auth_response = cocoproxy_socket.recv(2)
                    if len(auth_response) != 2 or auth_response[0] != 0x05 or auth_response[1] != 0x00:
                        self.logger.error(f"SOCKS5 auth failed: {auth_response.hex() if auth_response else 'no response'}")
                        return False
                    self.logger.debug("SOCKS5 auth successful")
                except socket.timeout:
                    self.logger.error("SOCKS5 auth timeout")
                    return False
                except Exception as e:
                    self.logger.error(f"SOCKS5 auth error: {e}")
                    return False
                
                # Step 3: Send SOCKS5 connect request
                connect_request = bytearray()
                connect_request.extend(b'\x05\x01\x00')  # VER=5, CMD=CONNECT, RSV=0
                
                # Add address type and address
                try:
                    # Try to parse as IP address first
                    import ipaddress
                    ip = ipaddress.ip_address(target_host)
                    if ip.version == 4:
                        connect_request.append(0x01)  # ATYP=IPv4
                        connect_request.extend(ip.packed)
                    else:
                        connect_request.append(0x04)  # ATYP=IPv6
                        connect_request.extend(ip.packed)
                except ValueError:
                    # It's a domain name
                    connect_request.append(0x03)  # ATYP=Domain
                    host_bytes = target_host.encode('utf-8')
                    connect_request.append(len(host_bytes))
                    connect_request.extend(host_bytes)
                
                # Add port
                connect_request.extend(struct.pack('!H', int(target_port)))
                
                cocoproxy_socket.send(connect_request)
                self.logger.debug(f"Sent SOCKS5 connect request for {target_host}:{target_port}")
                
                # Step 4: Receive SOCKS5 connect response
                try:
                    connect_response = cocoproxy_socket.recv(1024)
                    if len(connect_response) < 4 or connect_response[0] != 0x05:
                        self.logger.error(f"SOCKS5 connect failed: {connect_response.hex() if connect_response else 'no response'}")
                        return False
                    
                    if connect_response[1] != 0x00:
                        self.logger.error(f"SOCKS5 connect error code: {connect_response[1]}")
                        return False
                        
                    self.logger.debug("SOCKS5 connect successful")
                except socket.timeout:
                    self.logger.error("SOCKS5 connect timeout")
                    return False
                except Exception as e:
                    self.logger.error(f"SOCKS5 connect error: {e}")
                    return False
                finally:
                    cocoproxy_socket.settimeout(None)
            else:
                # For HTTP protocol, no additional handshake needed
                self.logger.debug(f"HTTP protocol setup complete for {target_host}:{target_port}")
            
            return True
            
        except socket.timeout:
            self.logger.error(f"Failed to send CocoProxy request: timed out")
            return False
        except Exception as e:
            self.logger.error(f"Failed to send CocoProxy protocol: {e}")
            return False
    
    def _forward_data(self, client_socket, server_socket, client_addr, target_info):
        """Forward data between client and server"""
        def forward(source, destination, direction):
            try:
                total_bytes = 0
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
                    total_bytes += len(data)
                    self.traffic_data.emit(len(data))
                
                self.logger.debug(f"Forward {direction} completed: {total_bytes} bytes")
                
            except Exception as e:
                self.logger.debug(f"Forward {direction} error: {e}")
            finally:
                try:
                    source.close()
                    destination.close()
                except:
                    pass
        
        # Start forwarding threads
        client_to_server = threading.Thread(
            target=forward,
            args=(client_socket, server_socket, f"{client_addr}->server"),
            daemon=True
        )
        server_to_client = threading.Thread(
            target=forward,
            args=(server_socket, client_socket, f"server->{client_addr}"),
            daemon=True
        )
        
        client_to_server.start()
        server_to_client.start()
        
        client_to_server.join()
        server_to_client.join()
        
        self.logger.info(f"Data forwarding ended for {client_addr} -> {target_info}")
    
    def is_running(self):
        """Check if proxy server is running"""
        return self.running