package com.cocoproxy.client.ui

import android.content.Intent
import android.net.VpnService
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.ui.navigation.CocoProxyNavigation
import com.cocoproxy.client.ui.theme.CocoProxyTheme
import com.cocoproxy.client.ui.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val viewModel: MainViewModel by viewModels()
    
    // VPN permission launcher
    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            android.util.Log.d("MainActivity", "VPN permission granted by user")
            viewModel.onVpnPermissionGranted()
        } else {
            android.util.Log.d("MainActivity", "VPN permission denied by user")
            viewModel.onVpnPermissionDenied()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // Install splash screen
        installSplashScreen()
        
        super.onCreate(savedInstanceState)
        
        android.util.Log.d("MainActivity", "MainActivity onCreate called")
        android.util.Log.d("MainActivity", "ViewModel instance: $viewModel")
        
        // Observe VPN permission requests
        observeVpnPermissionRequests()
        android.util.Log.d("MainActivity", "VPN permission observer setup completed")
        
        setContent {
            val uiConfig by viewModel.uiConfig.collectAsStateWithLifecycle()
            
            CocoProxyTheme(
                darkTheme = when (uiConfig.theme) {
                    "dark" -> true
                    "light" -> false
                    else -> androidx.compose.foundation.isSystemInDarkTheme()
                }
            ) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    CocoProxyNavigation(
                        onVpnPermissionRequest = { requestVpnPermission() }
                    )
                }
            }
        }
        
        // Add VPN permission check when needed
        checkAndRequestVpnPermissionIfNeeded()
    }
    
    private fun observeVpnPermissionRequests() {
        android.util.Log.d("MainActivity", "Setting up VPN permission observer")
        android.util.Log.d("MainActivity", "Current vpnPermissionRequest value: ${viewModel.vpnPermissionRequest.value}")
        
        viewModel.vpnPermissionRequest.observe(this) { shouldRequest ->
            android.util.Log.d("MainActivity", "VPN permission request observed: $shouldRequest")
            if (shouldRequest == true) {
                android.util.Log.d("MainActivity", "shouldRequest is true, calling requestVpnPermission")
                // Add a small delay to ensure the UI is ready
                android.os.Handler(android.os.Looper.getMainLooper()).post {
                    requestVpnPermission()
                }
                // Reset the request flag
                viewModel.clearVpnPermissionRequest()
            } else {
                android.util.Log.d("MainActivity", "shouldRequest is false or null, ignoring")
            }
        }
        
        // Also add a manual check after a delay
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            android.util.Log.d("MainActivity", "Manual check - vpnPermissionRequest value: ${viewModel.vpnPermissionRequest.value}")
        }, 1000)
    }
    
    private fun requestVpnPermission() {
        android.util.Log.d("MainActivity", "requestVpnPermission called")
        val intent = VpnService.prepare(this)
        if (intent != null) {
            android.util.Log.d("MainActivity", "VPN permission needed, launching permission dialog")
            vpnPermissionLauncher.launch(intent)
        } else {
            // Permission already granted
            android.util.Log.d("MainActivity", "VPN permission already granted")
            viewModel.onVpnPermissionGranted()
        }
    }
    
    override fun onResume() {
        super.onResume()
        viewModel.onActivityResumed()
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.onActivityPaused()
    }
    
    private fun checkAndRequestVpnPermissionIfNeeded() {
        android.util.Log.d("MainActivity", "Setting up direct VPN permission check")
        
        // VPN permission will be requested when user starts proxy with VPN mode enabled
        // Remove automatic VPN startup for now
    }
    
    override fun onDestroy() {
        super.onDestroy()
        viewModel.onActivityDestroyed()
    }
}