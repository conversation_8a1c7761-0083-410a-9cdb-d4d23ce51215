package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

func main() {
	fmt.Println("Fixing traffic data in database...")

	// 打开数据库
	db, err := sql.Open("sqlite3", "../coco.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}
	defer db.Close()

	// 查询当前流量数据
	fmt.Println("\nCurrent traffic data:")
	rows, err := db.Query("SELECT id, username, traffic_limit, used_traffic FROM users")
	if err != nil {
		log.Fatal("Failed to query users:", err)
	}
	defer rows.Close()

	fmt.Println("ID | Username | Traffic Limit | Used Traffic")
	fmt.Println("---|----------|---------------|-------------")

	for rows.Next() {
		var id int
		var username string
		var trafficLimit, usedTraffic int64

		err := rows.Scan(&id, &username, &trafficLimit, &usedTraffic)
		if err != nil {
			log.Printf("Error scanning row: %v", err)
			continue
		}

		fmt.Printf("%d  | %-8s | %-13d | %d\n", id, username, trafficLimit, usedTraffic)

		// 检查是否有异常大的数值
		if usedTraffic > 1e12 || trafficLimit > 1e12 { // > 1TB
			fmt.Printf("   ⚠️  WARNING: Very large traffic values detected!\n")
		}
	}

	// 询问是否要重置流量数据
	fmt.Print("\nDo you want to reset all traffic data to 0? (y/n): ")
	var response string
	fmt.Scanln(&response)

	if response == "y" || response == "Y" {
		// 重置所有用户的流量数据
		_, err = db.Exec("UPDATE users SET used_traffic = 0")
		if err != nil {
			log.Fatal("Failed to reset used traffic:", err)
		}

		// 可选：也重置流量限制为合理值（10GB）
		fmt.Print("Do you want to set traffic limit to 10GB for all users? (y/n): ")
		fmt.Scanln(&response)

		if response == "y" || response == "Y" {
			tenGB := int64(10 * 1024 * 1024 * 1024) // 10GB in bytes
			_, err = db.Exec("UPDATE users SET traffic_limit = ?", tenGB)
			if err != nil {
				log.Fatal("Failed to set traffic limit:", err)
			}
			fmt.Printf("Traffic limit set to %d bytes (10GB) for all users.\n", tenGB)
		}

		fmt.Println("Traffic data reset successfully!")

		// 显示更新后的数据
		fmt.Println("\nUpdated traffic data:")
		rows, err = db.Query("SELECT id, username, traffic_limit, used_traffic FROM users")
		if err != nil {
			log.Fatal("Failed to query users:", err)
		}
		defer rows.Close()

		fmt.Println("ID | Username | Traffic Limit | Used Traffic")
		fmt.Println("---|----------|---------------|-------------")

		for rows.Next() {
			var id int
			var username string
			var trafficLimit, usedTraffic int64

			err := rows.Scan(&id, &username, &trafficLimit, &usedTraffic)
			if err != nil {
				log.Printf("Error scanning row: %v", err)
				continue
			}

			limitMB := float64(trafficLimit) / (1024 * 1024)
			usedMB := float64(usedTraffic) / (1024 * 1024)

			fmt.Printf("%d  | %-8s | %-13.1f MB | %.1f MB\n", id, username, limitMB, usedMB)
		}
	} else {
		fmt.Println("No changes made.")
	}
}