#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import threading
import struct
import logging
import time
from PySide6.QtCore import QObject, Signal

class LocalProxyServer(QObject):
    """Local proxy server for CocoProxy client"""
    
    # Signals
    proxy_status_changed = Signal(bool, str)  # running, message
    client_connected = Signal(str)  # client address
    client_disconnected = Signal(str)  # client address
    traffic_data = Signal(int)  # bytes transferred
    
    def __init__(self, config):
        """Initialize local proxy server"""
        super().__init__()
        self.config = config
        self.logger = logging.getLogger("CocoProxy.LocalProxy")
        self.running = False
        self.server_socket = None
        self.server_thread = None
        self.client_threads = []
        
    def start(self):
        """Start the local proxy server"""
        if self.running:
            self.logger.warning("Proxy server is already running")
            return False, "Proxy server is already running"
        
        try:
            # Get configuration
            local_port = self.config.get("proxy", "local_port")
            protocol = self.config.get("proxy", "protocol").lower()
            
            # Create server socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(("127.0.0.1", local_port))
            self.server_socket.listen(10)
            
            self.running = True
            
            # Start server thread
            if protocol == "socks5":
                self.server_thread = threading.Thread(target=self._socks5_server_loop, daemon=True)
            elif protocol == "http":
                self.server_thread = threading.Thread(target=self._http_server_loop, daemon=True)
            else:
                raise ValueError(f"Unsupported protocol: {protocol}")
            
            self.server_thread.start()
            
            message = f"{protocol.upper()} proxy server started on 127.0.0.1:{local_port}"
            self.logger.info(message)
            self.proxy_status_changed.emit(True, message)
            
            return True, message
            
        except Exception as e:
            self.running = False
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            error_msg = f"Failed to start proxy server: {str(e)}"
            self.logger.error(error_msg)
            self.proxy_status_changed.emit(False, error_msg)
            
            return False, error_msg
    
    def stop(self):
        """Stop the local proxy server"""
        if not self.running:
            return True, "Proxy server is not running"
        
        try:
            self.running = False
            
            # Close server socket
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            # Wait for server thread to finish
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=2)
            
            # Clean up client threads
            for thread in self.client_threads[:]:
                if thread.is_alive():
                    thread.join(timeout=1)
                self.client_threads.remove(thread)
            
            message = "Proxy server stopped"
            self.logger.info(message)
            self.proxy_status_changed.emit(False, message)
            
            return True, message
            
        except Exception as e:
            error_msg = f"Error stopping proxy server: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _socks5_server_loop(self):
        """SOCKS5 server main loop"""
        self.logger.info("SOCKS5 server loop started")
        
        while self.running:
            try:
                if not self.server_socket:
                    break
                
                # Accept client connection
                client_socket, client_addr = self.server_socket.accept()
                client_addr_str = f"{client_addr[0]}:{client_addr[1]}"
                
                self.logger.info(f"SOCKS5 client connected: {client_addr_str}")
                self.client_connected.emit(client_addr_str)
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self._handle_socks5_client,
                    args=(client_socket, client_addr_str),
                    daemon=True
                )
                client_thread.start()
                self.client_threads.append(client_thread)
                
            except OSError:
                # Socket closed
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"Error in SOCKS5 server loop: {e}")
                break
        
        self.logger.info("SOCKS5 server loop ended")
    
    def _handle_socks5_client(self, client_socket, client_addr):
        """Handle SOCKS5 client connection"""
        try:
            # SOCKS5 authentication
            if not self._socks5_auth(client_socket):
                return
            
            # SOCKS5 request
            target_host, target_port = self._socks5_request(client_socket)
            if not target_host:
                return
            
            # Connect to remote server through CocoProxy
            remote_socket = self._connect_to_remote_server()
            if not remote_socket:
                self._socks5_reply(client_socket, 0x01)  # General failure
                return
            
            # Send connection request to remote server
            if not self._send_connect_request(remote_socket, target_host, target_port):
                self._socks5_reply(client_socket, 0x01)  # General failure
                remote_socket.close()
                return
            
            # Send success reply
            self._socks5_reply(client_socket, 0x00)  # Success
            
            # Start data forwarding
            self._forward_data(client_socket, remote_socket, client_addr)
            
        except Exception as e:
            self.logger.error(f"Error handling SOCKS5 client {client_addr}: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
            self.client_disconnected.emit(client_addr)
    
    def _socks5_auth(self, client_socket):
        """Handle SOCKS5 authentication"""
        try:
            # Read authentication methods
            data = client_socket.recv(2)
            if len(data) != 2:
                return False
            
            version, nmethods = struct.unpack("BB", data)
            if version != 5:
                return False
            
            # Read methods
            methods = client_socket.recv(nmethods)
            if len(methods) != nmethods:
                return False
            
            # Reply with no authentication required
            client_socket.send(b"\x05\x00")
            return True
            
        except Exception as e:
            self.logger.error(f"SOCKS5 auth error: {e}")
            return False
    
    def _socks5_request(self, client_socket):
        """Handle SOCKS5 connection request"""
        try:
            # Read request header
            data = client_socket.recv(4)
            if len(data) != 4:
                return None, None
            
            version, cmd, rsv, atyp = struct.unpack("BBBB", data)
            
            if version != 5 or cmd != 1:  # Only support CONNECT
                return None, None
            
            # Read address
            if atyp == 1:  # IPv4
                addr_data = client_socket.recv(4)
                target_host = socket.inet_ntoa(addr_data)
            elif atyp == 3:  # Domain name
                addr_len = struct.unpack("B", client_socket.recv(1))[0]
                target_host = client_socket.recv(addr_len).decode('utf-8')
            else:
                return None, None
            
            # Read port
            port_data = client_socket.recv(2)
            target_port = struct.unpack("!H", port_data)[0]
            
            return target_host, target_port
            
        except Exception as e:
            self.logger.error(f"SOCKS5 request error: {e}")
            return None, None
    
    def _socks5_reply(self, client_socket, status):
        """Send SOCKS5 reply"""
        try:
            # Reply format: VER REP RSV ATYP BND.ADDR BND.PORT
            reply = struct.pack("BBBB", 5, status, 0, 1)  # IPv4
            reply += socket.inet_aton("0.0.0.0")  # Bind address
            reply += struct.pack("!H", 0)  # Bind port
            client_socket.send(reply)
        except Exception as e:
            self.logger.error(f"SOCKS5 reply error: {e}")
    
    def _http_server_loop(self):
        """HTTP proxy server main loop"""
        self.logger.info("HTTP proxy server loop started")
        
        while self.running:
            try:
                if not self.server_socket:
                    break
                
                # Accept client connection
                client_socket, client_addr = self.server_socket.accept()
                client_addr_str = f"{client_addr[0]}:{client_addr[1]}"
                
                self.logger.info(f"HTTP client connected: {client_addr_str}")
                self.client_connected.emit(client_addr_str)
                
                # Handle client in separate thread
                client_thread = threading.Thread(
                    target=self._handle_http_client,
                    args=(client_socket, client_addr_str),
                    daemon=True
                )
                client_thread.start()
                self.client_threads.append(client_thread)
                
            except OSError:
                # Socket closed
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"Error in HTTP server loop: {e}")
                break
        
        self.logger.info("HTTP proxy server loop ended")
    
    def _handle_http_client(self, client_socket, client_addr):
        """Handle HTTP proxy client connection"""
        try:
            self.logger.debug(f"Reading HTTP request from {client_addr}")
            
            # Read HTTP request
            request_data = b""
            while b"\r\n\r\n" not in request_data:
                chunk = client_socket.recv(1024)
                if not chunk:
                    self.logger.debug(f"No data received from {client_addr}")
                    return
                request_data += chunk
                self.logger.debug(f"Received {len(chunk)} bytes from {client_addr}, total: {len(request_data)}")
            
            self.logger.debug(f"Complete request received from {client_addr}: {len(request_data)} bytes")
            
            # Parse request
            request_lines = request_data.split(b"\r\n")
            request_line = request_lines[0].decode('utf-8')
            
            self.logger.debug(f"Request line from {client_addr}: {request_line}")
            
            if request_line.startswith("CONNECT"):
                # HTTPS tunnel
                self.logger.debug(f"Handling HTTPS tunnel for {client_addr}")
                self._handle_https_tunnel(client_socket, request_line, client_addr)
            else:
                # HTTP request
                self.logger.debug(f"Handling HTTP request for {client_addr}")
                self._handle_http_request(client_socket, request_data, client_addr)
                
        except Exception as e:
            self.logger.error(f"Error handling HTTP client {client_addr}: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
            self.client_disconnected.emit(client_addr)
    
    def _handle_https_tunnel(self, client_socket, request_line, client_addr):
        """Handle HTTPS CONNECT tunnel"""
        try:
            # Parse CONNECT request
            parts = request_line.split()
            if len(parts) < 2:
                return
            
            target = parts[1]
            if ":" in target:
                target_host, target_port = target.rsplit(":", 1)
                target_port = int(target_port)
            else:
                target_host = target
                target_port = 443
            
            # Connect to remote server
            remote_socket = self._connect_to_remote_server()
            if not remote_socket:
                client_socket.send(b"HTTP/1.1 502 Bad Gateway\r\n\r\n")
                return
            
            # For HTTPS CONNECT, we need to send the CONNECT request as data
            # Reconstruct the CONNECT request
            connect_request = f"CONNECT {target_host}:{target_port} HTTP/1.1\r\n"
            connect_request += f"Host: {target_host}:{target_port}\r\n"
            connect_request += "\r\n"
            
            self.logger.debug(f"Sending CONNECT request ({len(connect_request)} bytes): {connect_request.strip()}")
            
            # Send connection request with CONNECT data
            if not self._send_connect_request(remote_socket, target_host, target_port, connect_request.encode('utf-8')):
                client_socket.send(b"HTTP/1.1 502 Bad Gateway\r\n\r\n")
                remote_socket.close()
                return
            
            # Send success response
            client_socket.send(b"HTTP/1.1 200 Connection established\r\n\r\n")
            
            # Start data forwarding
            self._forward_data(client_socket, remote_socket, client_addr)
            
        except Exception as e:
            self.logger.error(f"HTTPS tunnel error: {e}")
    
    def _handle_http_request(self, client_socket, request_data, client_addr):
        """Handle HTTP request"""
        try:
            self.logger.debug(f"Processing HTTP request from {client_addr}")
            
            # Parse the request
            request_lines = request_data.split(b"\r\n")
            request_line = request_lines[0].decode('utf-8')
            headers = [line.decode('utf-8') for line in request_lines[1:] if line]
            
            self.logger.debug(f"Request line: {request_line}")
            self.logger.debug(f"Headers: {headers}")
            
            # Parse the request line
            parts = request_line.split(' ')
            if len(parts) < 3:
                self.logger.error(f"Invalid request line format: {request_line}")
                response = "HTTP/1.1 400 Bad Request\r\n\r\n"
                client_socket.send(response.encode())
                return
            
            method, url, version = parts[0], parts[1], parts[2]
            self.logger.debug(f"Parsed: method={method}, url={url}, version={version}")
            
            # Initialize target host and port
            target_host = None
            target_port = 80
            path = url
            
            # Parse URL to extract host and port
            if url.startswith('http://'):
                # Absolute URL: GET http://example.com/path HTTP/1.1
                url = url[7:]  # Remove http://
                if '/' in url:
                    host_port, path = url.split('/', 1)
                    path = '/' + path
                else:
                    host_port = url
                    path = '/'
                
                if ':' in host_port:
                    target_host, target_port = host_port.split(':', 1)
                    target_port = int(target_port)
                else:
                    target_host = host_port
                    target_port = 80
            else:
                # Relative URL: GET /path HTTP/1.1 with Host header
                # Extract host from Host header
                for header_line in headers:
                    if header_line.lower().startswith('host:'):
                        host_value = header_line[5:].strip()  # Remove 'host:' prefix
                        if ':' in host_value:
                            target_host, target_port = host_value.split(':', 1)
                            target_port = int(target_port)
                        else:
                            target_host = host_value
                            target_port = 80
                        break
            
            # Check if we have a valid target host
            if not target_host:
                self.logger.error(f"Could not determine target host from request: {request_line}")
                response = "HTTP/1.1 400 Bad Request\r\n\r\n"
                client_socket.send(response.encode())
                return
            
            self.logger.debug(f"Target: {target_host}:{target_port}, Path: {path}")
            
            # Connect to remote server
            self.logger.debug(f"Connecting to remote CocoProxy server...")
            remote_socket = self._connect_to_remote_server()
            if not remote_socket:
                self.logger.error(f"Failed to connect to remote CocoProxy server")
                response = "HTTP/1.1 502 Bad Gateway\r\n\r\n"
                client_socket.send(response.encode())
                return
            
            self.logger.debug(f"Connected to remote CocoProxy server successfully")
            
            # Reconstruct the HTTP request
            request_data_str = f"{method} {path} {version}\r\n"
            
            # Add headers, but modify Host header if needed
            host_header_found = False
            for header_line in headers:
                if header_line.lower().startswith('host:'):
                    # Replace with correct host and port
                    if target_port == 80:
                        request_data_str += f"Host: {target_host}\r\n"
                    else:
                        request_data_str += f"Host: {target_host}:{target_port}\r\n"
                    host_header_found = True
                else:
                    request_data_str += header_line + "\r\n"
            
            # Add Host header if not found
            if not host_header_found:
                if target_port == 80:
                    request_data_str += f"Host: {target_host}\r\n"
                else:
                    request_data_str += f"Host: {target_host}:{target_port}\r\n"
            
            request_data_str += "\r\n"
            
            self.logger.debug(f"Reconstructed HTTP request ({len(request_data_str)} bytes):\n{request_data_str}")
            
            # Send connection request with HTTP request data
            self.logger.debug(f"Sending CocoProxy connect request to {target_host}:{target_port}")
            if not self._send_connect_request(remote_socket, target_host, target_port, request_data_str.encode('utf-8')):
                self.logger.error(f"Failed to send CocoProxy connect request")
                response = "HTTP/1.1 502 Bad Gateway\r\n\r\n"
                client_socket.send(response.encode())
                remote_socket.close()
                return
            
            self.logger.debug(f"Successfully sent HTTP request to {target_host}:{target_port}")
            
            # Forward data between client and remote server
            self.logger.debug(f"Starting data forwarding between client and remote server")
            self._forward_data(client_socket, remote_socket, client_addr)
            
        except Exception as e:
            self.logger.error(f"Error handling HTTP request: {e}")
            try:
                response = "HTTP/1.1 500 Internal Server Error\r\n\r\n"
                client_socket.send(response.encode())
            except:
                pass
    
    def _connect_to_remote_server(self):
        """Connect to the remote CocoProxy server"""
        try:
            server_host = self.config.get("server", "host")
            server_port = self.config.get("server", "port")
            
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.settimeout(10)
            remote_socket.connect((server_host, server_port))
            
            return remote_socket
            
        except Exception as e:
            self.logger.error(f"Failed to connect to remote server: {e}")
            return None
    
    def _send_connect_request(self, remote_socket, target_host, target_port, data=None):
        """Send connection request to remote server using CocoProxy protocol"""
        try:
            username = self.config.get("user", "username")
            if not username:
                self.logger.error("No username configured")
                return False
            
            # Get protocol type from configuration
            proxy_protocol = self.config.get("proxy", "protocol")
            if proxy_protocol == "http":
                protocol_type = 0x01  # PROTOCOL_HTTP_HTTPS
            elif proxy_protocol == "socks5":
                protocol_type = 0x03  # PROTOCOL_SOCKS5
            else:
                protocol_type = 0x03  # Default to SOCKS5
                self.logger.warning(f"Unknown protocol '{proxy_protocol}', defaulting to SOCKS5")
            
            # Prepare target data
            if data is not None:
                # For HTTP protocol, send the actual HTTP request data
                target_data = data
            else:
                # For SOCKS5 protocol, send target address
                target_data = f"{target_host}:{target_port}".encode('utf-8')
            
            encrypt_type = 0x00  # ENCRYPT_NONE
            username_bytes = username.encode('utf-8')
            username_len = len(username_bytes)
            
            if username_len > 255:
                self.logger.error(f"Username too long: {username_len}")
                return False
            
            # Create packet
            import struct
            packet = bytearray()
            packet.append(protocol_type)      # 1 byte protocol type
            packet.append(encrypt_type)       # 1 byte encrypt type  
            packet.append(username_len)       # 1 byte username length
            packet.extend(username_bytes)     # N bytes username
            packet.extend(b'\x00\x00\x00\x00') # 4 bytes random padding
            packet.extend(struct.pack('!I', len(target_data))) # 4 bytes data length
            packet.extend(target_data)        # Target data
            
            if data is not None:
                self.logger.debug(f"Sending CocoProxy HTTP request to {target_host}:{target_port} as {username}")
                self.logger.debug(f"Protocol: {protocol_type}, Encrypt: {encrypt_type}, User: {username}, Data: {len(target_data)} bytes")
            else:
                self.logger.debug(f"Sending CocoProxy SOCKS5 request: {target_host}:{target_port} as {username}")
                self.logger.debug(f"Protocol: {protocol_type}, Encrypt: {encrypt_type}, User: {username}, Target: {target_data.decode('utf-8')}, Data: {len(target_data)} bytes")
            
            # Send packet
            remote_socket.send(packet)
            
            # Only perform SOCKS5 handshake if protocol is SOCKS5
            if protocol_type == 0x03:  # PROTOCOL_SOCKS5
                # After sending CocoProxy protocol, we need to perform SOCKS5 handshake
                # since the server expects standard SOCKS5 protocol after CocoProxy setup
                
                # Step 1: Send SOCKS5 authentication request
                auth_request = b'\x05\x01\x00'  # VER=5, NMETHODS=1, METHOD=0 (no auth)
                remote_socket.send(auth_request)
                self.logger.debug("Sent SOCKS5 auth request")
            
                # Step 2: Receive SOCKS5 authentication response
                remote_socket.settimeout(5)
                try:
                    auth_response = remote_socket.recv(2)
                    if len(auth_response) != 2 or auth_response[0] != 0x05 or auth_response[1] != 0x00:
                        self.logger.error(f"SOCKS5 auth failed: {auth_response.hex() if auth_response else 'no response'}")
                        return False
                    self.logger.debug("SOCKS5 auth successful")
                except Exception as e:
                    self.logger.error(f"SOCKS5 auth error: {e}")
                    return False
                
                # Step 3: Send SOCKS5 connect request
                connect_request = bytearray()
                connect_request.extend(b'\x05\x01\x00')  # VER=5, CMD=CONNECT, RSV=0
                
                # Add address type and address
                try:
                    # Try to parse as IP address first
                    import ipaddress
                    ip = ipaddress.ip_address(target_host)
                    if ip.version == 4:
                        connect_request.append(0x01)  # ATYP=IPv4
                        connect_request.extend(ip.packed)
                    else:
                        connect_request.append(0x04)  # ATYP=IPv6
                        connect_request.extend(ip.packed)
                except ValueError:
                    # It's a domain name
                    connect_request.append(0x03)  # ATYP=Domain
                    host_bytes = target_host.encode('utf-8')
                    connect_request.append(len(host_bytes))
                    connect_request.extend(host_bytes)
                
                # Add port
                connect_request.extend(struct.pack('!H', int(target_port)))
                
                remote_socket.send(connect_request)
                self.logger.debug(f"Sent SOCKS5 connect request for {target_host}:{target_port}")
                
                # Step 4: Receive SOCKS5 connect response
                try:
                    connect_response = remote_socket.recv(1024)
                    if len(connect_response) < 4 or connect_response[0] != 0x05:
                        self.logger.error(f"SOCKS5 connect failed: {connect_response.hex() if connect_response else 'no response'}")
                        return False
                    
                    if connect_response[1] != 0x00:
                        self.logger.error(f"SOCKS5 connect error code: {connect_response[1]}")
                        return False
                        
                    self.logger.debug("SOCKS5 connect successful")
                except Exception as e:
                    self.logger.error(f"SOCKS5 connect error: {e}")
                    return False
                finally:
                    remote_socket.settimeout(None)
            else:
                # For HTTP protocol, wait for server acknowledgment
                self.logger.debug(f"Waiting for CocoProxy server acknowledgment for HTTP protocol...")
                try:
                    remote_socket.settimeout(10)
                    # Wait for server response (should be a simple acknowledgment)
                    ack_response = remote_socket.recv(1024)
                    if not ack_response:
                        self.logger.error("No acknowledgment received from CocoProxy server")
                        return False
                    
                    self.logger.debug(f"Received server acknowledgment: {ack_response.hex() if len(ack_response) <= 20 else ack_response[:20].hex() + '...'}")
                    
                    # Try to decode as text to see if it's an HTTP response
                    try:
                        response_text = ack_response.decode('utf-8', errors='ignore')
                        if response_text.startswith('HTTP/'):
                            self.logger.error(f"Server returned HTTP response instead of CocoProxy protocol: {response_text[:100]}")
                            return False
                    except:
                        pass
                    
                    # Check if it's an error response
                    if len(ack_response) >= 1:
                        status_code = ack_response[0]
                        if status_code != 0x00:  # 0x00 should indicate success
                            self.logger.error(f"CocoProxy server returned error status: {status_code} (0x{status_code:02x})")
                            if status_code == 0x48:  # ASCII 'H' - might be HTTP response
                                try:
                                    response_text = ack_response.decode('utf-8', errors='ignore')
                                    self.logger.error(f"Server response (possibly HTTP): {response_text[:200]}")
                                except:
                                    pass
                            return False
                    
                    self.logger.debug(f"HTTP protocol setup complete for {target_host}:{target_port}")
                    
                except socket.timeout:
                    self.logger.error("Timeout waiting for CocoProxy server acknowledgment")
                    return False
                except Exception as e:
                    self.logger.error(f"Error waiting for server acknowledgment: {e}")
                    return False
                finally:
                    remote_socket.settimeout(None)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send connect request: {e}")
            return False
    
    def _forward_data(self, client_socket, remote_socket, client_addr):
        """Forward data between client and remote server"""
        def forward(source, destination, direction):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    
                    destination.send(data)
                    self.traffic_data.emit(len(data))
                    
            except Exception as e:
                self.logger.debug(f"Forward {direction} error: {e}")
            finally:
                try:
                    source.close()
                    destination.close()
                except:
                    pass
        
        # Start forwarding in both directions
        client_to_remote = threading.Thread(
            target=forward,
            args=(client_socket, remote_socket, "client->remote"),
            daemon=True
        )
        remote_to_client = threading.Thread(
            target=forward,
            args=(remote_socket, client_socket, "remote->client"),
            daemon=True
        )
        
        client_to_remote.start()
        remote_to_client.start()
        
        # Wait for both threads to finish
        client_to_remote.join()
        remote_to_client.join()
        
        self.logger.debug(f"Data forwarding ended for {client_addr}")
    
    def is_running(self):
        """Check if proxy server is running"""
        return self.running