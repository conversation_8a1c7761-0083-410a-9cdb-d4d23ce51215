package com.cocoproxy.client.ui.viewmodel;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u00aa\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\u001e\b\u0007\u0018\u0000 b2\u00020\u0001:\u0001bB!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0006\u0010C\u001a\u000200J\u0006\u0010D\u001a\u00020EJ\u0006\u0010F\u001a\u00020EJ\u001e\u0010G\u001a\u00020E2\u0006\u0010\f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0012H\u0082@\u00a2\u0006\u0002\u0010HJ\u001e\u0010I\u001a\u00020E2\u0006\u0010\f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0012H\u0082@\u00a2\u0006\u0002\u0010HJ\u0006\u0010J\u001a\u00020EJ\u0006\u0010K\u001a\u00020EJ\u000e\u0010L\u001a\u00020E2\u0006\u0010M\u001a\u00020\u000eJ\u000e\u0010N\u001a\u00020E2\u0006\u0010M\u001a\u00020\u0012J\u000e\u0010O\u001a\u00020E2\u0006\u0010M\u001a\u00020\u0015J\u000e\u0010P\u001a\u00020E2\u0006\u0010M\u001a\u00020\u0018J\u000e\u0010Q\u001a\u00020E2\u0006\u0010M\u001a\u00020\u0015J\u000e\u0010R\u001a\u00020E2\u0006\u0010M\u001a\u00020\u000eJ\u0006\u0010S\u001a\u00020EJ\u0006\u0010T\u001a\u00020EJ\u0006\u0010U\u001a\u00020EJ\u000e\u0010V\u001a\u00020E2\u0006\u0010W\u001a\u00020;J\u0006\u0010X\u001a\u00020EJ\u000e\u0010Y\u001a\u00020E2\u0006\u0010Z\u001a\u00020@J\b\u0010[\u001a\u00020EH\u0002J\b\u0010\\\u001a\u00020EH\u0002J\u0006\u0010]\u001a\u00020EJ\u0006\u0010^\u001a\u00020EJ\u0006\u0010_\u001a\u00020EJ\b\u0010`\u001a\u00020EH\u0002J\b\u0010a\u001a\u00020EH\u0014R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00150\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0010R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0010R\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u001b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0010R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0010R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0010R\u0019\u0010#\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010$0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010\u0010R\u0014\u0010&\u001a\b\u0012\u0004\u0012\u00020(0\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010)\u001a\b\u0012\u0004\u0012\u00020(0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0010R\u0017\u0010+\u001a\b\u0012\u0004\u0012\u00020,0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u0010R\u0014\u0010.\u001a\b\u0012\u0004\u0012\u0002000/X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u00101\u001a\b\u0012\u0004\u0012\u00020002\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u00104R\u0014\u00105\u001a\b\u0012\u0004\u0012\u0002000\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u00106\u001a\b\u0012\u0004\u0012\u0002000\r\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010\u0010R\u0016\u00107\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010$0\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u00108\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010$0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010\u0010R\u0016\u0010:\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010;0\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010<\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010;0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010\u0010R\u001a\u0010>\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020@0?0\'X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010A\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020@0?0\r\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010\u0010\u00a8\u0006c"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/MainViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "cocoProxyClient", "Lcom/cocoproxy/client/core/CocoProxyClient;", "configRepository", "Lcom/cocoproxy/client/data/repository/ConfigRepository;", "<init>", "(Landroid/app/Application;Lcom/cocoproxy/client/core/CocoProxyClient;Lcom/cocoproxy/client/data/repository/ConfigRepository;)V", "context", "Landroid/content/Context;", "serverConfig", "Lkotlinx/coroutines/flow/StateFlow;", "Lcom/cocoproxy/client/data/model/ServerConfig;", "getServerConfig", "()Lkotlinx/coroutines/flow/StateFlow;", "proxyConfig", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "getProxyConfig", "uiConfig", "Lcom/cocoproxy/client/data/model/UiConfig;", "getUiConfig", "vpnConfig", "Lcom/cocoproxy/client/data/model/VpnConfig;", "getVpnConfig", "userProfile", "Lcom/cocoproxy/client/data/model/UserProfile;", "getUserProfile", "connectionState", "Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;", "getConnectionState", "trafficData", "Lcom/cocoproxy/client/data/model/TrafficData;", "getTrafficData", "errorMessage", "", "getErrorMessage", "_proxyStatus", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/cocoproxy/client/data/model/ProxyStatus;", "proxyStatus", "getProxyStatus", "connectionStatus", "Lcom/cocoproxy/client/data/model/ConnectionStatus;", "getConnectionStatus", "_vpnPermissionRequest", "Landroidx/lifecycle/MutableLiveData;", "", "vpnPermissionRequest", "Landroidx/lifecycle/LiveData;", "getVpnPermissionRequest", "()Landroidx/lifecycle/LiveData;", "_isLoading", "isLoading", "_snackbarMessage", "snackbarMessage", "getSnackbarMessage", "_navigationEvent", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "navigationEvent", "getNavigationEvent", "_logs", "", "Lcom/cocoproxy/client/data/model/LogEntry;", "logs", "getLogs", "isLoginRequired", "startProxy", "", "stopProxy", "startRegularProxy", "(Lcom/cocoproxy/client/data/model/ServerConfig;Lcom/cocoproxy/client/data/model/ProxyConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startVpnProxy", "onVpnPermissionGranted", "onVpnPermissionDenied", "saveServerConfig", "config", "saveProxyConfig", "saveUiConfig", "updateVpnConfig", "updateUiConfig", "testConnection", "clearSnackbarMessage", "clearNavigationEvent", "clearVpnPermissionRequest", "navigateTo", "event", "clearLogs", "addLog", "entry", "observeConnectionState", "observeErrorMessages", "onActivityResumed", "onActivityPaused", "onActivityDestroyed", "autoConnectIfPossible", "onCleared", "Companion", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MainViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.CocoProxyClient cocoProxyClient = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.repository.ConfigRepository configRepository = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "MainViewModel";
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ServerConfig> serverConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ProxyConfig> proxyConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.UiConfig> uiConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.VpnConfig> vpnConfig = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.UserProfile> userProfile = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> connectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> trafficData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.data.model.ProxyStatus> _proxyStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ProxyStatus> proxyStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ConnectionStatus> connectionStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _vpnPermissionRequest = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> vpnPermissionRequest = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _snackbarMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> snackbarMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.ui.viewmodel.NavigationEvent> _navigationEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.ui.viewmodel.NavigationEvent> navigationEvent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.cocoproxy.client.data.model.LogEntry>> _logs = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.LogEntry>> logs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.ui.viewmodel.MainViewModel.Companion Companion = null;
    
    @javax.inject.Inject()
    public MainViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient cocoProxyClient, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.repository.ConfigRepository configRepository) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ServerConfig> getServerConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ProxyConfig> getProxyConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.UiConfig> getUiConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.VpnConfig> getVpnConfig() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.UserProfile> getUserProfile() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> getConnectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> getTrafficData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ProxyStatus> getProxyStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.ConnectionStatus> getConnectionStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getVpnPermissionRequest() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getSnackbarMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.ui.viewmodel.NavigationEvent> getNavigationEvent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.cocoproxy.client.data.model.LogEntry>> getLogs() {
        return null;
    }
    
    /**
     * Check if login is required
     */
    public final boolean isLoginRequired() {
        return false;
    }
    
    /**
     * Start proxy service
     */
    public final void startProxy() {
    }
    
    /**
     * Stop proxy service
     */
    public final void stopProxy() {
    }
    
    /**
     * Start regular proxy service
     */
    private final java.lang.Object startRegularProxy(com.cocoproxy.client.data.model.ServerConfig serverConfig, com.cocoproxy.client.data.model.ProxyConfig proxyConfig, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Start VPN proxy service
     */
    private final java.lang.Object startVpnProxy(com.cocoproxy.client.data.model.ServerConfig serverConfig, com.cocoproxy.client.data.model.ProxyConfig proxyConfig, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Handle VPN permission granted
     */
    public final void onVpnPermissionGranted() {
    }
    
    /**
     * Handle VPN permission denied
     */
    public final void onVpnPermissionDenied() {
    }
    
    /**
     * Save server configuration
     */
    public final void saveServerConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig config) {
    }
    
    /**
     * Save proxy configuration
     */
    public final void saveProxyConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig config) {
    }
    
    /**
     * Save UI configuration
     */
    public final void saveUiConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UiConfig config) {
    }
    
    /**
     * Update VPN configuration
     */
    public final void updateVpnConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.VpnConfig config) {
    }
    
    /**
     * Update UI configuration
     */
    public final void updateUiConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UiConfig config) {
    }
    
    /**
     * Test connection to server
     */
    public final void testConnection(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig config) {
    }
    
    /**
     * Clear snackbar message
     */
    public final void clearSnackbarMessage() {
    }
    
    /**
     * Clear navigation event
     */
    public final void clearNavigationEvent() {
    }
    
    /**
     * Clear VPN permission request
     */
    public final void clearVpnPermissionRequest() {
    }
    
    /**
     * Navigate to screen
     */
    public final void navigateTo(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.viewmodel.NavigationEvent event) {
    }
    
    /**
     * Clear logs
     */
    public final void clearLogs() {
    }
    
    /**
     * Add log entry
     */
    public final void addLog(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.LogEntry entry) {
    }
    
    /**
     * Observe connection state changes
     */
    private final void observeConnectionState() {
    }
    
    /**
     * Observe error messages
     */
    private final void observeErrorMessages() {
    }
    
    /**
     * Activity lifecycle callbacks
     */
    public final void onActivityResumed() {
    }
    
    public final void onActivityPaused() {
    }
    
    public final void onActivityDestroyed() {
    }
    
    /**
     * Auto-connect if credentials are saved and remember me is enabled
     */
    private final void autoConnectIfPossible() {
    }
    
    @java.lang.Override()
    protected void onCleared() {
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/MainViewModel$Companion;", "", "<init>", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}