<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Modern gradient background -->
    <radialGradient id="bgGrad" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#F7931E;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD23F;stop-opacity:1" />
    </radialGradient>
    <!-- Coconut shell gradient -->
    <radialGradient id="cocoGrad" cx="50%" cy="40%" r="60%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#F5F5DC;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#DEB887;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B7355;stop-opacity:1" />
    </radialGradient>
    <!-- Shield gradient -->
    <linearGradient id="shieldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#357ABD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E3A8A;stop-opacity:1" />
    </linearGradient>
    <!-- Glow effect -->
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle with shadow -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGrad)" stroke="#E65100" stroke-width="4" filter="url(#glow)"/>
  
  <!-- Coconut main body -->
  <ellipse cx="128" cy="110" rx="70" ry="80" fill="url(#cocoGrad)" stroke="#8B7355" stroke-width="3"/>
  
  <!-- Coconut fiber texture lines -->
  <g stroke="#A0522D" stroke-width="2" fill="none" opacity="0.6">
    <path d="M70 70 Q128 60 186 70"/>
    <path d="M65 85 Q128 75 191 85"/>
    <path d="M68 100 Q128 90 188 100"/>
    <path d="M70 115 Q128 105 186 115"/>
    <path d="M72 130 Q128 120 184 130"/>
    <path d="M75 145 Q128 135 181 145"/>
  </g>
  
  <!-- Coconut three holes (eyes) -->
  <circle cx="105" cy="85" r="8" fill="#2C1810" stroke="#1A0F08" stroke-width="2"/>
  <circle cx="128" cy="75" r="8" fill="#2C1810" stroke="#1A0F08" stroke-width="2"/>
  <circle cx="151" cy="85" r="8" fill="#2C1810" stroke="#1A0F08" stroke-width="2"/>
  
  <!-- Inner hole highlights -->
  <circle cx="107" cy="83" r="2" fill="#4A4A4A"/>
  <circle cx="130" cy="73" r="2" fill="#4A4A4A"/>
  <circle cx="153" cy="83" r="2" fill="#4A4A4A"/>
  
  <!-- Modern shield design -->
  <path d="M128 140 L170 155 L170 190 Q170 210 128 225 Q86 210 86 190 L86 155 Z" fill="url(#shieldGrad)" stroke="#1E3A8A" stroke-width="3" filter="url(#glow)"/>
  
  <!-- Lock icon in shield -->
  <rect x="118" y="180" width="20" height="20" rx="3" fill="#FFFFFF" stroke="#E0E0E0" stroke-width="1"/>
  <path d="M122 180 L122 175 Q122 170 128 170 Q134 170 134 175 L134 180" fill="none" stroke="#FFFFFF" stroke-width="3" stroke-linecap="round"/>
  
  <!-- Network connection indicators -->
  <circle cx="40" cy="128" r="6" fill="#4CAF50" filter="url(#glow)"/>
  <circle cx="216" cy="128" r="6" fill="#4CAF50" filter="url(#glow)"/>
  <circle cx="128" cy="40" r="6" fill="#4CAF50" filter="url(#glow)"/>
  
  <!-- Connection lines -->
  <line x1="46" y1="128" x2="82" y2="128" stroke="#4CAF50" stroke-width="3" opacity="0.7"/>
  <line x1="174" y1="128" x2="210" y2="128" stroke="#4CAF50" stroke-width="3" opacity="0.7"/>
  <line x1="128" y1="46" x2="128" y2="82" stroke="#4CAF50" stroke-width="3" opacity="0.7"/>
  
  <!-- Connection lines -->
  <line x1="14" y1="32" x2="22" y2="40" stroke="#32CD32" stroke-width="1.5" opacity="0.6" stroke-dasharray="2,2"/>
  <line x1="50" y1="32" x2="42" y2="40" stroke="#32CD32" stroke-width="1.5" opacity="0.6" stroke-dasharray="2,2"/>
  <line x1="32" y1="14" x2="32" y2="35" stroke="#32CD32" stroke-width="1.5" opacity="0.6" stroke-dasharray="2,2"/>
</svg>