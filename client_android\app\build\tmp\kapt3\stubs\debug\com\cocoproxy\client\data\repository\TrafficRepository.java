package com.cocoproxy.client.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010%\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001B\u001b\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00120\u0014H\u0086@\u00a2\u0006\u0002\u0010\u0015J&\u0010\u0016\u001a\u00020\n2\u0006\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u00192\u0006\u0010\u001b\u001a\u00020\u001cJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u00122\u0006\u0010\u0017\u001a\u00020\u0011J\u0006\u0010\u001d\u001a\u00020\nJ\u000e\u0010\u001e\u001a\u00020\n2\u0006\u0010\u0017\u001a\u00020\u0011J\u0006\u0010\u001f\u001a\u00020\u0019J\u0006\u0010 \u001a\u00020\u0019J\u0006\u0010!\u001a\u00020\u0019J\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00120\u0014J\f\u0010#\u001a\b\u0012\u0004\u0012\u00020\u00120\u0014J\u0010\u0010$\u001a\u00020\u00112\u0006\u0010\u0017\u001a\u00020\u0011H\u0002J\u0006\u0010%\u001a\u00020\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u001a\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u0011\u0012\u0004\u0012\u00020\u00120\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/cocoproxy/client/data/repository/TrafficRepository;", "", "context", "Landroid/content/Context;", "appManager", "Lcom/cocoproxy/client/core/AppManager;", "<init>", "(Landroid/content/Context;Lcom/cocoproxy/client/core/AppManager;)V", "_trafficUpdates", "Lkotlinx/coroutines/flow/MutableSharedFlow;", "", "trafficUpdates", "Lkotlinx/coroutines/flow/Flow;", "getTrafficUpdates", "()Lkotlinx/coroutines/flow/Flow;", "trafficStatsMap", "", "", "Lcom/cocoproxy/client/data/model/AppTrafficStats;", "getAppTrafficStats", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAppTraffic", "packageName", "uploadBytes", "", "downloadBytes", "isProxied", "", "clearTrafficStats", "clearAppTrafficStats", "getTotalUploadBytes", "getTotalDownloadBytes", "getTotalBytes", "getProxiedAppsTraffic", "getDirectAppsTraffic", "getAppName", "generateMockTrafficData", "app_debug"})
public final class TrafficRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.AppManager appManager = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableSharedFlow<kotlin.Unit> _trafficUpdates = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<kotlin.Unit> trafficUpdates = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, com.cocoproxy.client.data.model.AppTrafficStats> trafficStatsMap = null;
    
    @javax.inject.Inject()
    public TrafficRepository(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.AppManager appManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<kotlin.Unit> getTrafficUpdates() {
        return null;
    }
    
    /**
     * 获取所有应用的流量统计
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAppTrafficStats(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.cocoproxy.client.data.model.AppTrafficStats>> $completion) {
        return null;
    }
    
    /**
     * 更新应用流量统计
     */
    public final void updateAppTraffic(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName, long uploadBytes, long downloadBytes, boolean isProxied) {
    }
    
    /**
     * 获取指定应用的流量统计
     */
    @org.jetbrains.annotations.Nullable()
    public final com.cocoproxy.client.data.model.AppTrafficStats getAppTrafficStats(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName) {
        return null;
    }
    
    /**
     * 清除所有流量统计
     */
    public final void clearTrafficStats() {
    }
    
    /**
     * 清除指定应用的流量统计
     */
    public final void clearAppTrafficStats(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName) {
    }
    
    /**
     * 获取总上传流量
     */
    public final long getTotalUploadBytes() {
        return 0L;
    }
    
    /**
     * 获取总下载流量
     */
    public final long getTotalDownloadBytes() {
        return 0L;
    }
    
    /**
     * 获取总流量
     */
    public final long getTotalBytes() {
        return 0L;
    }
    
    /**
     * 获取代理应用的流量统计
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.AppTrafficStats> getProxiedAppsTraffic() {
        return null;
    }
    
    /**
     * 获取直连应用的流量统计
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.AppTrafficStats> getDirectAppsTraffic() {
        return null;
    }
    
    /**
     * 根据包名获取应用名称
     */
    private final java.lang.String getAppName(java.lang.String packageName) {
        return null;
    }
    
    /**
     * 模拟流量数据（用于测试）
     */
    public final void generateMockTrafficData() {
    }
}