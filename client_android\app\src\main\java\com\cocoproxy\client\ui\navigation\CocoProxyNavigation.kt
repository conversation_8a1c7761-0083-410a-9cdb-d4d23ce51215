package com.cocoproxy.client.ui.navigation

import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.cocoproxy.client.ui.screen.*
import com.cocoproxy.client.ui.viewmodel.MainViewModel

/**
 * Main navigation destinations
 */
sealed class Screen(val route: String, val title: String, val icon: ImageVector) {
    object Login : Screen("login", "Login", Icons.Filled.Login)
    object Home : Screen("home", "Home", Icons.Filled.Home)
    object Traffic : Screen("traffic", "Traffic", Icons.Filled.Analytics)
    object Settings : Screen("settings", "Settings", Icons.Filled.Settings)
    object Logs : Screen("logs", "Logs", Icons.Filled.List)
    object About : Screen("about", "About", Icons.Filled.Info)
    object ServerConfig : Screen("server_config", "Server", Icons.Filled.Storage)
    object ProxyConfig : Screen("proxy_config", "Proxy", Icons.Filled.Router)
    object AppSelection : Screen("app_selection", "App Selection", Icons.Filled.Apps)
    object AppSplitMain : Screen("app_split_main", "App Split", Icons.Filled.Apps)
    object SplitModeConfig : Screen("split_mode_config", "Split Mode", Icons.Filled.FilterList)
    object AppSelectionCompose : Screen("app_selection_compose", "Select Apps", Icons.Filled.CheckBox)
    object AppTrafficStats : Screen("app_traffic_stats", "Traffic Stats", Icons.Filled.Analytics)
}

/**
 * Bottom navigation items
 */
val bottomNavItems = listOf(
    Screen.Home,
    Screen.Traffic,
    Screen.Settings
)

/**
 * Main navigation component
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CocoProxyNavigation(
    onVpnPermissionRequest: () -> Unit
) {
    val navController = rememberNavController()
    val mainViewModel: MainViewModel = hiltViewModel()
    
    // Observe navigation events
    LaunchedEffect(Unit) {
        mainViewModel.navigationEvent.collect { event ->
            android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigation event received: $event")
            event?.let {
                when (it) {
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToSettings -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to Settings")
                        navController.navigate(Screen.Settings.route)
                    }
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToLogs -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to Logs")
                        navController.navigate(Screen.Logs.route)
                    }
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToAbout -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to About")
                        navController.navigate(Screen.About.route)
                    }
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToServerConfig -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to ServerConfig")
                        navController.navigate(Screen.ServerConfig.route)
                    }
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToProxyConfig -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to ProxyConfig")
                        navController.navigate(Screen.ProxyConfig.route)
                    }
                    is com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToAppSelection -> {
                        android.util.Log.d("com.cocoproxy.client.CocoProxyNavigation", "Navigating to AppSelection")
                        navController.navigate(Screen.AppSelection.route)
                    }
                }
                mainViewModel.clearNavigationEvent()
            }
        }
    }
    
    Scaffold(
        bottomBar = {
            NavigationBar {
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                val currentDestination = navBackStackEntry?.destination
                
                bottomNavItems.forEach { screen ->
                    NavigationBarItem(
                        icon = { Icon(screen.icon, contentDescription = screen.title) },
                        label = { Text(screen.title) },
                        selected = currentDestination?.hierarchy?.any { it.route == screen.route } == true,
                        onClick = {
                            navController.navigate(screen.route) {
                                // Pop up to the start destination of the graph to
                                // avoid building up a large stack of destinations
                                // on the back stack as users select items
                                popUpTo(navController.graph.findStartDestination().id) {
                                    saveState = true
                                }
                                // Avoid multiple copies of the same destination when
                                // reselecting the same item
                                launchSingleTop = true
                                // Restore state when reselecting a previously selected item
                                restoreState = true
                            }
                        }
                    )
                }
            }
        },
        snackbarHost = {
            val snackbarMessage by mainViewModel.snackbarMessage.collectAsState()
            snackbarMessage?.let { message ->
                LaunchedEffect(message) {
                    // Show snackbar and clear message
                    mainViewModel.clearSnackbarMessage()
                }
                SnackbarHost(
                    hostState = remember { SnackbarHostState() }.apply {
                        LaunchedEffect(message) {
                            showSnackbar(message)
                        }
                    }
                )
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = Screen.Home.route,
            modifier = Modifier.padding(innerPadding)
        ) {
            composable(Screen.Login.route) {
                LoginScreen(
                    onLoginSuccess = {
                        navController.navigate(Screen.Home.route) {
                            popUpTo(Screen.Login.route) { inclusive = true }
                        }
                    },
                    onCancel = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.Home.route) {
                HomeScreen(
                    onNavigateToServerConfig = {
                        navController.navigate(Screen.ServerConfig.route)
                    },
                    onNavigateToProxyConfig = {
                        navController.navigate(Screen.ProxyConfig.route)
                    },
                    onVpnPermissionRequest = onVpnPermissionRequest,
                    onNavigateToLogin = {
                        navController.navigate(Screen.Login.route)
                    }
                )
            }
            
            composable(Screen.Traffic.route) {
                TrafficScreen()
            }
            
            composable(Screen.Settings.route) {
                SettingsScreen(
                    onNavigateToServerConfig = {
                        navController.navigate(Screen.ServerConfig.route)
                    },
                    onNavigateToProxyConfig = {
                        navController.navigate(Screen.ProxyConfig.route)
                    },
                    onNavigateToAppSelection = {
                        navController.navigate(Screen.AppSelection.route)
                    }
                )
            }
            
            composable(Screen.Logs.route) {
                LogsScreen()
            }
            
            composable(Screen.About.route) {
                AboutScreen()
            }
            
            composable(Screen.ServerConfig.route) {
                ServerConfigScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.ProxyConfig.route) {
                ProxyConfigScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.AppSelection.route) {
                AppSplitMainScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onNavigateToModeConfig = {
                        navController.navigate(Screen.SplitModeConfig.route)
                    },
                    onNavigateToAppSelection = {
                        navController.navigate(Screen.AppSelectionCompose.route)
                    },
                    onNavigateToTrafficStats = {
                        navController.navigate(Screen.AppTrafficStats.route)
                    }
                )
            }
            
            composable(Screen.SplitModeConfig.route) {
                SplitModeConfigScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.AppSelectionCompose.route) {
                AppSelectionComposeScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
            
            composable(Screen.AppTrafficStats.route) {
                AppTrafficStatsScreen(
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}