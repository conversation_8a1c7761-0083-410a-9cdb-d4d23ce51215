package com.cocoproxy.client.core;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0084\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0012\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u000f\b\u0007\u0018\u0000 C2\u00020\u0001:\u0002CDB\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001e\u0010(\u001a\u00020)2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u0086@\u00a2\u0006\u0002\u0010*J\u001e\u0010+\u001a\u00020)2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u0082@\u00a2\u0006\u0002\u0010*J\u000e\u0010,\u001a\u00020)H\u0082@\u00a2\u0006\u0002\u0010-J\b\u0010.\u001a\u00020/H\u0002J\u0006\u00100\u001a\u00020/J\b\u00101\u001a\u00020/H\u0002J\u0006\u00102\u001a\u00020/J,\u00103\u001a\u0004\u0018\u00010\u00162\u0006\u00104\u001a\u00020\u00122\u0006\u00105\u001a\u0002062\n\b\u0002\u00107\u001a\u0004\u0018\u00010 H\u0086@\u00a2\u0006\u0002\u00108JB\u00109\u001a\u00020)2\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\u0006\u00104\u001a\u00020\u00122\u0006\u00105\u001a\u0002062\n\b\u0002\u00107\u001a\u0004\u0018\u00010 H\u0082@\u00a2\u0006\u0002\u0010:J.\u0010;\u001a\u00020)2\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u00104\u001a\u00020\u00122\u0006\u00105\u001a\u000206H\u0082@\u00a2\u0006\u0002\u0010<J\u0010\u0010=\u001a\u00020/2\u0006\u0010>\u001a\u00020\u0012H\u0002J\u0010\u0010?\u001a\u00020 2\u0006\u0010@\u001a\u00020 H\u0002J\u0010\u0010A\u001a\u00020 2\u0006\u0010B\u001a\u00020 H\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\fR\u0016\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00120\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0013\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00120\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\fR\u0010\u0010\u0015\u001a\u0004\u0018\u00010\u0016X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0017\u001a\u0004\u0018\u00010\u0018X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u001eX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001f\u001a\u0004\u0018\u00010 X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010!\u001a\u0004\u0018\u00010\"X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010#\u001a\u00020$X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010%\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\'\u001a\u0004\u0018\u00010&X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006E"}, d2 = {"Lcom/cocoproxy/client/core/CocoProxyClient;", "", "api", "Lcom/cocoproxy/client/data/api/CocoProxyApi;", "<init>", "(Lcom/cocoproxy/client/data/api/CocoProxyApi;)V", "_connectionState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;", "connectionState", "Lkotlinx/coroutines/flow/StateFlow;", "getConnectionState", "()Lkotlinx/coroutines/flow/StateFlow;", "_trafficData", "Lcom/cocoproxy/client/data/model/TrafficData;", "trafficData", "getTrafficData", "_errorMessage", "", "errorMessage", "getErrorMessage", "tcpSocket", "Ljava/net/Socket;", "inputStream", "Ljava/io/InputStream;", "outputStream", "Ljava/io/OutputStream;", "serverConfig", "Lcom/cocoproxy/client/data/model/ServerConfig;", "proxyConfig", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "encryptionKey", "", "cipher", "Ljavax/crypto/Cipher;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "heartbeatJob", "Lkotlinx/coroutines/Job;", "connectionJob", "connect", "", "(Lcom/cocoproxy/client/data/model/ServerConfig;Lcom/cocoproxy/client/data/model/ProxyConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendCocoProxyProtocol", "performSocks5Handshake", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startMessageHandler", "", "disconnect", "startHeartbeat", "cleanup", "createProxyConnection", "targetHost", "targetPort", "", "initialData", "(Ljava/lang/String;I[BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "sendProxyConnectionRequest", "(Ljava/io/OutputStream;Lcom/cocoproxy/client/data/model/ServerConfig;Lcom/cocoproxy/client/data/model/ProxyConfig;Ljava/lang/String;I[BLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "performProxyConnectionSocks5Handshake", "(Ljava/io/InputStream;Ljava/io/OutputStream;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "initializeEncryption", "encryptionType", "encrypt", "data", "decrypt", "encryptedData", "Companion", "ConnectionState", "app_debug"})
public final class CocoProxyClient {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.api.CocoProxyApi api = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "CocoProxyClient";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PROTOCOL_VERSION = "1.0";
    private static final long HEARTBEAT_INTERVAL = 30000L;
    private static final int CONNECT_TIMEOUT = 10000;
    private static final int READ_TIMEOUT = 30000;
    private static final byte PROTOCOL_HTTP_HTTPS = (byte)1;
    private static final byte PROTOCOL_TCP = (byte)2;
    private static final byte PROTOCOL_SOCKS5 = (byte)3;
    private static final byte ENCRYPT_NONE = (byte)0;
    private static final byte ENCRYPT_AES = (byte)1;
    private static final byte ENCRYPT_CHACHA20 = (byte)2;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> _connectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> connectionState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.data.model.TrafficData> _trafficData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> trafficData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _errorMessage = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> errorMessage = null;
    @org.jetbrains.annotations.Nullable()
    private java.net.Socket tcpSocket;
    @org.jetbrains.annotations.Nullable()
    private java.io.InputStream inputStream;
    @org.jetbrains.annotations.Nullable()
    private java.io.OutputStream outputStream;
    @org.jetbrains.annotations.Nullable()
    private com.cocoproxy.client.data.model.ServerConfig serverConfig;
    @org.jetbrains.annotations.Nullable()
    private com.cocoproxy.client.data.model.ProxyConfig proxyConfig;
    @org.jetbrains.annotations.Nullable()
    private byte[] encryptionKey;
    @org.jetbrains.annotations.Nullable()
    private javax.crypto.Cipher cipher;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job heartbeatJob;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job connectionJob;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.core.CocoProxyClient.Companion Companion = null;
    
    @javax.inject.Inject()
    public CocoProxyClient(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.api.CocoProxyApi api) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> getConnectionState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> getTrafficData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getErrorMessage() {
        return null;
    }
    
    /**
     * Connect to CocoProxy server using TCP and CocoProxy protocol
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object connect(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig serverConfig, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig proxyConfig, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Send CocoProxy protocol request
     */
    private final java.lang.Object sendCocoProxyProtocol(com.cocoproxy.client.data.model.ServerConfig serverConfig, com.cocoproxy.client.data.model.ProxyConfig proxyConfig, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Perform SOCKS5 handshake after CocoProxy protocol setup
     */
    private final java.lang.Object performSocks5Handshake(kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Start message handler for incoming data
     */
    private final void startMessageHandler() {
    }
    
    /**
     * Disconnect from server
     */
    public final void disconnect() {
    }
    
    /**
     * Start heartbeat (simplified for TCP connection)
     */
    private final void startHeartbeat() {
    }
    
    /**
     * Clean up resources
     */
    public final void cleanup() {
    }
    
    /**
     * Create a new connection to target through CocoProxy server
     * This creates a separate socket connection for each proxy request
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object createProxyConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String targetHost, int targetPort, @org.jetbrains.annotations.Nullable()
    byte[] initialData, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.net.Socket> $completion) {
        return null;
    }
    
    /**
     * Send CocoProxy protocol request for a specific target connection
     */
    private final java.lang.Object sendProxyConnectionRequest(java.io.OutputStream outputStream, com.cocoproxy.client.data.model.ServerConfig serverConfig, com.cocoproxy.client.data.model.ProxyConfig proxyConfig, java.lang.String targetHost, int targetPort, byte[] initialData, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Perform SOCKS5 handshake for proxy connection
     */
    private final java.lang.Object performProxyConnectionSocks5Handshake(java.io.InputStream inputStream, java.io.OutputStream outputStream, java.lang.String targetHost, int targetPort, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Initialize encryption (placeholder for future implementation)
     */
    private final void initializeEncryption(java.lang.String encryptionType) {
    }
    
    /**
     * Encrypt data (placeholder for future implementation)
     */
    private final byte[] encrypt(byte[] data) {
        return null;
    }
    
    /**
     * Decrypt data (placeholder for future implementation)
     */
    private final byte[] decrypt(byte[] encryptedData) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0005\n\u0002\b\u0006\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\nX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\rX\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/cocoproxy/client/core/CocoProxyClient$Companion;", "", "<init>", "()V", "TAG", "", "PROTOCOL_VERSION", "HEARTBEAT_INTERVAL", "", "CONNECT_TIMEOUT", "", "READ_TIMEOUT", "PROTOCOL_HTTP_HTTPS", "", "PROTOCOL_TCP", "PROTOCOL_SOCKS5", "ENCRYPT_NONE", "ENCRYPT_AES", "ENCRYPT_CHACHA20", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;", "", "<init>", "(Ljava/lang/String;I)V", "DISCONNECTED", "CONNECTING", "CONNECTED", "RECONNECTING", "ERROR", "app_debug"})
    public static enum ConnectionState {
        /*public static final*/ DISCONNECTED /* = new DISCONNECTED() */,
        /*public static final*/ CONNECTING /* = new CONNECTING() */,
        /*public static final*/ CONNECTED /* = new CONNECTED() */,
        /*public static final*/ RECONNECTING /* = new RECONNECTING() */,
        /*public static final*/ ERROR /* = new ERROR() */;
        
        ConnectionState() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> getEntries() {
            return null;
        }
    }
}