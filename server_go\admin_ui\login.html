<!DOCTYPE html>
<html>
<head>
    <title>CocoProxy Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .login-container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 350px;
        }
        h1 {
            text-align: center;
            color: #4a6ee0;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #4a6ee0;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #3a5ecc;
        }
        .alert {
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .note {
            margin-top: 20px;
            text-align: center;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>CocoProxy Login</h1>
        <div id="alert" class="alert"></div>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <div class="note">
            Default credentials: admin / admin123
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const alert = document.getElementById('alert');
            
            alert.style.display = 'block';
            alert.textContent = 'Logging in...';
            alert.className = 'alert';
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username,
                        password
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert.textContent = 'Login successful! Redirecting...';
                    alert.className = 'alert alert-success';
                    
                    // Store token in localStorage
                    localStorage.setItem('authToken', data.token);
                    localStorage.setItem('username', data.username);
                    
                    // Redirect to index.html after 1 second
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    alert.textContent = `Login failed: ${data.error || 'Unknown error'}`;
                    alert.className = 'alert alert-error';
                }
            } catch (error) {
                alert.textContent = `Network error: ${error.message}`;
                alert.className = 'alert alert-error';
                console.error('Login error:', error);
            }
        });
    </script>
</body>
</html>