package com.cocoproxy.client.core

import android.util.Log
import com.cocoproxy.client.data.api.CocoProxyApi
import com.cocoproxy.client.data.api.LoginRequest
import com.cocoproxy.client.data.model.ProxyConfig
import com.cocoproxy.client.data.model.ServerConfig
import com.cocoproxy.client.data.model.TrafficData
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.*
import java.net.Socket
import java.nio.ByteBuffer
import java.security.SecureRandom
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CocoProxyClient @Inject constructor(
    private val api: CocoProxyApi
) {
    
    companion object {
        private const val TAG = "CocoProxyClient"
        private const val PROTOCOL_VERSION = "1.0"
        private const val HEARTBEAT_INTERVAL = 30000L // 30 seconds
        private const val CONNECT_TIMEOUT = 10000 // 10 seconds
        private const val READ_TIMEOUT = 30000 // 30 seconds
        
        // Protocol constants (from server_go/main.go)
        private const val PROTOCOL_HTTP_HTTPS: Byte = 0x01
        private const val PROTOCOL_TCP: Byte = 0x02
        private const val PROTOCOL_SOCKS5: Byte = 0x03
        private const val ENCRYPT_NONE: Byte = 0x00
        private const val ENCRYPT_AES: Byte = 0x01
        private const val ENCRYPT_CHACHA20: Byte = 0x02
    }
    
    // Connection states
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    
    private val _trafficData = MutableStateFlow(TrafficData())
    val trafficData: StateFlow<TrafficData> = _trafficData.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // TCP connection
    private var tcpSocket: Socket? = null
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null
    
    // Configuration
    private var serverConfig: ServerConfig? = null
    private var proxyConfig: ProxyConfig? = null
    
    // Encryption
    private var encryptionKey: ByteArray? = null
    private var cipher: Cipher? = null
    
    // Coroutine scope
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var heartbeatJob: Job? = null
    private var connectionJob: Job? = null
    
    enum class ConnectionState {
        DISCONNECTED,
        CONNECTING,
        CONNECTED,
        RECONNECTING,
        ERROR
    }
    
    /**
     * Connect to CocoProxy server using TCP and CocoProxy protocol
     */
    suspend fun connect(serverConfig: ServerConfig, proxyConfig: ProxyConfig): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                // Clean up any existing connection first
                if (_connectionState.value != ConnectionState.DISCONNECTED) {
                    Log.i(TAG, "Cleaning up existing connection before reconnecting")
                    heartbeatJob?.cancel()
                    connectionJob?.cancel()
                    cleanup()
                }
                
                _connectionState.value = ConnectionState.CONNECTING
                _errorMessage.value = null
                
                <EMAIL> = serverConfig
                <EMAIL> = proxyConfig
                
                Log.i(TAG, "Connecting to CocoProxy server ${serverConfig.host}:${serverConfig.port}")
                
                // Create TCP connection
                tcpSocket = Socket().apply {
                    soTimeout = READ_TIMEOUT
                    connect(java.net.InetSocketAddress(serverConfig.host, serverConfig.port), CONNECT_TIMEOUT)
                }
                
                inputStream = tcpSocket?.getInputStream()
                outputStream = tcpSocket?.getOutputStream()
                
                Log.i(TAG, "TCP connection established")
                
                // Send CocoProxy protocol handshake
                val success = sendCocoProxyProtocol(serverConfig, proxyConfig)
                if (!success) {
                    Log.e(TAG, "CocoProxy protocol handshake failed")
                    _connectionState.value = ConnectionState.ERROR
                    cleanup()
                    return@withContext false
                }
                
                _connectionState.value = ConnectionState.CONNECTED
                Log.i(TAG, "Successfully connected to CocoProxy server")
                
                // Start heartbeat and message handling
                startHeartbeat()
                startMessageHandler()
                
                true
                
            } catch (e: Exception) {
                Log.e(TAG, "Connection error: ${e.message}", e)
                _connectionState.value = ConnectionState.ERROR
                _errorMessage.value = e.message
                cleanup()
                false
            }
        }
    }
    
    /**
     * Send CocoProxy protocol request
     */
    private suspend fun sendCocoProxyProtocol(serverConfig: ServerConfig, proxyConfig: ProxyConfig): Boolean {
        return try {
            Log.d(TAG, "Sending CocoProxy protocol request")
            
            // Get protocol type from configuration
            val protocolType = when (proxyConfig.protocol.lowercase()) {
                "http", "https" -> PROTOCOL_HTTP_HTTPS
                "tcp" -> PROTOCOL_TCP
                "socks5" -> PROTOCOL_SOCKS5
                else -> {
                    Log.w(TAG, "Unknown protocol '${proxyConfig.protocol}', defaulting to SOCKS5")
                    PROTOCOL_SOCKS5
                }
            }
            
            // Prepare target data (for initial connection, we use a placeholder)
            val targetData = "127.0.0.1:${proxyConfig.localPort}".toByteArray(Charsets.UTF_8)
            val encryptType = ENCRYPT_NONE // No encryption for now
            val usernameBytes = serverConfig.username.toByteArray(Charsets.UTF_8)
            val usernameLen = usernameBytes.size
            
            if (usernameLen > 255) {
                Log.e(TAG, "Username too long: $usernameLen")
                return false
            }
            
            // Create packet according to CocoProxy protocol
            // Format: 1 byte protocol type + 1 byte encrypt type + 1 byte username length + 
            //         N bytes username + 4 bytes random padding + 4 bytes data length + data
            val packet = ByteArrayOutputStream()
            packet.write(protocolType.toInt())      // 1 byte protocol type
            packet.write(encryptType.toInt())       // 1 byte encrypt type  
            packet.write(usernameLen)               // 1 byte username length
            packet.write(usernameBytes)             // N bytes username
            packet.write(ByteArray(4))              // 4 bytes random padding
            
            // Write data length (4 bytes, big endian)
            val dataLengthBytes = ByteBuffer.allocate(4).putInt(targetData.size).array()
            packet.write(dataLengthBytes)           // 4 bytes data length
            packet.write(targetData)                // Target data
            
            val packetBytes = packet.toByteArray()
            
            Log.d(TAG, "Sending packet: ${packetBytes.size} bytes")
            Log.d(TAG, "Protocol: $protocolType, Encrypt: $encryptType, User: ${serverConfig.username}, Target: ${String(targetData)}, Data: ${targetData.size} bytes")
            
            // Send packet
            outputStream?.write(packetBytes)
            outputStream?.flush()
            
            // For SOCKS5 protocol, perform additional handshake
            if (protocolType == PROTOCOL_SOCKS5) {
                return performSocks5Handshake()
            }
            
            Log.d(TAG, "CocoProxy protocol setup complete")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send CocoProxy protocol: ${e.message}", e)
            false
        }
    }
    
    /**
     * Perform SOCKS5 handshake after CocoProxy protocol setup
     */
    private suspend fun performSocks5Handshake(): Boolean {
        return try {
            Log.d(TAG, "Starting SOCKS5 handshake")
            
            // Step 1: Send SOCKS5 authentication request
            val authRequest = byteArrayOf(0x05, 0x01, 0x00) // VER=5, NMETHODS=1, METHOD=0 (no auth)
            outputStream?.write(authRequest)
            outputStream?.flush()
            Log.d(TAG, "Sent SOCKS5 auth request")
            
            // Step 2: Receive SOCKS5 authentication response with timeout handling
            val authResponse = ByteArray(2)
            var authBytesRead = 0
            
            // Wait for response with timeout handling
            var attempts = 0
            val maxAttempts = 50 // 5 seconds total (50 * 100ms)
            
            while (authBytesRead < 2 && attempts < maxAttempts) {
                try {
                    val available = inputStream?.available() ?: 0
                    if (available >= 2) {
                        authBytesRead = inputStream?.read(authResponse) ?: 0
                        break
                    } else {
                        delay(100) // Wait 100ms before checking again
                        attempts++
                    }
                } catch (e: java.net.SocketTimeoutException) {
                    Log.v(TAG, "SOCKS5 auth response timeout, retrying...")
                    delay(100)
                    attempts++
                }
            }
            
            if (authBytesRead != 2 || authResponse[0] != 0x05.toByte() || authResponse[1] != 0x00.toByte()) {
                Log.e(TAG, "SOCKS5 auth failed: bytesRead=$authBytesRead, response=${authResponse.joinToString { "%02x".format(it) }}")
                return false
            }
            Log.d(TAG, "SOCKS5 auth successful")
            
            // For initial connection, we don't need to send a specific connect request
            // The server will handle proxy requests as they come
            
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "SOCKS5 handshake error: ${e.message}", e)
            false
        }
    }
    
    /**
     * Start message handler for incoming data
     */
    private fun startMessageHandler() {
        connectionJob = scope.launch {
            try {
                val buffer = ByteArray(4096)
                while (isActive && _connectionState.value == ConnectionState.CONNECTED) {
                    try {
                        // Check if data is available before reading
                        val available = inputStream?.available() ?: 0
                        if (available > 0) {
                            val bytesRead = inputStream?.read(buffer) ?: -1
                            if (bytesRead > 0) {
                                // Handle incoming data (traffic monitoring, etc.)
                                _trafficData.value = _trafficData.value.copy(
                                    bytesDownloaded = _trafficData.value.bytesDownloaded + bytesRead
                                )
                                Log.d(TAG, "Received $bytesRead bytes")
                            } else if (bytesRead == -1) {
                                Log.i(TAG, "Connection closed by server")
                                break
                            }
                        } else {
                            // No data available, sleep briefly to avoid busy waiting
                            delay(100)
                        }
                    } catch (e: java.net.SocketTimeoutException) {
                        // Timeout is expected when no data is available, continue loop
                        Log.v(TAG, "Read timeout - no data available, continuing...")
                        continue
                    } catch (e: Exception) {
                        Log.e(TAG, "Message handler error: ${e.message}", e)
                        _connectionState.value = ConnectionState.ERROR
                        _errorMessage.value = e.message
                        break
                    }
                }
            } catch (e: Exception) {
                if (isActive) {
                    Log.e(TAG, "Message handler error: ${e.message}", e)
                    _connectionState.value = ConnectionState.ERROR
                    _errorMessage.value = e.message
                }
            }
        }
    }
    
    /**
     * Disconnect from server
     */
    fun disconnect() {
        scope.launch {
            try {
                heartbeatJob?.cancel()
                connectionJob?.cancel()
                cleanup()
                _connectionState.value = ConnectionState.DISCONNECTED
                Log.i(TAG, "Disconnected from CocoProxy server")
            } catch (e: Exception) {
                Log.e(TAG, "Disconnect error: ${e.message}", e)
            }
        }
    }
    
    /**
     * Start heartbeat (simplified for TCP connection)
     */
    private fun startHeartbeat() {
        heartbeatJob = scope.launch {
            while (isActive && _connectionState.value == ConnectionState.CONNECTED) {
                try {
                    // For TCP connection, we can send a simple ping or just check connection status
                    delay(HEARTBEAT_INTERVAL)
                    
                    // Check if socket is still connected
                    if (tcpSocket?.isClosed == true || tcpSocket?.isConnected == false) {
                        Log.w(TAG, "TCP connection lost")
                        _connectionState.value = ConnectionState.ERROR
                        break
                    }
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Heartbeat error: ${e.message}", e)
                    break
                }
            }
        }
    }
    
    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            inputStream?.close()
            outputStream?.close()
            tcpSocket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Cleanup error: ${e.message}", e)
        } finally {
            inputStream = null
            outputStream = null
            tcpSocket = null
        }
    }
    
    /**
     * Create a new connection to target through CocoProxy server
     * This creates a separate socket connection for each proxy request
     */
    suspend fun createProxyConnection(targetHost: String, targetPort: Int, initialData: ByteArray? = null): Socket? {
        return withContext(Dispatchers.IO) {
            try {
                val currentServerConfig = serverConfig ?: return@withContext null
                val currentProxyConfig = proxyConfig ?: return@withContext null
                
                Log.d(TAG, "Creating proxy connection to $targetHost:$targetPort")
                
                // Create new TCP connection to server
                val proxySocket = Socket().apply {
                    soTimeout = READ_TIMEOUT
                    connect(java.net.InetSocketAddress(currentServerConfig.host, currentServerConfig.port), CONNECT_TIMEOUT)
                }
                
                val proxyInput = proxySocket.getInputStream()
                val proxyOutput = proxySocket.getOutputStream()
                
                // Send CocoProxy protocol request for this specific target
                val success = sendProxyConnectionRequest(
                    proxyOutput, 
                    currentServerConfig, 
                    currentProxyConfig, 
                    targetHost, 
                    targetPort, 
                    initialData
                )
                
                if (!success) {
                    Log.e(TAG, "Failed to establish proxy connection to $targetHost:$targetPort")
                    proxySocket.close()
                    return@withContext null
                }
                
                // For SOCKS5, perform handshake
                if (currentProxyConfig.protocol.lowercase() == "socks5") {
                    if (!performProxyConnectionSocks5Handshake(proxyInput, proxyOutput, targetHost, targetPort)) {
                        Log.e(TAG, "SOCKS5 handshake failed for $targetHost:$targetPort")
                        proxySocket.close()
                        return@withContext null
                    }
                }
                
                Log.d(TAG, "Proxy connection established to $targetHost:$targetPort")
                proxySocket
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to create proxy connection to $targetHost:$targetPort: ${e.message}", e)
                null
            }
        }
    }
    
    /**
     * Send CocoProxy protocol request for a specific target connection
     */
    private suspend fun sendProxyConnectionRequest(
        outputStream: OutputStream,
        serverConfig: ServerConfig,
        proxyConfig: ProxyConfig,
        targetHost: String,
        targetPort: Int,
        initialData: ByteArray? = null
    ): Boolean {
        return try {
            Log.d(TAG, "Sending proxy connection request for $targetHost:$targetPort")
            
            // Use SOCKS5 protocol type to match VPN service
            val protocolType = PROTOCOL_SOCKS5
            
            // Prepare target data
            val targetData = if (initialData != null) {
                // For HTTP requests, send the actual request data
                initialData
            } else {
                // For SOCKS5, send target address
                "$targetHost:$targetPort".toByteArray(Charsets.UTF_8)
            }
            
            val encryptType = ENCRYPT_NONE // No encryption for now
            val usernameBytes = serverConfig.username.toByteArray(Charsets.UTF_8)
            val usernameLen = usernameBytes.size
            
            if (usernameLen > 255) {
                Log.e(TAG, "Username too long: $usernameLen")
                return false
            }
            
            // Create packet according to CocoProxy protocol
            val packet = ByteArrayOutputStream()
            packet.write(protocolType.toInt())      // 1 byte protocol type
            packet.write(encryptType.toInt())       // 1 byte encrypt type  
            packet.write(usernameLen)               // 1 byte username length
            packet.write(usernameBytes)             // N bytes username
            packet.write(ByteArray(4))              // 4 bytes random padding
            
            // Write data length (4 bytes, big endian)
            val dataLengthBytes = ByteBuffer.allocate(4).putInt(targetData.size).array()
            packet.write(dataLengthBytes)           // 4 bytes data length
            packet.write(targetData)                // Target data
            
            val packetBytes = packet.toByteArray()
            
            Log.d(TAG, "Sending proxy connection packet: ${packetBytes.size} bytes")
            Log.d(TAG, "Protocol: $protocolType, Target: $targetHost:$targetPort, Data: ${targetData.size} bytes")
            
            // Send packet
            outputStream.write(packetBytes)
            outputStream.flush()
            
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send proxy connection request: ${e.message}", e)
            false
        }
    }
    
    /**
     * Perform SOCKS5 handshake for proxy connection
     */
    private suspend fun performProxyConnectionSocks5Handshake(
        inputStream: InputStream,
        outputStream: OutputStream,
        targetHost: String,
        targetPort: Int
    ): Boolean {
        return try {
            Log.d(TAG, "Starting SOCKS5 handshake for $targetHost:$targetPort")
            
            // Step 1: Send SOCKS5 authentication request
            val authRequest = byteArrayOf(0x05, 0x01, 0x00) // VER=5, NMETHODS=1, METHOD=0 (no auth)
            outputStream.write(authRequest)
            outputStream.flush()
            
            // Step 2: Receive SOCKS5 authentication response
            val authResponse = ByteArray(2)
            var authBytesRead = 0
            var attempts = 0
            val maxAttempts = 50
            
            while (authBytesRead < 2 && attempts < maxAttempts) {
                try {
                    val available = inputStream.available()
                    if (available >= 2) {
                        authBytesRead = inputStream.read(authResponse)
                        break
                    } else {
                        delay(100)
                        attempts++
                    }
                } catch (e: java.net.SocketTimeoutException) {
                    delay(100)
                    attempts++
                }
            }
            
            if (authBytesRead != 2 || authResponse[0] != 0x05.toByte() || authResponse[1] != 0x00.toByte()) {
                Log.e(TAG, "SOCKS5 auth failed for $targetHost:$targetPort")
                return false
            }
            
            // Step 3: Send SOCKS5 connect request
            val connectRequest = ByteArrayOutputStream()
            connectRequest.write(0x05) // VER
            connectRequest.write(0x01) // CMD (CONNECT)
            connectRequest.write(0x00) // RSV
            connectRequest.write(0x03) // ATYP (DOMAIN)
            connectRequest.write(targetHost.length) // Domain length
            connectRequest.write(targetHost.toByteArray()) // Domain
            connectRequest.write((targetPort shr 8) and 0xFF) // Port high byte
            connectRequest.write(targetPort and 0xFF) // Port low byte
            
            outputStream.write(connectRequest.toByteArray())
            outputStream.flush()
            
            // Step 4: Receive SOCKS5 connect response
            val connectResponse = ByteArray(10) // Minimum response size
            var connectBytesRead = 0
            attempts = 0
            
            while (connectBytesRead < 4 && attempts < maxAttempts) {
                try {
                    val available = inputStream.available()
                    if (available >= 4) {
                        connectBytesRead = inputStream.read(connectResponse, 0, 4)
                        break
                    } else {
                        delay(100)
                        attempts++
                    }
                } catch (e: java.net.SocketTimeoutException) {
                    delay(100)
                    attempts++
                }
            }
            
            if (connectBytesRead < 4 || connectResponse[0] != 0x05.toByte() || connectResponse[1] != 0x00.toByte()) {
                Log.e(TAG, "SOCKS5 connect failed for $targetHost:$targetPort")
                return false
            }
            
            // Read remaining response data based on address type
            val addressType = connectResponse[3].toInt() and 0xFF
            val remainingBytes = when (addressType) {
                0x01 -> 6 // IPv4: 4 bytes IP + 2 bytes port
                0x03 -> {
                    // Domain: 1 byte length + domain + 2 bytes port
                    val domainLength = inputStream.read()
                    domainLength + 2
                }
                0x04 -> 18 // IPv6: 16 bytes IP + 2 bytes port
                else -> 6 // Default to IPv4
            }
            
            // Read remaining response
            val remaining = ByteArray(remainingBytes)
            inputStream.read(remaining)
            
            Log.d(TAG, "SOCKS5 connect successful for $targetHost:$targetPort")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "SOCKS5 handshake error for $targetHost:$targetPort: ${e.message}", e)
            false
        }
    }
    
    /**
     * Initialize encryption (placeholder for future implementation)
     */
    private fun initializeEncryption(encryptionType: String) {
        try {
            when (encryptionType.lowercase()) {
                "aes" -> {
                    val keyGen = KeyGenerator.getInstance("AES")
                    keyGen.init(256)
                    encryptionKey = keyGen.generateKey().encoded
                    cipher = Cipher.getInstance("AES/CBC/PKCS5Padding")
                }
                // Add other encryption types as needed
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize encryption: ${e.message}", e)
        }
    }
    
    /**
     * Encrypt data (placeholder for future implementation)
     */
    private fun encrypt(data: ByteArray): ByteArray {
        return try {
            encryptionKey?.let { key ->
                cipher?.let { c ->
                    val secretKey = SecretKeySpec(key, "AES")
                    val iv = ByteArray(16)
                    SecureRandom().nextBytes(iv)
                    val ivSpec = IvParameterSpec(iv)
                    
                    c.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec)
                    val encrypted = c.doFinal(data)
                    
                    // Combine IV and encrypted data
                    iv + encrypted
                }
            } ?: data
        } catch (e: Exception) {
            Log.e(TAG, "Encryption failed: ${e.message}", e)
            data
        }
    }
    
    /**
     * Decrypt data (placeholder for future implementation)
     */
    private fun decrypt(encryptedData: ByteArray): ByteArray {
        return try {
            encryptionKey?.let { key ->
                cipher?.let { c ->
                    val iv = encryptedData.sliceArray(0..15)
                    val encrypted = encryptedData.sliceArray(16 until encryptedData.size)
                    
                    val secretKey = SecretKeySpec(key, "AES")
                    val ivSpec = IvParameterSpec(iv)
                    
                    c.init(Cipher.DECRYPT_MODE, secretKey, ivSpec)
                    c.doFinal(encrypted)
                }
            } ?: encryptedData
        } catch (e: Exception) {
            Log.e(TAG, "Decryption failed: ${e.message}", e)
            encryptedData
        }
    }
    
 }