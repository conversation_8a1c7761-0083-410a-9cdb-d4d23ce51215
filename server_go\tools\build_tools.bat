@echo off
echo Building CocoProxy tools...

echo Building check_db...
go build -o check_db.exe check_db.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build check_db
    pause
    exit /b 1
)

echo Building reset_admin...
go build -o reset_admin.exe reset_admin.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build reset_admin
    pause
    exit /b 1
)

echo Building start_admin...
go build -o start_admin.exe start_admin.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build start_admin
    pause
    exit /b 1
)

echo Building admin_only...
go build -o admin_only.exe admin_only.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build admin_only
    pause
    exit /b 1
)

echo Building create_admin_user...
go build -o create_admin_user.exe create_admin_user.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build create_admin_user
    pause
    exit /b 1
)

echo Building fix_traffic_data...
go build -o fix_traffic_data.exe fix_traffic_data.go
if %ERRORLEVEL% NEQ 0 (
    echo Failed to build fix_traffic_data
    pause
    exit /b 1
)

echo All tools built successfully!
echo.
echo Available tools:
echo - check_db.exe         : Check database contents
echo - reset_admin.exe      : Reset admin user
echo - start_admin.exe      : Start simple admin UI server
echo - admin_only.exe       : Start full admin UI server (without proxy)
echo - create_admin_user.exe : Create admin user
echo - fix_traffic_data.exe  : Fix traffic data overflow issues
echo.
echo To create admin user 'test' with password 'test':
echo   create_admin_user.exe test test
echo.
echo To fix traffic data overflow:
echo   fix_traffic_data.exe
pause