package com.cocoproxy.client.core;

/**
 * Manager for handling installed applications
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\"\n\u0002\u0010\b\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u0013\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001e\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\b\b\u0002\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u0018\u0010\u000f\u001a\u0004\u0018\u00010\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0011\u001a\u00020\u0012H\u0002J(\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00150\nJ\"\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u001a\u001a\u00020\u0012J(\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001e0\u001dJ$\u0010\u001f\u001a\u00020\r2\u0006\u0010\u0011\u001a\u00020\u00122\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00120\n2\u0006\u0010!\u001a\u00020\rR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0006\u001a\n \b*\u0004\u0018\u00010\u00070\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/cocoproxy/client/core/AppManager;", "", "context", "Landroid/content/Context;", "<init>", "(Landroid/content/Context;)V", "packageManager", "Landroid/content/pm/PackageManager;", "kotlin.jvm.PlatformType", "getInstalledApps", "", "Lcom/cocoproxy/client/data/model/InstalledApp;", "includeSystemApps", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppIcon", "Landroid/graphics/drawable/Drawable;", "packageName", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "categorizeApp", "Lcom/cocoproxy/client/data/model/AppCategory;", "filterAppsByCategory", "apps", "categories", "searchApps", "query", "getAppsByUids", "uids", "", "", "shouldUseProxy", "selectedApps", "isWhitelist", "app_debug"})
public final class AppManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    private final android.content.pm.PackageManager packageManager = null;
    
    @javax.inject.Inject()
    public AppManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Get all installed applications
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getInstalledApps(boolean includeSystemApps, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.cocoproxy.client.data.model.InstalledApp>> $completion) {
        return null;
    }
    
    /**
     * Get application icon
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAppIcon(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super android.graphics.drawable.Drawable> $completion) {
        return null;
    }
    
    /**
     * Categorize application based on package name
     */
    private final com.cocoproxy.client.data.model.AppCategory categorizeApp(java.lang.String packageName) {
        return null;
    }
    
    /**
     * Filter apps by category
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.InstalledApp> filterAppsByCategory(@org.jetbrains.annotations.NotNull()
    java.util.List<com.cocoproxy.client.data.model.InstalledApp> apps, @org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.cocoproxy.client.data.model.AppCategory> categories) {
        return null;
    }
    
    /**
     * Search apps by name or package name
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.InstalledApp> searchApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.cocoproxy.client.data.model.InstalledApp> apps, @org.jetbrains.annotations.NotNull()
    java.lang.String query) {
        return null;
    }
    
    /**
     * Get apps by UIDs for packet filtering
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.cocoproxy.client.data.model.InstalledApp> getAppsByUids(@org.jetbrains.annotations.NotNull()
    java.util.List<com.cocoproxy.client.data.model.InstalledApp> apps, @org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.Integer> uids) {
        return null;
    }
    
    /**
     * Check if app should use proxy based on split configuration
     */
    public final boolean shouldUseProxy(@org.jetbrains.annotations.NotNull()
    java.lang.String packageName, @org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> selectedApps, boolean isWhitelist) {
        return false;
    }
}