package com.cocoproxy.client.ui.theme

import androidx.compose.ui.graphics.Color

// Primary colors
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Light theme colors
val Purple20 = Color(0xFFBB86FC)
val PurpleGrey20 = Color(0xFF9E9E9E)
val Pink20 = Color(0xFFF8BBD9)

// CocoProxy brand colors
val CocoProxyPrimary = Color(0xFF2196F3)
val CocoProxyPrimaryVariant = Color(0xFF1976D2)
val CocoProxySecondary = Color(0xFF03DAC6)
val CocoProxySecondaryVariant = Color(0xFF018786)

// Status colors
val StatusConnected = Color(0xFF4CAF50)
val StatusConnecting = Color(0xFFFF9800)
val StatusDisconnected = Color(0xFF9E9E9E)
val StatusError = Color(0xFFF44336)
val SuccessColor = Color(0xFF4CAF50)

// Traffic colors
val TrafficUpload = Color(0xFF2196F3)
val TrafficDownload = Color(0xFF4CAF50)

// Background colors
val Light = Color(0xFFFFFBFE)
val Dark = Color(0xFF1C1B1F)
val LightSurface = Color(0xFFF7F2FA)
val DarkSurface = Color(0xFF2B2930)

// Card colors
val CardLight = Color(0xFFFFFFFF)
val CardDark = Color(0xFF2B2930)

// Border colors
val BorderLight = Color(0xFFE0E0E0)
val BorderDark = Color(0xFF424242)

// Text colors
val TextPrimary = Color(0xFF1C1B1F)
val TextSecondary = Color(0xFF49454F)
val TextOnDark = Color(0xFFE6E1E5)
val TextOnDarkSecondary = Color(0xFFCAC4D0)

// Success, Warning, Error colors
val Success = Color(0xFF4CAF50)
val Warning = Color(0xFFFF9800)
val Error = Color(0xFFF44336)
val Info = Color(0xFF2196F3)

// Log level colors
val ErrorColor = Color(0xFFF44336)
val WarningColor = Color(0xFFFF9800)
val InfoColor = Color(0xFF2196F3)
val DebugColor = Color(0xFF9E9E9E)
val VerboseColor = Color(0xFF607D8B)

// Chart colors
val ChartColors = listOf(
    Color(0xFF2196F3),
    Color(0xFF4CAF50),
    Color(0xFFFF9800),
    Color(0xFF9C27B0),
    Color(0xFFE91E63),
    Color(0xFF00BCD4),
    Color(0xFF8BC34A),
    Color(0xFFFFEB3B)
)