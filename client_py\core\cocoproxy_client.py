#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import socket
import threading
import struct
import logging
import time
import json
from PySide6.QtCore import QObject, Signal

class CocoProxyClient(QObject):
    """CocoProxy client with server forwarding"""
    
    # Signals
    proxy_status_changed = Signal(bool, str)  # running, message
    client_connected = Signal(str)  # client address
    client_disconnected = Signal(str)  # client address
    traffic_data = Signal(int)  # bytes transferred
    
    def __init__(self, config):
        """Initialize CocoProxy client"""
        super().__init__()
        self.config = config
        self.logger = logging.getLogger("CocoProxy.CocoProxyClient")
        self.running = False
        self.server_socket = None
        self.server_thread = None
        self.client_threads = []
        
    def start(self):
        """Start the proxy server"""
        if self.running:
            return False, "Proxy server is already running"
        
        try:
            local_port = self.config.get("proxy", "local_port")
            
            # Create server socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(("127.0.0.1", local_port))
            self.server_socket.listen(10)
            
            self.running = True
            
            # Start server thread
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()
            
            message = f"CocoProxy SOCKS5 client started on 127.0.0.1:{local_port}"
            self.logger.info(message)
            self.proxy_status_changed.emit(True, message)
            
            return True, message
            
        except Exception as e:
            self.running = False
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            error_msg = f"Failed to start proxy server: {str(e)}"
            self.logger.error(error_msg)
            self.proxy_status_changed.emit(False, error_msg)
            
            return False, error_msg
    
    def stop(self):
        """Stop the proxy server"""
        if not self.running:
            return True, "Proxy server is not running"
        
        try:
            self.running = False
            
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
            
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=2)
            
            for thread in self.client_threads[:]:
                if thread.is_alive():
                    thread.join(timeout=1)
                self.client_threads.remove(thread)
            
            message = "Proxy server stopped"
            self.logger.info(message)
            self.proxy_status_changed.emit(False, message)
            
            return True, message
            
        except Exception as e:
            error_msg = f"Error stopping proxy server: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def _server_loop(self):
        """Main server loop"""
        self.logger.info("CocoProxy client server loop started")
        
        while self.running:
            try:
                if not self.server_socket:
                    break
                
                client_socket, client_addr = self.server_socket.accept()
                client_addr_str = f"{client_addr[0]}:{client_addr[1]}"
                
                self.logger.info(f"Client connected: {client_addr_str}")
                self.client_connected.emit(client_addr_str)
                
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, client_addr_str),
                    daemon=True
                )
                client_thread.start()
                self.client_threads.append(client_thread)
                
            except OSError:
                break
            except Exception as e:
                if self.running:
                    self.logger.error(f"Error in server loop: {e}")
                break
        
        self.logger.info("CocoProxy client server loop ended")
    
    def _handle_client(self, client_socket, client_addr):
        """Handle client connection"""
        try:
            # SOCKS5 authentication
            if not self._socks5_auth(client_socket):
                return
            
            # SOCKS5 request
            target_host, target_port = self._socks5_request(client_socket)
            if not target_host:
                return
            
            # Connect to CocoProxy server
            server_socket = self._connect_to_cocoproxy_server()
            if not server_socket:
                self._socks5_reply(client_socket, 0x01)  # General failure
                return
            
            # Send connection request through CocoProxy protocol
            if not self._send_cocoproxy_request(server_socket, target_host, target_port):
                self._socks5_reply(client_socket, 0x01)  # General failure
                server_socket.close()
                return
            
            # Send success reply to client
            self._socks5_reply(client_socket, 0x00)  # Success
            
            # Forward data
            self._forward_data(client_socket, server_socket, client_addr)
            
        except Exception as e:
            self.logger.error(f"Error handling client {client_addr}: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
            self.client_disconnected.emit(client_addr)
    
    def _socks5_auth(self, client_socket):
        """Handle SOCKS5 authentication"""
        try:
            data = client_socket.recv(2)
            if len(data) != 2:
                return False
            
            version, nmethods = struct.unpack("BB", data)
            if version != 5:
                return False
            
            methods = client_socket.recv(nmethods)
            if len(methods) != nmethods:
                return False
            
            # Reply with no authentication required
            client_socket.send(b"\x05\x00")
            return True
            
        except Exception as e:
            self.logger.error(f"SOCKS5 auth error: {e}")
            return False
    
    def _socks5_request(self, client_socket):
        """Handle SOCKS5 connection request"""
        try:
            data = client_socket.recv(4)
            if len(data) != 4:
                return None, None
            
            version, cmd, rsv, atyp = struct.unpack("BBBB", data)
            
            if version != 5 or cmd != 1:
                return None, None
            
            if atyp == 1:  # IPv4
                addr_data = client_socket.recv(4)
                target_host = socket.inet_ntoa(addr_data)
            elif atyp == 3:  # Domain name
                addr_len = struct.unpack("B", client_socket.recv(1))[0]
                target_host = client_socket.recv(addr_len).decode('utf-8')
            else:
                return None, None
            
            port_data = client_socket.recv(2)
            target_port = struct.unpack("!H", port_data)[0]
            
            return target_host, target_port
            
        except Exception as e:
            self.logger.error(f"SOCKS5 request error: {e}")
            return None, None
    
    def _socks5_reply(self, client_socket, status):
        """Send SOCKS5 reply"""
        try:
            reply = struct.pack("BBBB", 5, status, 0, 1)
            reply += socket.inet_aton("0.0.0.0")
            reply += struct.pack("!H", 0)
            client_socket.send(reply)
        except Exception as e:
            self.logger.error(f"SOCKS5 reply error: {e}")
    
    def _connect_to_cocoproxy_server(self):
        """Connect to CocoProxy server"""
        try:
            server_host = self.config.get("server", "host")
            server_port = self.config.get("server", "port")
            
            self.logger.debug(f"Connecting to CocoProxy server {server_host}:{server_port}")
            
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.settimeout(10)
            server_socket.connect((server_host, server_port))
            
            return server_socket
            
        except Exception as e:
            self.logger.error(f"Failed to connect to CocoProxy server: {e}")
            return None
    
    def _send_cocoproxy_request(self, server_socket, target_host, target_port):
        """Send connection request using CocoProxy protocol"""
        try:
            # Get user credentials
            username = self.config.get("user", "username")
            
            if not username:
                self.logger.error("No username configured")
                return False
            
            # For now, implement a simplified protocol
            # In a real implementation, this would follow the exact CocoProxy protocol
            # with proper encryption and authentication
            
            # Create a simple request format
            request_data = {
                "username": username,
                "target_host": target_host,
                "target_port": target_port,
                "protocol": "tcp"
            }
            
            # Convert to bytes (simplified - real implementation would use binary protocol)
            request_json = json.dumps(request_data).encode('utf-8')
            request_length = len(request_json)
            
            # Send length first, then data
            server_socket.send(struct.pack("!I", request_length))
            server_socket.send(request_json)
            
            # Wait for response
            response_length_data = server_socket.recv(4)
            if len(response_length_data) != 4:
                return False
            
            response_length = struct.unpack("!I", response_length_data)[0]
            response_data = server_socket.recv(response_length)
            
            if len(response_data) != response_length:
                return False
            
            try:
                response = json.loads(response_data.decode('utf-8'))
                if response.get("status") == "success":
                    self.logger.debug(f"CocoProxy connection established to {target_host}:{target_port}")
                    return True
                else:
                    self.logger.error(f"CocoProxy connection failed: {response.get('error', 'Unknown error')}")
                    return False
            except json.JSONDecodeError:
                self.logger.error("Invalid response from CocoProxy server")
                return False
            
        except Exception as e:
            self.logger.error(f"Failed to send CocoProxy request: {e}")
            return False
    
    def _forward_data(self, client_socket, server_socket, client_addr):
        """Forward data between client and server"""
        def forward(source, destination, direction):
            try:
                while True:
                    data = source.recv(4096)
                    if not data:
                        break
                    destination.send(data)
                    self.traffic_data.emit(len(data))
            except Exception as e:
                self.logger.debug(f"Forward {direction} error: {e}")
            finally:
                try:
                    source.close()
                    destination.close()
                except:
                    pass
        
        client_to_server = threading.Thread(
            target=forward,
            args=(client_socket, server_socket, "client->server"),
            daemon=True
        )
        server_to_client = threading.Thread(
            target=forward,
            args=(server_socket, client_socket, "server->client"),
            daemon=True
        )
        
        client_to_server.start()
        server_to_client.start()
        
        client_to_server.join()
        server_to_client.join()
        
        self.logger.debug(f"Data forwarding ended for {client_addr}")
    
    def is_running(self):
        """Check if proxy server is running"""
        return self.running