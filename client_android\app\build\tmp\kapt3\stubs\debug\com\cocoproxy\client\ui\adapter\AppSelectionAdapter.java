package com.cocoproxy.client.ui.adapter;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\u0018\u00002\f\u0012\b\u0012\u00060\u0002R\u00020\u00000\u0001:\u0002 !B=\u0012\u0018\u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\u0014\u0010\u0013\u001a\u00020\u00072\f\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fJ\u0014\u0010\u0015\u001a\u00020\u00072\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011J\u001c\u0010\u0017\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001bH\u0016J\u001c\u0010\u001c\u001a\u00020\u00072\n\u0010\u001d\u001a\u00060\u0002R\u00020\u00002\u0006\u0010\u001e\u001a\u00020\u001bH\u0016J\b\u0010\u001f\u001a\u00020\u001bH\u0016R \u0010\u0003\u001a\u0014\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00070\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00050\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter;", "Landroidx/recyclerview/widget/RecyclerView$Adapter;", "Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter$AppViewHolder;", "onAppSelected", "Lkotlin/Function2;", "Lcom/cocoproxy/client/data/model/InstalledApp;", "", "", "onAppLongClick", "Lkotlin/Function1;", "appManager", "Lcom/cocoproxy/client/core/AppManager;", "<init>", "(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/functions/Function1;Lcom/cocoproxy/client/core/AppManager;)V", "apps", "", "selectedApps", "", "", "updateApps", "newApps", "updateSelectedApps", "newSelectedApps", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "", "onBindViewHolder", "holder", "position", "getItemCount", "AppViewHolder", "AppDiffCallback", "app_debug"})
public final class AppSelectionAdapter extends androidx.recyclerview.widget.RecyclerView.Adapter<com.cocoproxy.client.ui.adapter.AppSelectionAdapter.AppViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function2<com.cocoproxy.client.data.model.InstalledApp, java.lang.Boolean, kotlin.Unit> onAppSelected = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.cocoproxy.client.data.model.InstalledApp, kotlin.Unit> onAppLongClick = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.AppManager appManager = null;
    @org.jetbrains.annotations.NotNull()
    private java.util.List<com.cocoproxy.client.data.model.InstalledApp> apps;
    @org.jetbrains.annotations.NotNull()
    private java.util.Set<java.lang.String> selectedApps;
    
    public AppSelectionAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super com.cocoproxy.client.data.model.InstalledApp, ? super java.lang.Boolean, kotlin.Unit> onAppSelected, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.cocoproxy.client.data.model.InstalledApp, kotlin.Unit> onAppLongClick, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.AppManager appManager) {
        super();
    }
    
    public final void updateApps(@org.jetbrains.annotations.NotNull()
    java.util.List<com.cocoproxy.client.data.model.InstalledApp> newApps) {
    }
    
    public final void updateSelectedApps(@org.jetbrains.annotations.NotNull()
    java.util.Set<java.lang.String> newSelectedApps) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.cocoproxy.client.ui.adapter.AppSelectionAdapter.AppViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.adapter.AppSelectionAdapter.AppViewHolder holder, int position) {
    }
    
    @java.lang.Override()
    public int getItemCount() {
        return 0;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\u00020\u0001B#\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0004\b\u0006\u0010\u0007J\b\u0010\b\u001a\u00020\tH\u0016J\b\u0010\n\u001a\u00020\tH\u0016J\u0018\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\tH\u0016J\u0018\u0010\u000f\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\t2\u0006\u0010\u000e\u001a\u00020\tH\u0016R\u0014\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0010"}, d2 = {"Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter$AppDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$Callback;", "oldList", "", "Lcom/cocoproxy/client/data/model/InstalledApp;", "newList", "<init>", "(Ljava/util/List;Ljava/util/List;)V", "getOldListSize", "", "getNewListSize", "areItemsTheSame", "", "oldItemPosition", "newItemPosition", "areContentsTheSame", "app_debug"})
    static final class AppDiffCallback extends androidx.recyclerview.widget.DiffUtil.Callback {
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.cocoproxy.client.data.model.InstalledApp> oldList = null;
        @org.jetbrains.annotations.NotNull()
        private final java.util.List<com.cocoproxy.client.data.model.InstalledApp> newList = null;
        
        public AppDiffCallback(@org.jetbrains.annotations.NotNull()
        java.util.List<com.cocoproxy.client.data.model.InstalledApp> oldList, @org.jetbrains.annotations.NotNull()
        java.util.List<com.cocoproxy.client.data.model.InstalledApp> newList) {
            super();
        }
        
        @java.lang.Override()
        public int getOldListSize() {
            return 0;
        }
        
        @java.lang.Override()
        public int getNewListSize() {
            return 0;
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0016\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014J\u0010\u0010\u0015\u001a\u00020\u00162\u0006\u0010\u0017\u001a\u00020\u0018H\u0002R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0019"}, d2 = {"Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter$AppViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "<init>", "(Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter;Landroid/view/View;)V", "imageViewIcon", "Landroid/widget/ImageView;", "textViewAppName", "Landroid/widget/TextView;", "textViewPackageName", "textViewCategory", "checkBoxSelected", "Landroid/widget/CheckBox;", "textViewSystemApp", "bind", "", "app", "Lcom/cocoproxy/client/data/model/InstalledApp;", "isSelected", "", "getCategoryDisplayName", "", "category", "Lcom/cocoproxy/client/data/model/AppCategory;", "app_debug"})
    public final class AppViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final android.widget.ImageView imageViewIcon = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewAppName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewPackageName = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewCategory = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.CheckBox checkBoxSelected = null;
        @org.jetbrains.annotations.NotNull()
        private final android.widget.TextView textViewSystemApp = null;
        
        public AppViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.cocoproxy.client.data.model.InstalledApp app, boolean isSelected) {
        }
        
        private final java.lang.String getCategoryDisplayName(com.cocoproxy.client.data.model.AppCategory category) {
            return null;
        }
    }
}