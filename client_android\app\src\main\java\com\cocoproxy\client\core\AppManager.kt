package com.cocoproxy.client.core

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import com.cocoproxy.client.data.model.AppCategory
import com.cocoproxy.client.data.model.InstalledApp
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manager for handling installed applications
 */
@Singleton
class AppManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val packageManager = context.packageManager
    
    init {
        // Test log to ensure AppManager is initialized
        android.util.Log.i("AppManager", "AppManager initialized")
        // Trigger getInstalledApps for testing
        GlobalScope.launch {
            android.util.Log.i("AppManager", "Testing getInstalledApps call")
            getInstalledApps(false)
        }
    }
    
    /**
     * Get all installed applications
     */
    suspend fun getInstalledApps(includeSystemApps: Boolean = false): List<InstalledApp> = withContext(Dispatchers.IO) {
        try {
            // Use GET_META_DATA flag to get more complete app information
            val packages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
            android.util.Log.i("AppManager", "getInstalledApps called with includeSystemApps=$includeSystemApps, found ${packages.size} total packages")
            
            packages.mapNotNull { appInfo ->
                try {
                    // For debugging: check if this is the specific app we're looking for
                    val isTargetApp = appInfo.packageName == "com.cocozq.observer"
                    if (isTargetApp) {
                        android.util.Log.i("AppManager", "Processing target app: ${appInfo.packageName}")
                    }
                    
                    val packageInfo = packageManager.getPackageInfo(appInfo.packageName, 0)
                    
                    // Check if app has a launcher activity (indicates it's a user-facing app)
                    val hasLauncherActivity = try {
                        val launchIntent = packageManager.getLaunchIntentForPackage(appInfo.packageName)
                        launchIntent != null
                    } catch (e: Exception) {
                        false
                    }
                    
                    // More accurate system app detection
                    val isSystemApp = (appInfo.flags and ApplicationInfo.FLAG_SYSTEM) != 0
                    val isUpdatedSystemApp = (appInfo.flags and ApplicationInfo.FLAG_UPDATED_SYSTEM_APP) != 0
                    val isUserApp = !isSystemApp || isUpdatedSystemApp
                    
                    if (isTargetApp) {
                        android.util.Log.i("AppManager", "Target app flags - isSystemApp: $isSystemApp, isUpdatedSystemApp: $isUpdatedSystemApp, isUserApp: $isUserApp, includeSystemApps: $includeSystemApps")
                    }
                    
                    // Skip based on includeSystemApps flag, but allow apps with launcher activities
                    if (!includeSystemApps && !isUserApp && !hasLauncherActivity) {
                        if (isTargetApp) {
                            android.util.Log.i("AppManager", "Target app filtered out by system app check")
                        }
                        return@mapNotNull null
                    }
                    
                    // Only filter out core system packages that users typically don't want to see
                    // Be more selective and allow apps with launcher activities even if they match system patterns
                    if (!includeSystemApps && !hasLauncherActivity && (
                        appInfo.packageName.startsWith("com.android.") ||
                        appInfo.packageName.startsWith("android.") ||
                        appInfo.packageName.startsWith("com.google.android.gms") ||
                        appInfo.packageName.startsWith("com.google.android.gsf") ||
                        appInfo.packageName == "com.android.systemui" ||
                        appInfo.packageName == "com.android.settings"
                    )) {
                        if (isTargetApp) {
                            android.util.Log.i("AppManager", "Target app filtered out by package name blacklist")
                        }
                        return@mapNotNull null
                    }
                    
                    if (isTargetApp) {
                        android.util.Log.i("AppManager", "Target app passed all filters, creating InstalledApp object")
                    }
                    
                    // Get app name safely
                    val appName = try {
                        appInfo.loadLabel(packageManager).toString()
                    } catch (e: Exception) {
                        appInfo.packageName // Fallback to package name
                    }
                    
                    InstalledApp(
                        packageName = appInfo.packageName,
                        appName = appName,
                        versionName = packageInfo.versionName ?: "",
                        versionCode = packageInfo.longVersionCode,
                        isSystemApp = !isUserApp,
                        uid = appInfo.uid,
                        category = categorizeApp(appInfo.packageName),
                        installTime = packageInfo.firstInstallTime,
                        lastUpdateTime = packageInfo.lastUpdateTime
                    )
                } catch (e: Exception) {
                    // Log the error for debugging but don't crash
                    android.util.Log.w("AppManager", "Failed to process app: ${appInfo.packageName}", e)
                    null
                }
            }.sortedBy { it.appName.lowercase() }.also { result ->
                android.util.Log.i("AppManager", "getInstalledApps returning ${result.size} apps")
                val hasTargetApp = result.any { it.packageName == "com.cocozq.observer" }
                android.util.Log.i("AppManager", "Target app com.cocozq.observer in result: $hasTargetApp")
                if (hasTargetApp) {
                    val targetApp = result.find { it.packageName == "com.cocozq.observer" }
                    android.util.Log.i("AppManager", "Target app details: ${targetApp?.appName}, isSystemApp: ${targetApp?.isSystemApp}")
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("AppManager", "Error in getInstalledApps", e)
            emptyList()
        }
    }
    
    /**
     * Get application icon
     */
    suspend fun getAppIcon(packageName: String): Drawable? = withContext(Dispatchers.IO) {
        try {
            packageManager.getApplicationIcon(packageName)
        } catch (e: Exception) {
            ContextCompat.getDrawable(context, android.R.drawable.sym_def_app_icon)
        }
    }
    
    /**
     * Categorize application based on package name
     */
    private fun categorizeApp(packageName: String): AppCategory {
        return when {
            // Social apps
            packageName.contains("facebook", true) ||
            packageName.contains("twitter", true) ||
            packageName.contains("instagram", true) ||
            packageName.contains("whatsapp", true) ||
            packageName.contains("telegram", true) ||
            packageName.contains("wechat", true) ||
            packageName.contains("qq", true) ||
            packageName.contains("discord", true) ||
            packageName.contains("snapchat", true) ||
            packageName.contains("tiktok", true) -> AppCategory.SOCIAL
            
            // Entertainment apps (browsers, media, etc.)
            packageName.contains("chrome", true) ||
            packageName.contains("firefox", true) ||
            packageName.contains("browser", true) ||
            packageName.contains("opera", true) ||
            packageName.contains("edge", true) ||
            packageName.contains("safari", true) ||
            packageName.contains("youtube", true) ||
            packageName.contains("netflix", true) ||
            packageName.contains("spotify", true) ||
            packageName.contains("music", true) ||
            packageName.contains("video", true) ||
            packageName.contains("media", true) ||
            packageName.contains("player", true) -> AppCategory.ENTERTAINMENT
            
            // Games
            packageName.contains("game", true) ||
            packageName.contains("play", true) ||
            packageName.contains("unity", true) -> AppCategory.GAMES
            
            // Shopping apps
            packageName.contains("amazon", true) ||
            packageName.contains("shop", true) ||
            packageName.contains("store", true) ||
            packageName.contains("market", true) ||
            packageName.contains("pay", true) ||
            packageName.contains("wallet", true) -> AppCategory.SHOPPING
            
            // Travel apps
            packageName.contains("travel", true) ||
            packageName.contains("trip", true) ||
            packageName.contains("hotel", true) ||
            packageName.contains("flight", true) ||
            packageName.contains("uber", true) ||
            packageName.contains("taxi", true) -> AppCategory.TRAVEL
            
            // News apps
            packageName.contains("news", true) ||
            packageName.contains("reader", true) ||
            packageName.contains("rss", true) -> AppCategory.NEWS
            
            // Finance apps
            packageName.contains("bank", true) ||
            packageName.contains("finance", true) ||
            packageName.contains("money", true) ||
            packageName.contains("invest", true) -> AppCategory.FINANCE
            
            // Education apps
            packageName.contains("education", true) ||
            packageName.contains("learn", true) ||
            packageName.contains("study", true) ||
            packageName.contains("course", true) -> AppCategory.EDUCATION
            
            // Health apps
            packageName.contains("health", true) ||
            packageName.contains("fitness", true) ||
            packageName.contains("medical", true) ||
            packageName.contains("doctor", true) -> AppCategory.HEALTH
            
            // Productivity apps
            packageName.contains("office", true) ||
            packageName.contains("docs", true) ||
            packageName.contains("drive", true) ||
            packageName.contains("dropbox", true) ||
            packageName.contains("note", true) ||
            packageName.contains("calendar", true) ||
            packageName.contains("mail", true) ||
            packageName.contains("gmail", true) -> AppCategory.PRODUCTIVITY
            
            // System apps
            packageName.startsWith("com.android") ||
            packageName.startsWith("com.google.android") ||
            packageName.startsWith("android") -> AppCategory.SYSTEM
            
            else -> AppCategory.OTHER
        }
    }
    
    /**
     * Filter apps by category
     */
    fun filterAppsByCategory(apps: List<InstalledApp>, categories: List<AppCategory>): List<InstalledApp> {
        if (categories.isEmpty()) return apps
        return apps.filter { it.category in categories }
    }
    
    /**
     * Search apps by name or package name
     */
    fun searchApps(apps: List<InstalledApp>, query: String): List<InstalledApp> {
        if (query.isBlank()) return apps
        val lowerQuery = query.lowercase()
        return apps.filter {
            it.appName.lowercase().contains(lowerQuery) ||
            it.packageName.lowercase().contains(lowerQuery)
        }
    }
    
    /**
     * Get apps by UIDs for packet filtering
     */
    fun getAppsByUids(apps: List<InstalledApp>, uids: Set<Int>): List<InstalledApp> {
        return apps.filter { it.uid in uids }
    }
    
    /**
     * Check if app should use proxy based on split configuration
     */
    fun shouldUseProxy(packageName: String, selectedApps: List<String>, isWhitelist: Boolean): Boolean {
        val isSelected = packageName in selectedApps
        return if (isWhitelist) isSelected else !isSelected
    }
}