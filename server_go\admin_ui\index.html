<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CocoProxy Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            position: relative;
        }

        .login-container {
            max-width: 400px;
            margin: 100px auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            display: none;
        }

        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 16px;
        }

        .nav-tab.active {
            background: #667eea;
            color: white;
        }

        .nav-tab:hover {
            background: #5a6fd8;
            color: white;
        }

        .tab-content {
            display: none;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .table tr:hover {
            background: #f5f5f5;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card h3 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .stat-card .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            background-color: white;
            margin: 15% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 500px;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: black;
        }

        @media (max-width: 768px) {
            .nav-tabs {
                flex-direction: column;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Login moved to login.html -->

    <!-- Main Interface -->
    <div id="mainContent" class="main-content">
        <div class="container">
            <div class="header">
                <h1>CocoProxy Admin Panel</h1>
                <p>Network Proxy Service Management System</p>
                <button class="logout-btn" onclick="logout()">Logout</button>
            </div>

            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('dashboard')">Dashboard</button>
                <button class="nav-tab" onclick="showTab('users')">User Management</button>
                <button class="nav-tab" onclick="showTab('traffic')">Traffic Monitor</button>
                <button class="nav-tab" onclick="showTab('config')">System Config</button>
            </div>

            <div id="alert" class="alert"></div>

            <!-- Dashboard -->
            <div id="dashboard" class="tab-content active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Users</h3>
                        <div class="stat-value" id="totalUsers">-</div>
                    </div>
                    <div class="stat-card">
                        <h3>Active Users</h3>
                        <div class="stat-value" id="activeUsers">-</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Traffic Used</h3>
                        <div class="stat-value" id="totalTraffic">-</div>
                    </div>
                    <div class="stat-card">
                        <h3>Server Uptime</h3>
                        <div class="stat-value" id="uptime">-</div>
                    </div>
                </div>
            </div>

            <!-- User Management -->
            <div id="users" class="tab-content">
                <div style="margin-bottom: 20px;">
                    <button class="btn btn-success" onclick="showAddUserModal()">Add User</button>
                    <button class="btn btn-primary" onclick="loadUsers()">Refresh</button>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Traffic Limit</th>
                            <th>Used Traffic</th>
                            <th>Admin</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTable">
                    </tbody>
                </table>
            </div>

            <!-- Traffic Monitor -->
            <div id="traffic" class="tab-content">
                <button class="btn btn-primary" onclick="loadTraffic()" style="margin-bottom: 20px;">Refresh Data</button>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Traffic Limit</th>
                            <th>Used Traffic</th>
                            <th>Usage Rate</th>
                            <th>Remaining</th>
                        </tr>
                    </thead>
                    <tbody id="trafficTable">
                    </tbody>
                </table>
            </div>

            <!-- System Config -->
            <div id="config" class="tab-content">
                <h3>Server Configuration</h3>
                <div id="configInfo">
                    <p><strong>Server Host:</strong> <span id="serverHost">-</span></p>
                    <p><strong>Server Port:</strong> <span id="serverPort">-</span></p>
                    <p><strong>UDP Port:</strong> <span id="udpPort">-</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeUserModal()">&times;</span>
            <h3 id="modalTitle">Add User</h3>
            <form id="userForm">
                <input type="hidden" id="userId" name="id">
                <div class="form-group">
                    <label for="modalUsername">Username:</label>
                    <input type="text" id="modalUsername" name="username" required>
                </div>
                <div class="form-group">
                    <label for="modalPassword">Password:</label>
                    <input type="password" id="modalPassword" name="password">
                </div>
                <div class="form-group">
                    <label for="modalEncryptKey">Encrypt Key:</label>
                    <input type="text" id="modalEncryptKey" name="encrypt_key" required>
                </div>
                <div class="form-group">
                    <label for="modalTrafficLimit">Traffic Limit (GB):</label>
                    <input type="number" id="modalTrafficLimit" name="traffic_limit" step="0.1" min="0">
                </div>
                <div class="form-group">
                    <label for="modalIsAdmin">Admin Privilege:</label>
                    <select id="modalIsAdmin" name="is_admin">
                        <option value="false">No</option>
                        <option value="true">Yes</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">Save</button>
                <button type="button" class="btn" onclick="closeUserModal()">Cancel</button>
            </form>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken') || '';
        let currentEditingUserId = null;
        
        // Check if user is logged in
        window.addEventListener('load', () => {
            if (!authToken) {
                window.location.href = 'login.html';
                return;
            }
            showMainContent();
            loadDashboard();
        });

        // Login functionality moved to login.html

        // Show main content
        function showMainContent() {
            document.getElementById('mainContent').style.display = 'block';
        }

        // Logout
        function logout() {
            authToken = '';
            localStorage.removeItem('authToken');
            localStorage.removeItem('username');
            window.location.href = 'login.html';
        }

        // Switch tabs
        function showTab(tabName) {
            // Hide all tab contents
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.nav-tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // Load corresponding data based on tab
            switch(tabName) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'users':
                    loadUsers();
                    break;
                case 'traffic':
                    loadTraffic();
                    break;
                case 'config':
                    loadConfig();
                    break;
            }
        }

        // Show alert message
        function showAlert(message, type = 'success') {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 3000);
        }

        // API request function
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authToken,
                },
            };

            const response = await fetch(url, { ...defaultOptions, ...options });
            
            if (response.status === 401) {
                logout();
                throw new Error('Unauthorized, please login again');
            }

            return response;
        }

        // Load dashboard data
        async function loadDashboard() {
            try {
                const response = await apiRequest('/api/status');
                const data = await response.json();

                document.getElementById('totalUsers').textContent = data.total_users;
                document.getElementById('activeUsers').textContent = data.active_users;
                document.getElementById('totalTraffic').textContent = formatBytes(data.total_traffic_used);
                document.getElementById('uptime').textContent = data.server_uptime;
            } catch (error) {
                showAlert('Failed to load dashboard data: ' + error.message, 'error');
            }
        }

        // Load user list
        async function loadUsers() {
            try {
                const response = await apiRequest('/api/users');
                const users = await response.json();

                const tbody = document.getElementById('usersTable');
                tbody.innerHTML = '';

                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${formatBytes(user.traffic_limit)}</td>
                        <td>${formatBytes(user.used_traffic)}</td>
                        <td>${user.is_admin ? 'Yes' : 'No'}</td>
                        <td>
                            <button class="btn btn-warning" onclick="editUser(${user.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteUser(${user.id})">Delete</button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                showAlert('Failed to load user list: ' + error.message, 'error');
            }
        }

        // Load traffic data
        async function loadTraffic() {
            try {
                const response = await apiRequest('/api/traffic');
                const traffic = await response.json();

                const tbody = document.getElementById('trafficTable');
                tbody.innerHTML = '';

                traffic.forEach(item => {
                    const row = document.createElement('tr');
                    const usagePercent = item.usage_percent || 0;
                    
                    row.innerHTML = `
                        <td>${item.username}</td>
                        <td>${formatBytes(item.traffic_limit)}</td>
                        <td>${formatBytes(item.used_traffic)}</td>
                        <td>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${Math.min(usagePercent, 100)}%"></div>
                            </div>
                            ${usagePercent.toFixed(1)}%
                        </td>
                        <td>${formatBytes(Math.max(item.remaining, 0))}</td>
                    `;
                    tbody.appendChild(row);
                });
            } catch (error) {
                showAlert('Failed to load traffic data: ' + error.message, 'error');
            }
        }

        // Load config info
        async function loadConfig() {
            try {
                const response = await apiRequest('/api/config');
                const config = await response.json();

                document.getElementById('serverHost').textContent = config.server_host;
                document.getElementById('serverPort').textContent = config.server_port;
                document.getElementById('udpPort').textContent = config.udp_port;
            } catch (error) {
                showAlert('Failed to load config info: ' + error.message, 'error');
            }
        }

        // Show add user modal
        function showAddUserModal() {
            currentEditingUserId = null;
            document.getElementById('modalTitle').textContent = 'Add User';
            document.getElementById('userForm').reset();
            document.getElementById('userId').value = '';
            document.getElementById('userModal').style.display = 'block';
        }

        // Edit user
        async function editUser(userId) {
            try {
                const response = await apiRequest(`/api/users/${userId}`);
                const user = await response.json();

                currentEditingUserId = userId;
                document.getElementById('modalTitle').textContent = 'Edit User';
                document.getElementById('userId').value = user.id;
                document.getElementById('modalUsername').value = user.username;
                document.getElementById('modalPassword').value = '';
                document.getElementById('modalEncryptKey').value = user.encrypt_key;
                document.getElementById('modalTrafficLimit').value = user.traffic_limit / (1024 * 1024 * 1024);
                document.getElementById('modalIsAdmin').value = user.is_admin.toString();
                document.getElementById('userModal').style.display = 'block';
            } catch (error) {
                showAlert('Failed to load user info: ' + error.message, 'error');
            }
        }

        // Delete user
        async function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                try {
                    const response = await apiRequest(`/api/users/${userId}`, {
                        method: 'DELETE',
                    });

                    if (response.ok) {
                        showAlert('User deleted successfully');
                        loadUsers();
                    } else {
                        const data = await response.json();
                        showAlert('Failed to delete user: ' + (data.error || 'Unknown error'), 'error');
                    }
                } catch (error) {
                    showAlert('Failed to delete user: ' + error.message, 'error');
                }
            }
        }

        // Close user modal
        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
        }

        // User form submit
        document.getElementById('userForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(e.target);
            const userData = {
                username: formData.get('username'),
                encrypt_key: formData.get('encrypt_key'),
                traffic_limit: parseInt(parseFloat(formData.get('traffic_limit')) * 1024 * 1024 * 1024),
                is_admin: formData.get('is_admin') === 'true',
            };

            // Only include password field when password is provided
            const password = formData.get('password');
            if (password) {
                userData.password_hash = password;
            }

            try {
                let response;
                if (currentEditingUserId) {
                    // Edit user
                    response = await apiRequest(`/api/users/${currentEditingUserId}`, {
                        method: 'PUT',
                        body: JSON.stringify(userData),
                    });
                } else {
                    // Add user
                    if (!password) {
                        showAlert('Password is required when adding a user', 'error');
                        return;
                    }
                    response = await apiRequest('/api/users', {
                        method: 'POST',
                        body: JSON.stringify(userData),
                    });
                }

                if (response.ok) {
                    showAlert(currentEditingUserId ? 'User updated successfully' : 'User added successfully');
                    closeUserModal();
                    loadUsers();
                } else {
                    const data = await response.json();
                    showAlert('Operation failed: ' + (data.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                showAlert('Operation failed: ' + error.message, 'error');
            }
        });

        // Format bytes
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Login check is now at the top of the script

        // Close modal when clicking outside
        window.addEventListener('click', (event) => {
            const modal = document.getElementById('userModal');
            if (event.target === modal) {
                closeUserModal();
            }
        });
    </script>
</body>
</html>