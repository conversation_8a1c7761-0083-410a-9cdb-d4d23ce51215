package com.cocoproxy.client.core

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.net.InetSocketAddress
import java.net.Socket
import java.util.concurrent.ConcurrentHashMap

/**
 * Connection pool for managing SOCKS5 proxy connections
 */
class ConnectionPool {
    
    companion object {
        private const val TAG = "ConnectionPool"
        private const val CONNECTION_TIMEOUT = 10000L // 10 seconds
        private const val CONNECTION_IDLE_TIMEOUT = 60000L // 60 seconds
        private const val CLEANUP_INTERVAL = 30000L // 30 seconds
    }
    
    // Connection pool
    private val connections = ConcurrentHashMap<String, Socket>()
    private val connectionLastUsed = ConcurrentHashMap<String, Long>()
    private val connectionTargets = ConcurrentHashMap<String, String>()
    private val connectionIsUdp = ConcurrentHashMap<String, Boolean>()
    
    // Stats
    private val _activeConnections = MutableStateFlow(0)
    val activeConnections: StateFlow<Int> = _activeConnections.asStateFlow()
    
    private val _totalConnectionsCreated = MutableStateFlow(0)
    val totalConnectionsCreated: StateFlow<Int> = _totalConnectionsCreated.asStateFlow()
    
    private val _totalConnectionsClosed = MutableStateFlow(0)
    val totalConnectionsClosed: StateFlow<Int> = _totalConnectionsClosed.asStateFlow()
    
    // Mutex for thread safety
    private val mutex = Mutex()
    
    // Cleanup coroutine
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var cleanupJob: Job? = null
    
    init {
        startCleanupJob()
    }
    
    /**
     * Get a connection from the pool or create a new one
     */
    suspend fun getConnection(
        proxyHost: String,
        proxyPort: Int,
        targetHost: String,
        targetPort: Int,
        isUdp: Boolean = false
    ): Socket? = mutex.withLock {
        val key = if (isUdp) "udp:$targetHost:$targetPort" else "$targetHost:$targetPort"
        
        // Check if we already have a connection for this target
        val existingConnection = connections[key]
        if (existingConnection != null) {
            // Update last used time
            connectionLastUsed[key] = System.currentTimeMillis()
            return existingConnection
        }
        
        // Create a new connection
        try {
            val socket = Socket()
            socket.soTimeout = 30000 // 30 seconds timeout
            
            // 记录连接目标信息
            Log.d(TAG, "Creating new connection to $proxyHost:$proxyPort for $targetHost:$targetPort")
            
            socket.connect(InetSocketAddress(proxyHost, proxyPort), 10000) // 10 seconds connect timeout
            
            // Store in pool
            connections[key] = socket
            connectionLastUsed[key] = System.currentTimeMillis()
            connectionTargets[key] = "$targetHost:$targetPort"
            connectionIsUdp[key] = isUdp
            
            // Update stats
            _activeConnections.value = connections.size
            _totalConnectionsCreated.value += 1
            
            return socket
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create connection to $proxyHost:$proxyPort for $targetHost:$targetPort", e)
            return null
        }
    }
    
    /**
     * Return a connection to the pool
     */
    suspend fun returnConnection(
        socket: Socket,
        proxyHost: String,
        proxyPort: Int,
        targetHost: String,
        targetPort: Int,
        isUdp: Boolean = false
    ) = mutex.withLock {
        val key = if (isUdp) "udp:$targetHost:$targetPort" else "$targetHost:$targetPort"
        
        // Update last used time
        connectionLastUsed[key] = System.currentTimeMillis()
        
        // If socket is closed or not connected, remove it
        if (socket.isClosed || !socket.isConnected) {
            removeConnection(key)
        }
    }
    
    /**
     * Remove a connection from the pool
     */
    private suspend fun removeConnection(key: String) = mutex.withLock {
        val socket = connections.remove(key)
        connectionLastUsed.remove(key)
        connectionTargets.remove(key)
        connectionIsUdp.remove(key)
        
        try {
            socket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error closing socket: ${e.message}")
        }
        
        // Update stats
        _activeConnections.value = connections.size
        _totalConnectionsClosed.value += 1
    }
    
    /**
     * Start the cleanup job to remove expired connections
     */
    private fun startCleanupJob() {
        cleanupJob = scope.launch {
            while (isActive) {
                delay(CLEANUP_INTERVAL)
                cleanupExpiredConnections()
            }
        }
    }
    
    /**
     * Cleanup expired connections
     */
    private suspend fun cleanupExpiredConnections() = mutex.withLock {
        val now = System.currentTimeMillis()
        val expiredKeys = mutableListOf<String>()
        
        // Find expired connections
        connectionLastUsed.forEach { (key, lastUsed) ->
            if (now - lastUsed > CONNECTION_IDLE_TIMEOUT) {
                expiredKeys.add(key)
            }
        }
        
        // Remove expired connections
        expiredKeys.forEach { key ->
            Log.d(TAG, "Removing expired connection: $key")
            removeConnection(key)
        }
        
        Log.d(TAG, "Cleaned up ${expiredKeys.size} expired connections. Active: ${connections.size}")
    }
    
    /**
     * Get pool stats
     */
    fun getPoolStats(): Map<String, Any> {
        return mapOf(
            "activeConnections" to connections.size,
            "totalCreated" to _totalConnectionsCreated.value,
            "totalClosed" to _totalConnectionsClosed.value
        )
    }
    
    /**
     * Shutdown the connection pool
     */
    fun shutdown() {
        scope.launch {
            cleanupJob?.cancel()
            
            mutex.withLock {
                // Close all connections
                connections.forEach { (key, socket) ->
                    try {
                        Log.d(TAG, "Closing connection: $key")
                        socket.close()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error closing socket: ${e.message}")
                    }
                }
                
                // Clear all maps
                connections.clear()
                connectionLastUsed.clear()
                connectionTargets.clear()
                connectionIsUdp.clear()
                
                // Update stats
                _activeConnections.value = 0
            }
        }
    }
}