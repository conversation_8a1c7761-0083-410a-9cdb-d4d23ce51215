package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
)

func FixAdminServer() {
	// 确保admin_ui目录存在
	adminUIDir := "./admin_ui"
	if _, err := os.Stat(adminUIDir); os.IsNotExist(err) {
		log.Printf("Creating admin_ui directory...")
		if err := os.MkdirAll(adminUIDir, 0755); err != nil {
			log.Fatalf("Failed to create admin_ui directory: %v", err)
		}
	}

	// 创建一个简单的测试页面
	testPagePath := filepath.Join(adminUIDir, "test.html")
	testPageContent := `<!DOCTYPE html>
<html>
<head>
    <title>Admin Server Test</title>
</head>
<body>
    <h1>Admin Server is Working!</h1>
    <p>If you can see this page, the admin server is running correctly.</p>
    <p>Try accessing the <a href="/api/login">login API</a> to test the API endpoints.</p>
</body>
</html>`

	if err := os.WriteFile(testPagePath, []byte(testPageContent), 0644); err != nil {
		log.Printf("Failed to create test page: %v", err)
	} else {
		log.Printf("Created test page at %s", testPagePath)
	}

	// 启动一个简单的HTTP服务器
	http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Admin server is working! Try accessing <a href='/admin_ui/test.html'>test page</a>")
	})

	http.HandleFunc("/api/login", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		w.Write([]byte(`{"token":"test_token","username":"admin","expires":"2023-12-31T23:59:59Z"}`))
	})

	// 提供静态文件
	fs := http.FileServer(http.Dir("."))
	http.Handle("/admin_ui/", http.StripPrefix("/", fs))

	log.Printf("Starting simple admin server on :8080...")
	if err := http.ListenAndServe(":8080", nil); err != nil {
		log.Fatalf("Failed to start admin server: %v", err)
	}
}