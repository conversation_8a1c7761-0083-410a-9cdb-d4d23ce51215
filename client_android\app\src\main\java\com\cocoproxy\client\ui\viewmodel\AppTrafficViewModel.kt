package com.cocoproxy.client.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.cocoproxy.client.data.model.AppTrafficStats
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AppTrafficViewModel @Inject constructor(
    application: Application,
    private val appManager: com.cocoproxy.client.core.AppManager
) : AndroidViewModel(application) {
    
    private val trafficRepository = com.cocoproxy.client.data.repository.TrafficRepository(application, appManager)
    
    private val _trafficStats = MutableStateFlow<List<AppTrafficStats>>(emptyList())
    val trafficStats: StateFlow<List<AppTrafficStats>> = _trafficStats.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()
    
    // 派生状态
    val totalUpload: StateFlow<Long> = _trafficStats.map { stats ->
        stats.sumOf { it.uploadBytes }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0L)
    
    val totalDownload: StateFlow<Long> = _trafficStats.map { stats ->
        stats.sumOf { it.downloadBytes }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0L)
    
    val totalBytes: StateFlow<Long> = _trafficStats.map { stats ->
        stats.sumOf { it.totalBytes }
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), 0L)
    
    init {
        // 启动时自动加载数据
        loadTrafficStats()
        
        // 定期刷新数据
        viewModelScope.launch {
            trafficRepository.trafficUpdates.collect {
                loadTrafficStats()
            }
        }
    }
    
    /**
     * 加载流量统计数据
     */
    fun loadTrafficStats() {
        viewModelScope.launch {
            _isLoading.value = true
            _errorMessage.value = null
            
            try {
                val stats = trafficRepository.getAppTrafficStats()
                _trafficStats.value = stats
            } catch (e: Exception) {
                _errorMessage.value = "加载流量统计失败: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * 刷新统计数据
     */
    fun refreshStats() {
        loadTrafficStats()
    }
    
    /**
     * 清除统计数据
     */
    fun clearStats() {
        viewModelScope.launch {
            try {
                trafficRepository.clearTrafficStats()
                _trafficStats.value = emptyList()
            } catch (e: Exception) {
                _errorMessage.value = "清除统计数据失败: ${e.message}"
            }
        }
    }
    
    /**
     * 获取指定应用的流量统计
     */
    fun getAppTrafficStats(packageName: String): AppTrafficStats? {
        return _trafficStats.value.find { it.packageName == packageName }
    }
    
    /**
     * 获取代理应用的流量统计
     */
    fun getProxiedAppsStats(): List<AppTrafficStats> {
        return _trafficStats.value.filter { it.isProxied }
    }
    
    /**
     * 获取直连应用的流量统计
     */
    fun getDirectAppsStats(): List<AppTrafficStats> {
        return _trafficStats.value.filter { !it.isProxied }
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }
}