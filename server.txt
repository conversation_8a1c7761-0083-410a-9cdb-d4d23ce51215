2025/07/10 01:22:08 CocoProxy SOCKS5: Received 19 bytes: 050100030c3136322e3135392e36312e3301bb
2025/07/10 01:22:08 CocoProxy SOCKS5: Connecting to 162.159.61.3:443 for user test
2025/07/10 01:22:08 CocoProxy SOCKS5: Successfully connected to 162.159.61.3:443
2025/07/10 01:22:08 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:08 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:09 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:09 CocoProxy SOCKS5: Received 19 bytes: 050100030c3136322e3135392e36312e3301bb
2025/07/10 01:22:09 CocoProxy SOCKS5: Connecting to 162.159.61.3:443 for user test
2025/07/10 01:22:09 CocoProxy SOCKS5: Successfully connected to 162.159.61.3:443
2025/07/10 01:22:09 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:09 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:10 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:10 CocoProxy SOCKS5: Received 19 bytes: 050100030c3136322e3135392e36312e3301bb
2025/07/10 01:22:10 CocoProxy SOCKS5: Connecting to 162.159.61.3:443 for user test
2025/07/10 01:22:10 CocoProxy SOCKS5: Successfully connected to 162.159.61.3:443
2025/07/10 01:22:10 CocoProxy SOCKS5: Error copying from server to client: writeto tcp 207.246.95.148:45892->162.159.61.3:443: readfrom tcp 207.246.95.148:28888->182.91.141.232:27121: splice: connection reset by peer
2025/07/10 01:22:10 CocoProxy SOCKS5: Connection to 162.159.61.3:443 ended
2025/07/10 01:22:10 CocoProxy SOCKS5: Error copying from client to server: writeto tcp 207.246.95.148:28888->182.91.141.232:27121: readfrom tcp 207.246.95.148:45892->162.159.61.3:443: use of closed network connection
2025/07/10 01:22:10 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:10 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:10 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:10 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:11 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307312e312e312e310035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 1.1.1.1:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 1.1.1.1:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 19 bytes: 050100030c3136322e3135392e36312e3301bb
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 162.159.61.3:443 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 162.159.61.3:443
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:11 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:11 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:11 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:11 CocoProxy SOCKS5: Connection to 1.1.1.1:53 ended
2025/07/10 01:22:11 CocoProxy SOCKS5: Error copying from client to server: writeto tcp 207.246.95.148:28888->182.91.141.232:27058: readfrom tcp 207.246.95.148:43374->1.1.1.1:53: use of closed network connection
2025/07/10 01:22:12 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:12 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:12 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:12 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:12 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:12 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:12 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:12 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:12 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:12 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:12 CocoProxy SOCKS5: Received 14 bytes: 0501000307312e312e312e310035
2025/07/10 01:22:12 CocoProxy SOCKS5: Connecting to 1.1.1.1:53 for user test
2025/07/10 01:22:12 CocoProxy SOCKS5: Successfully connected to 1.1.1.1:53
2025/07/10 01:22:12 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:13 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:13 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:13 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:13 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:13 CocoProxy SOCKS5: Connection to 1.1.1.1:53 ended
2025/07/10 01:22:13 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:13 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:13 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:13 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:13 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:13 CocoProxy SOCKS5: Received 14 bytes: 0501000307382e382e382e380035
2025/07/10 01:22:13 CocoProxy SOCKS5: Connecting to 8.8.8.8:53 for user test
2025/07/10 01:22:13 CocoProxy SOCKS5: Successfully connected to 8.8.8.8:53
2025/07/10 01:22:13 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:13 CocoProxy SOCKS5: Error copying from client to server: writeto tcp 207.246.95.148:28888->182.91.141.232:27133: readfrom tcp 207.246.95.148:39160->8.8.8.8:53: use of closed network connection
2025/07/10 01:22:14 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:14 CocoProxy SOCKS5: Connection to 172.64.41.3:443 ended
2025/07/10 01:22:14 CocoProxy SOCKS5: Error copying from client to server: writeto tcp 207.246.95.148:28888->182.91.141.232:27036: readfrom tcp 207.246.95.148:34430->172.64.41.3:443: use of closed network connection
2025/07/10 01:22:16 CocoProxy SOCKS5: Connection to 8.8.8.8:53 ended
2025/07/10 01:22:16 CocoProxy SOCKS5: Error copying from client to server: writeto tcp 207.246.95.148:28888->182.91.141.232:27158: readfrom tcp 207.246.95.148:39198->8.8.8.8:53: use of closed network connection
2025/07/10 01:22:16 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:16 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:16 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:16 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:16 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:16 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:16 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:16 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:16 CocoProxy SOCKS5: Received auth request 3 bytes: 050100
2025/07/10 01:22:16 CocoProxy SOCKS5: Received 14 bytes: 0501000307312e312e312e310035
2025/07/10 01:22:16 CocoProxy SOCKS5: Connecting to 1.1.1.1:53 for user test
2025/07/10 01:22:16 CocoProxy SOCKS5: Successfully connected to 1.1.1.1:53
2025/07/10 01:22:16 Warning: ChaCha20 key for user test is not 32 bytes. Adjusting or using default.
2025/07/10 01:22:16 CocoProxy SOCKS5: Handling SOCKS5 request for user test
2025/07/10 01:22:16 CocoProxy SOCKS5: Received 14 bytes: 0501000307312e312e312e310035
2025/07/10 01:22:16 CocoProxy SOCKS5: Connecting to 1.1.1.1:53 for user test
2025/07/10 01:22:16 CocoProxy SOCKS5: Successfully connected to 1.1.1.1:53
2025/07/10 01:22:16 CocoProxy SOCKS5: Received 14 bytes: 0501000307312e312e312e310035
2025/07/10 01:22:16 CocoProxy SOCKS5: Connecting to 1.1.1.1:53 for user test
2025/07/10 01:22:16 CocoProxy SOCKS5: Successfully connected to 1.1.1.1:53
2025/07/10 01:22:16 CocoProxy SOCKS5: Received auth request 3 bytes: 050100