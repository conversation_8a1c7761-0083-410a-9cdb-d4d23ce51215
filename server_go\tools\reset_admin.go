package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// 打开数据库
	db, err := sql.Open("sqlite3", "../coco.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}
	defer db.Close()

	// 删除现有的admin用户
	_, err = db.Exec("DELETE FROM users WHERE username = 'admin'")
	if err != nil {
		log.Printf("Warning: Failed to delete existing admin user: %v", err)
	}

	// 创建新的admin用户
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
	if err != nil {
		log.Fatal("Failed to hash password:", err)
	}

	stmt, err := db.Prepare("INSERT INTO users(username, password_hash, encrypt_key, traffic_limit, is_admin) VALUES(?, ?, ?, ?, ?)")
	if err != nil {
		log.Fatal("Failed to prepare statement:", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec("admin", string(hashedPassword), "your-secret-key-16", 0, true)
	if err != nil {
		log.Fatal("Failed to create admin user:", err)
	}

	fmt.Println("Admin user reset successfully!")
	fmt.Println("Username: admin")
	fmt.Println("Password: admin123")

	// 验证创建的用户
	row := db.QueryRow("SELECT id, username, is_admin FROM users WHERE username = 'admin'")
	var id int
	var username string
	var isAdmin bool

	err = row.Scan(&id, &username, &isAdmin)
	if err != nil {
		log.Fatal("Failed to verify admin user:", err)
	}

	fmt.Printf("Verification: ID=%d, Username=%s, IsAdmin=%t\n", id, username, isAdmin)

	// 测试密码验证
	row = db.QueryRow("SELECT password_hash FROM users WHERE username = 'admin'")
	var passwordHash string
	err = row.Scan(&passwordHash)
	if err != nil {
		log.Fatal("Failed to get password hash:", err)
	}

	err = bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte("admin123"))
	if err != nil {
		fmt.Printf("Password verification FAILED: %v\n", err)
	} else {
		fmt.Println("Password verification SUCCESS!")
	}
}