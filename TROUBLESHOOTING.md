# CocoProxy 故障排除指南

## 客户端登录问题

### 问题：登录失败 "Connection error: Server unreachable"

**可能原因：**
1. 服务器未启动
2. 管理端口配置错误
3. 用户不是管理员用户

**解决方案：**

#### 1. 检查服务器是否运行
```bash
# 在服务器目录运行
cd server_go
go run main.go
```

确保看到类似输出：
```
Admin UI server starting on :28080
Admin UI available at: http://localhost:28080
```

#### 2. 检查端口配置
- 服务器管理端口：**28080**
- 客户端配置应该匹配这个端口

在客户端设置中确认：
- Settings → Server → Admin Port = **28080**

#### 3. 创建管理员用户

**快速方法：**
```bash
# 运行根目录的脚本
create_test_admin.bat
```

**手动方法：**
```bash
cd server_go/tools
go build -o create_admin_user.exe create_admin_user.go
create_admin_user.exe test test
```

### 问题：Test Connection 成功但登录失败

这通常意味着：
- 代理端口(28888)可达 ✓
- 管理端口(28080)不可达 ✗

**检查步骤：**

1. **确认服务器完全启动**
   ```bash
   # 应该看到两个端口都在监听
   netstat -an | findstr :28888
   netstat -an | findstr :28080
   ```

2. **检查防火墙设置**
   - Windows防火墙可能阻止28080端口
   - 临时关闭防火墙测试

3. **检查服务器日志**
   - 查看服务器控制台输出
   - 确认没有错误信息

### 问题：用户存在但无法登录

**检查用户权限：**
```bash
cd server_go/tools
go build -o check_db.exe check_db.go
check_db.exe
```

确认用户的 `Is Admin` 列为 `true`。

**修复用户权限：**
```bash
# 将现有用户设为管理员
create_admin_user.exe existing_username password
```

## 网络连接问题

### 问题：客户端无法连接到代理

**检查代理设置：**
1. 本地代理端口：1080 (SOCKS5) 或 8080 (HTTP)
2. 代理服务器：127.0.0.1:1080

**测试代理连接：**
```bash
# 使用curl测试SOCKS5代理
curl --socks5 127.0.0.1:1080 http://httpbin.org/ip

# 使用curl测试HTTP代理  
curl --proxy http://127.0.0.1:8080 http://httpbin.org/ip
```

## 配置文件问题

### 重置客户端配置

**Windows:**
```bash
del "%USERPROFILE%\.cocoproxy\config.json"
```

**Linux/macOS:**
```bash
rm ~/.cocoproxy/config.json
```

### 默认配置值

```json
{
    "server": {
        "host": "127.0.0.1",
        "port": 28888,
        "admin_port": 28080
    },
    "proxy": {
        "local_port": 1080,
        "protocol": "socks5",
        "encryption": "aes"
    }
}
```

## 常见错误信息

### "Invalid credentials"
- 用户名或密码错误
- 用户不是管理员
- 用户不存在

### "Server unreachable"
- 服务器未启动
- 端口配置错误
- 网络连接问题

### "Connection timeout"
- 服务器响应慢
- 网络延迟高
- 防火墙阻止连接

## 调试步骤

### 1. 验证服务器状态
```bash
# 检查服务器进程
tasklist | findstr main.exe

# 检查端口监听
netstat -an | findstr :28888
netstat -an | findstr :28080
```

### 2. 测试API端点
```bash
# 测试登录API
curl -X POST http://127.0.0.1:28080/api/login \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"test"}'
```

### 3. 查看日志
- 客户端日志：`~/.cocoproxy/logs/cocoproxy.log`
- 服务器日志：控制台输出

### 4. 重建工具
```bash
cd server_go/tools
build_tools.bat
```

## 联系支持

如果问题仍然存在，请提供：
1. 错误信息截图
2. 客户端日志文件
3. 服务器控制台输出
4. 操作系统版本
5. 网络配置信息