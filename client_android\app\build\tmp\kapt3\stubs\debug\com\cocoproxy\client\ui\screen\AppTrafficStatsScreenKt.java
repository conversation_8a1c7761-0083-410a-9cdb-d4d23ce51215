package com.cocoproxy.client.ui.screen;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000b\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\u001a \u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u001a \u0010\u0006\u001a\u00020\u00012\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\b2\u0006\u0010\n\u001a\u00020\u000bH\u0003\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000eH\u0003\u001a5\u0010\u000f\u001a\u00020\u00012\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00132\u0017\u0010\u0014\u001a\u0013\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u0015\u00a2\u0006\u0002\b\u0017H\u0003\u001a\u0010\u0010\u0018\u001a\u00020\b2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002\u001a \u0010\u001b\u001a\u0012\u0012\u0004\u0012\u00020\u000e0\u001cj\b\u0012\u0004\u0012\u00020\u000e`\u001d2\u0006\u0010\u0019\u001a\u00020\u001aH\u0002\u001a\u0010\u0010\u001e\u001a\u00020\b2\u0006\u0010\u001f\u001a\u00020 H\u0002\u00a8\u0006!"}, d2 = {"AppTrafficStatsScreen", "", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/cocoproxy/client/ui/viewmodel/AppTrafficViewModel;", "TrafficStatItem", "label", "", "value", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "AppTrafficStatsItem", "stats", "Lcom/cocoproxy/client/data/model/AppTrafficStats;", "LazyRow", "modifier", "Landroidx/compose/ui/Modifier;", "horizontalArrangement", "Landroidx/compose/foundation/layout/Arrangement$Horizontal;", "content", "Lkotlin/Function1;", "Landroidx/compose/foundation/lazy/LazyListScope;", "Lkotlin/ExtensionFunctionType;", "getSortTypeDisplayName", "sortType", "Lcom/cocoproxy/client/ui/screen/TrafficSortType;", "getSortComparator", "Ljava/util/Comparator;", "Lkotlin/Comparator;", "formatBytes", "bytes", "", "app_debug"})
public final class AppTrafficStatsScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AppTrafficStatsScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.ui.viewmodel.AppTrafficViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void TrafficStatItem(java.lang.String label, java.lang.String value, androidx.compose.ui.graphics.vector.ImageVector icon) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AppTrafficStatsItem(com.cocoproxy.client.data.model.AppTrafficStats stats) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void LazyRow(androidx.compose.ui.Modifier modifier, androidx.compose.foundation.layout.Arrangement.Horizontal horizontalArrangement, kotlin.jvm.functions.Function1<? super androidx.compose.foundation.lazy.LazyListScope, kotlin.Unit> content) {
    }
    
    private static final java.lang.String getSortTypeDisplayName(com.cocoproxy.client.ui.screen.TrafficSortType sortType) {
        return null;
    }
    
    private static final java.util.Comparator<com.cocoproxy.client.data.model.AppTrafficStats> getSortComparator(com.cocoproxy.client.ui.screen.TrafficSortType sortType) {
        return null;
    }
    
    private static final java.lang.String formatBytes(long bytes) {
        return null;
    }
}