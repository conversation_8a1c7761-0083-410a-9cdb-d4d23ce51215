package com.cocoproxy.client.ui.viewmodel;

/**
 * Navigation events
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\u0006\u0004\u0005\u0006\u0007\b\tB\t\b\u0004\u00a2\u0006\u0004\b\u0002\u0010\u0003\u0082\u0001\u0006\n\u000b\f\r\u000e\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "", "<init>", "()V", "ToSettings", "ToLogs", "ToAbout", "ToServerConfig", "ToProxyConfig", "ToAppSelection", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToAbout;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToAppSelection;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToLogs;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToProxyConfig;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToServerConfig;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToSettings;", "app_debug"})
public abstract class NavigationEvent {
    
    private NavigationEvent() {
        super();
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToAbout;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "<init>", "()V", "app_debug"})
    public static final class ToAbout extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToAbout INSTANCE = null;
        
        private ToAbout() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToAppSelection;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "<init>", "()V", "app_debug"})
    public static final class ToAppSelection extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToAppSelection INSTANCE = null;
        
        private ToAppSelection() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToLogs;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "<init>", "()V", "app_debug"})
    public static final class ToLogs extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToLogs INSTANCE = null;
        
        private ToLogs() {
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\t\u0010\b\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\t\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToProxyConfig;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "config", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "<init>", "(Lcom/cocoproxy/client/data/model/ProxyConfig;)V", "getConfig", "()Lcom/cocoproxy/client/data/model/ProxyConfig;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class ToProxyConfig extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        private final com.cocoproxy.client.data.model.ProxyConfig config = null;
        
        public ToProxyConfig(@org.jetbrains.annotations.NotNull()
        com.cocoproxy.client.data.model.ProxyConfig config) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.data.model.ProxyConfig getConfig() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.data.model.ProxyConfig component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToProxyConfig copy(@org.jetbrains.annotations.NotNull()
        com.cocoproxy.client.data.model.ProxyConfig config) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\t\u0010\b\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\t\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\rH\u00d6\u0003J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001J\t\u0010\u0010\u001a\u00020\u0011H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToServerConfig;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "config", "Lcom/cocoproxy/client/data/model/ServerConfig;", "<init>", "(Lcom/cocoproxy/client/data/model/ServerConfig;)V", "getConfig", "()Lcom/cocoproxy/client/data/model/ServerConfig;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class ToServerConfig extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        private final com.cocoproxy.client.data.model.ServerConfig config = null;
        
        public ToServerConfig(@org.jetbrains.annotations.NotNull()
        com.cocoproxy.client.data.model.ServerConfig config) {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.data.model.ServerConfig getConfig() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.data.model.ServerConfig component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToServerConfig copy(@org.jetbrains.annotations.NotNull()
        com.cocoproxy.client.data.model.ServerConfig config) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003\u00a8\u0006\u0004"}, d2 = {"Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent$ToSettings;", "Lcom/cocoproxy/client/ui/viewmodel/NavigationEvent;", "<init>", "()V", "app_debug"})
    public static final class ToSettings extends com.cocoproxy.client.ui.viewmodel.NavigationEvent {
        @org.jetbrains.annotations.NotNull()
        public static final com.cocoproxy.client.ui.viewmodel.NavigationEvent.ToSettings INSTANCE = null;
        
        private ToSettings() {
        }
    }
}