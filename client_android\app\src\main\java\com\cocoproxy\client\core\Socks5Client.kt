package com.cocoproxy.client.core

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.InputStream
import java.io.OutputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.nio.ByteBuffer

/**
 * SOCKS5 client implementation for proxy connections
 */
class Socks5Client(
    private val proxyHost: String,
    private val proxyPort: Int,
    private val username: String? = null,
    private val password: String? = null
) {
    companion object {
        private const val SOCKS_VERSION = 0x05
        private const val NO_AUTH = 0x00
        private const val USERNAME_PASSWORD_AUTH = 0x02
        private const val CONNECT_COMMAND = 0x01
        private const val IPV4_ADDRESS_TYPE = 0x01
        private const val DOMAIN_ADDRESS_TYPE = 0x03
        private const val SUCCESS_REPLY = 0x00
        
        private const val CONNECTION_TIMEOUT = 10000 // 10 seconds
        private const val READ_TIMEOUT = 30000 // 30 seconds
    }
    
    /**
     * Connect to target through SOCKS5 proxy
     */
    suspend fun connect(targetHost: String, targetPort: Int): Socket? = withContext(Dispatchers.IO) {
        var socket: Socket? = null
        try {
            // Create socket connection to proxy
            socket = Socket()
            socket.connect(InetSocketAddress(proxyHost, proxyPort), CONNECTION_TIMEOUT)
            socket.soTimeout = READ_TIMEOUT
            
            val input = socket.getInputStream()
            val output = socket.getOutputStream()
            
            // Perform SOCKS5 handshake
            if (!performHandshake(input, output)) {
                socket.close()
                return@withContext null
            }
            
            // Authenticate if required
            if (username != null && password != null) {
                if (!authenticate(input, output)) {
                    socket.close()
                    return@withContext null
                }
            }
            
            // Send connect request
            if (!sendConnectRequest(input, output, targetHost, targetPort)) {
                socket.close()
                return@withContext null
            }
            
            socket
        } catch (e: Exception) {
            socket?.close()
            null
        }
    }
    
    /**
     * Perform SOCKS5 initial handshake
     */
    private suspend fun performHandshake(input: InputStream, output: OutputStream): Boolean = withContext(Dispatchers.IO) {
        try {
            // Send greeting
            val greeting = if (username != null && password != null) {
                byteArrayOf(SOCKS_VERSION.toByte(), 0x02, NO_AUTH.toByte(), USERNAME_PASSWORD_AUTH.toByte())
            } else {
                byteArrayOf(SOCKS_VERSION.toByte(), 0x01, NO_AUTH.toByte())
            }
            
            output.write(greeting)
            output.flush()
            
            // Read response
            val response = ByteArray(2)
            if (input.read(response) != 2) {
                return@withContext false
            }
            
            // Check version and method
            response[0] == SOCKS_VERSION.toByte() && 
            (response[1] == NO_AUTH.toByte() || response[1] == USERNAME_PASSWORD_AUTH.toByte())
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Perform username/password authentication
     */
    private suspend fun authenticate(input: InputStream, output: OutputStream): Boolean = withContext(Dispatchers.IO) {
        try {
            if (username == null || password == null) return@withContext true
            
            // Send authentication request
            val usernameBytes = username.toByteArray()
            val passwordBytes = password.toByteArray()
            
            val authRequest = ByteArray(3 + usernameBytes.size + passwordBytes.size)
            var offset = 0
            
            authRequest[offset++] = 0x01 // Auth version
            authRequest[offset++] = usernameBytes.size.toByte()
            System.arraycopy(usernameBytes, 0, authRequest, offset, usernameBytes.size)
            offset += usernameBytes.size
            authRequest[offset++] = passwordBytes.size.toByte()
            System.arraycopy(passwordBytes, 0, authRequest, offset, passwordBytes.size)
            
            output.write(authRequest)
            output.flush()
            
            // Read response
            val response = ByteArray(2)
            if (input.read(response) != 2) {
                return@withContext false
            }
            
            response[1] == SUCCESS_REPLY.toByte()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Send CONNECT request
     */
    private suspend fun sendConnectRequest(
        input: InputStream, 
        output: OutputStream, 
        targetHost: String, 
        targetPort: Int
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            // Prepare connect request
            val hostBytes = targetHost.toByteArray()
            val request = ByteArray(6 + hostBytes.size)
            var offset = 0
            
            request[offset++] = SOCKS_VERSION.toByte()
            request[offset++] = CONNECT_COMMAND.toByte()
            request[offset++] = 0x00 // Reserved
            request[offset++] = DOMAIN_ADDRESS_TYPE.toByte()
            request[offset++] = hostBytes.size.toByte()
            System.arraycopy(hostBytes, 0, request, offset, hostBytes.size)
            offset += hostBytes.size
            request[offset++] = (targetPort shr 8).toByte()
            request[offset] = (targetPort and 0xFF).toByte()
            
            output.write(request)
            output.flush()
            
            // Read response
            val response = ByteArray(4)
            if (input.read(response) != 4) {
                return@withContext false
            }
            
            // Check response
            if (response[0] != SOCKS_VERSION.toByte() || response[1] != SUCCESS_REPLY.toByte()) {
                return@withContext false
            }
            
            // Read address info (skip it)
            when (response[3]) {
                IPV4_ADDRESS_TYPE.toByte() -> {
                    input.skip(6) // 4 bytes IP + 2 bytes port
                }
                DOMAIN_ADDRESS_TYPE.toByte() -> {
                    val domainLength = input.read()
                    input.skip((domainLength + 2).toLong()) // domain + 2 bytes port
                }
                else -> {
                    input.skip(18) // IPv6 + port
                }
            }
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Forward data between two streams
     */
    suspend fun forwardData(
        sourceInput: InputStream,
        targetOutput: OutputStream,
        onDataTransferred: (Int) -> Unit = {}
    ) = withContext(Dispatchers.IO) {
        try {
            val buffer = ByteArray(8192)
            var bytesRead: Int
            
            while (sourceInput.read(buffer).also { bytesRead = it } != -1) {
                targetOutput.write(buffer, 0, bytesRead)
                targetOutput.flush()
                onDataTransferred(bytesRead)
            }
        } catch (e: Exception) {
            // Connection closed or error occurred
        }
    }
}