package com.cocoproxy.client.viewmodel;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\"\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0012\n\u0002\u0010$\n\u0002\u0010\b\n\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0006\u0010#\u001a\u00020$J\b\u0010%\u001a\u00020$H\u0002J\u000e\u0010&\u001a\u00020$2\u0006\u0010\'\u001a\u00020\u0015J\u0010\u0010(\u001a\u00020$2\b\u0010)\u001a\u0004\u0018\u00010!J\u000e\u0010*\u001a\u00020$2\u0006\u0010+\u001a\u00020\u0019J\u000e\u0010,\u001a\u00020$2\u0006\u0010-\u001a\u00020\u001cJ\u0016\u0010.\u001a\u00020$2\u0006\u0010/\u001a\u00020\r2\u0006\u00100\u001a\u00020\u0019J\u0006\u00101\u001a\u00020$J\u0006\u00102\u001a\u00020$J\b\u00103\u001a\u00020$H\u0002J\u000e\u00104\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0002\u00105J\u0012\u00106\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020807J\u0006\u00109\u001a\u00020\u0007J\u0012\u0010:\u001a\u000e\u0012\u0004\u0012\u00020!\u0012\u0004\u0012\u00020807J\u0014\u0010;\u001a\u00020$2\f\u0010<\u001a\b\u0012\u0004\u0012\u00020\u00150\fJ\f\u0010=\u001a\b\u0012\u0004\u0012\u00020\u00150\fJ\u0006\u0010>\u001a\u00020$R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u001a\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00140\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00190\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u001c0\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0012R\u000e\u0010\u001f\u001a\u00020\u0015X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010 \u001a\u0004\u0018\u00010!X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020\u0019X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/cocoproxy/client/viewmodel/AppSelectionViewModel;", "Landroidx/lifecycle/AndroidViewModel;", "application", "Landroid/app/Application;", "<init>", "(Landroid/app/Application;)V", "appManager", "Lcom/cocoproxy/client/core/AppManager;", "configRepository", "Lcom/cocoproxy/client/data/repository/ConfigRepository;", "_allApps", "Landroidx/lifecycle/MutableLiveData;", "", "Lcom/cocoproxy/client/data/model/InstalledApp;", "_filteredApps", "filteredApps", "Landroidx/lifecycle/LiveData;", "getFilteredApps", "()Landroidx/lifecycle/LiveData;", "_selectedApps", "", "", "selectedApps", "getSelectedApps", "_isLoading", "", "isLoading", "_splitMode", "Lcom/cocoproxy/client/core/SplitMode;", "splitMode", "getSplitMode", "searchQuery", "filterCategory", "Lcom/cocoproxy/client/data/model/AppCategory;", "includeSystemApps", "loadInstalledApps", "", "loadCurrentConfiguration", "setSearchQuery", "query", "setFilterCategory", "category", "setIncludeSystemApps", "include", "setSplitMode", "mode", "toggleAppSelection", "app", "isSelected", "selectAllVisibleApps", "deselectAllApps", "applyFilters", "saveConfiguration", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAppStatistics", "", "", "getAppManager", "getAppsByCategory", "importAppsFromPackageNames", "packageNames", "exportSelectedPackageNames", "resetToDefault", "app_debug"})
public final class AppSelectionViewModel extends androidx.lifecycle.AndroidViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.core.AppManager appManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.repository.ConfigRepository configRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> _allApps = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> _filteredApps = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> filteredApps = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.Set<java.lang.String>> _selectedApps = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> selectedApps = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.cocoproxy.client.core.SplitMode> _splitMode = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.cocoproxy.client.core.SplitMode> splitMode = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String searchQuery = "";
    @org.jetbrains.annotations.Nullable()
    private com.cocoproxy.client.data.model.AppCategory filterCategory;
    private boolean includeSystemApps = false;
    
    public AppSelectionViewModel(@org.jetbrains.annotations.NotNull()
    android.app.Application application) {
        super(null);
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.cocoproxy.client.data.model.InstalledApp>> getFilteredApps() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.Set<java.lang.String>> getSelectedApps() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.cocoproxy.client.core.SplitMode> getSplitMode() {
        return null;
    }
    
    /**
     * Load installed apps
     */
    public final void loadInstalledApps() {
    }
    
    /**
     * Load current configuration
     */
    private final void loadCurrentConfiguration() {
    }
    
    /**
     * Set search query
     */
    public final void setSearchQuery(@org.jetbrains.annotations.NotNull()
    java.lang.String query) {
    }
    
    /**
     * Set filter category
     */
    public final void setFilterCategory(@org.jetbrains.annotations.Nullable()
    com.cocoproxy.client.data.model.AppCategory category) {
    }
    
    /**
     * Set include system apps
     */
    public final void setIncludeSystemApps(boolean include) {
    }
    
    /**
     * Set split mode
     */
    public final void setSplitMode(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.SplitMode mode) {
    }
    
    /**
     * Toggle app selection
     */
    public final void toggleAppSelection(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.InstalledApp app, boolean isSelected) {
    }
    
    /**
     * Select all visible apps
     */
    public final void selectAllVisibleApps() {
    }
    
    /**
     * Deselect all apps
     */
    public final void deselectAllApps() {
    }
    
    /**
     * Apply filters to app list
     */
    private final void applyFilters() {
    }
    
    /**
     * Save configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveConfiguration(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Get app statistics
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Integer> getAppStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.core.AppManager getAppManager() {
        return null;
    }
    
    /**
     * Get apps by category
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<com.cocoproxy.client.data.model.AppCategory, java.lang.Integer> getAppsByCategory() {
        return null;
    }
    
    /**
     * Import apps from package names
     */
    public final void importAppsFromPackageNames(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> packageNames) {
    }
    
    /**
     * Export selected package names
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> exportSelectedPackageNames() {
        return null;
    }
    
    /**
     * Reset to default configuration
     */
    public final void resetToDefault() {
    }
}