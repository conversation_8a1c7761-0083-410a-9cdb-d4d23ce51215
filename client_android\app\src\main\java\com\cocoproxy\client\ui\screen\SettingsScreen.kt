package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.ui.theme.*
import com.cocoproxy.client.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    onNavigateToServerConfig: () -> Unit,
    onNavigateToProxyConfig: () -> Unit,
    onNavigateToAppSelection: () -> Unit,
    viewModel: MainViewModel = hiltViewModel()
) {
    val uiConfig by viewModel.uiConfig.collectAsStateWithLifecycle()
    val vpnConfig by viewModel.vpnConfig.collectAsStateWithLifecycle()
    val userProfile by viewModel.userProfile.collectAsStateWithLifecycle()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "Settings",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Connection Settings
        SettingsSection(
            title = "Connection",
            icon = Icons.Filled.Settings
        ) {
            SettingsItem(
                title = "Server Configuration",
                subtitle = "Configure proxy server settings",
                icon = Icons.Filled.Storage,
                onClick = onNavigateToServerConfig
            )
            
            SettingsItem(
                title = "Proxy Configuration",
                subtitle = "Configure proxy behavior",
                icon = Icons.Filled.Tune,
                onClick = onNavigateToProxyConfig
            )
            
            SettingsItem(
                title = "App Split Tunneling",
                subtitle = "Configure which apps use proxy",
                icon = Icons.Filled.Apps,
                onClick = onNavigateToAppSelection
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // VPN Settings
        SettingsSection(
            title = "VPN",
            icon = Icons.Filled.VpnKey
        ) {
            SettingsToggleItem(
                title = "Enable VPN Mode",
                subtitle = "Use VPN for system-wide proxy",
                icon = Icons.Filled.VpnKey,
                checked = vpnConfig.enabled,
                onCheckedChange = { viewModel.updateVpnConfig(vpnConfig.copy(enabled = it)) }
            )
            
            SettingsToggleItem(
                title = "Auto Connect",
                subtitle = "Automatically connect on app start",
                icon = Icons.Filled.PlayArrow,
                checked = vpnConfig.autoConnect,
                onCheckedChange = { viewModel.updateVpnConfig(vpnConfig.copy(autoConnect = it)) }
            )
            
            SettingsToggleItem(
                title = "Kill Switch",
                subtitle = "Block traffic when VPN disconnects",
                icon = Icons.Filled.Block,
                checked = vpnConfig.killSwitch,
                onCheckedChange = { viewModel.updateVpnConfig(vpnConfig.copy(killSwitch = it)) }
            )
            
            SettingsToggleItem(
                title = "IPv6 Support",
                subtitle = "Enable IPv6 traffic routing",
                icon = Icons.Filled.Language,
                checked = vpnConfig.ipv6Enabled,
                onCheckedChange = { viewModel.updateVpnConfig(vpnConfig.copy(ipv6Enabled = it)) }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // UI Settings
        SettingsSection(
            title = "Appearance",
            icon = Icons.Filled.Palette
        ) {
            SettingsToggleItem(
                title = "Dark Theme",
                subtitle = "Use dark color scheme",
                icon = Icons.Filled.DarkMode,
                checked = uiConfig.darkTheme,
                onCheckedChange = { viewModel.updateUiConfig(uiConfig.copy(darkTheme = it)) }
            )
            
            SettingsToggleItem(
                title = "Dynamic Colors",
                subtitle = "Use system accent colors",
                icon = Icons.Filled.ColorLens,
                checked = uiConfig.dynamicColors,
                onCheckedChange = { viewModel.updateUiConfig(uiConfig.copy(dynamicColors = it)) }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Notifications Settings
        SettingsSection(
            title = "Notifications",
            icon = Icons.Filled.Notifications
        ) {
            SettingsToggleItem(
                title = "Connection Status",
                subtitle = "Show connection status notifications",
                icon = Icons.Filled.NotificationsActive,
                checked = uiConfig.showNotifications,
                onCheckedChange = { viewModel.updateUiConfig(uiConfig.copy(showNotifications = it)) }
            )
            
            SettingsToggleItem(
                title = "Traffic Alerts",
                subtitle = "Alert on high traffic usage",
                icon = Icons.Filled.Warning,
                checked = uiConfig.trafficAlerts,
                onCheckedChange = { viewModel.updateUiConfig(uiConfig.copy(trafficAlerts = it)) }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Advanced Settings
        SettingsSection(
            title = "Advanced",
            icon = Icons.Filled.Engineering
        ) {
            SettingsToggleItem(
                title = "Debug Mode",
                subtitle = "Enable detailed logging",
                icon = Icons.Filled.BugReport,
                checked = uiConfig.debugMode,
                onCheckedChange = { viewModel.updateUiConfig(uiConfig.copy(debugMode = it)) }
            )
            
            SettingsItem(
                title = "Clear Cache",
                subtitle = "Clear application cache and data",
                icon = Icons.Filled.CleaningServices,
                onClick = { /*viewModel.clearCache()*/ }
            )
            
            SettingsItem(
                title = "Export Configuration",
                subtitle = "Export settings to file",
                icon = Icons.Filled.FileDownload,
                onClick = { /*viewModel.exportConfiguration()*/ }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // User Profile
        SettingsSection(
            title = "Profile",
            icon = Icons.Filled.Person
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Filled.Person,
                            contentDescription = null,
                            modifier = Modifier.size(48.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                        
                        Spacer(modifier = Modifier.width(16.dp))
                        
                        Column {
                            Text(
                                text = userProfile?.username?.ifEmpty { "Guest User" } ?: "Not logged in",
                                style = MaterialTheme.typography.titleMedium,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = userProfile?.email?.ifEmpty { "No email configured" } ?: "Please log in to continue",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
    }
}

@Composable
fun SettingsSection(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(vertical = 8.dp)
            ) {
                content()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Icon(
                imageVector = Icons.Filled.ArrowForward,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun SettingsToggleItem(
    title: String,
    subtitle: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}