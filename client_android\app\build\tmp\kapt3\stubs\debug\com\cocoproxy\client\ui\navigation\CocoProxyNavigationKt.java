package com.cocoproxy.client.ui.navigation;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a\u0016\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u0007\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\t"}, d2 = {"bottomNavItems", "", "Lcom/cocoproxy/client/ui/navigation/Screen;", "getBottomNavItems", "()Ljava/util/List;", "CocoProxyNavigation", "", "onVpnPermissionRequest", "Lkotlin/Function0;", "app_debug"})
public final class CocoProxyNavigationKt {
    
    /**
     * Bottom navigation items
     */
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.cocoproxy.client.ui.navigation.Screen> bottomNavItems = null;
    
    /**
     * Bottom navigation items
     */
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.cocoproxy.client.ui.navigation.Screen> getBottomNavItems() {
        return null;
    }
    
    /**
     * Main navigation component
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void CocoProxyNavigation(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onVpnPermissionRequest) {
    }
}