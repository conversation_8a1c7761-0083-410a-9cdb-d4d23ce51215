package com.cocoproxy.client.data.model;

/**
 * Complete application configuration
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00152\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001b"}, d2 = {"Lcom/cocoproxy/client/data/model/AppConfig;", "", "server", "Lcom/cocoproxy/client/data/model/ServerConfig;", "proxy", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "ui", "Lcom/cocoproxy/client/data/model/UiConfig;", "<init>", "(Lcom/cocoproxy/client/data/model/ServerConfig;Lcom/cocoproxy/client/data/model/ProxyConfig;Lcom/cocoproxy/client/data/model/UiConfig;)V", "getServer", "()Lcom/cocoproxy/client/data/model/ServerConfig;", "getProxy", "()Lcom/cocoproxy/client/data/model/ProxyConfig;", "getUi", "()Lcom/cocoproxy/client/data/model/UiConfig;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "", "app_debug"})
@androidx.compose.runtime.Stable()
public final class AppConfig {
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.model.ServerConfig server = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.model.ProxyConfig proxy = null;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.data.model.UiConfig ui = null;
    
    public AppConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig server, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig proxy, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UiConfig ui) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.ServerConfig getServer() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.ProxyConfig getProxy() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.UiConfig getUi() {
        return null;
    }
    
    public AppConfig() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.ServerConfig component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.ProxyConfig component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.UiConfig component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.data.model.AppConfig copy(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig server, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig proxy, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UiConfig ui) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}