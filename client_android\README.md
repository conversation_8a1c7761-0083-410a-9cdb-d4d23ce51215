# CocoProxy Android Client

CocoProxy Android客户端是一个功能强大的代理应用，支持SOCKS5代理和VPN功能，为Android设备提供安全的网络连接。

## 功能特性

### 核心功能
- **安全代理连接**: 支持SOCKS5、HTTP、HTTPS代理协议
- **VPN支持**: 完整的VPN功能，支持全局流量路由
- **流量监控**: 实时监控上传/下载流量和连接状态
- **自动连接**: 支持开机自启和自动重连
- **Kill Switch**: VPN断开时自动阻断网络流量
- **自定义DNS**: 支持自定义DNS服务器配置

### 用户界面
- **现代化设计**: 基于Material Design 3的现代化UI
- **深色主题**: 支持深色/浅色主题切换
- **动态色彩**: 支持Android 12+的动态色彩系统
- **直观导航**: 底部导航栏，便于快速切换功能

### 安全特性
- **端到端加密**: 使用AES-256加密保护数据传输
- **SSL/TLS支持**: 支持SSL/TLS加密连接
- **认证机制**: 支持用户名/密码认证
- **证书验证**: 可选的SSL证书验证

## 技术架构

### 开发技术栈
- **语言**: Kotlin
- **UI框架**: Jetpack Compose
- **架构模式**: MVVM + Repository Pattern
- **依赖注入**: Hilt
- **异步处理**: Kotlin Coroutines + Flow
- **网络库**: OkHttp + WebSocket
- **数据存储**: DataStore (Preferences)
- **加密库**: Bouncy Castle

### 项目结构
```
app/
├── src/main/java/com/cocoproxy/client/
│   ├── core/                    # 核心功能模块
│   │   └── CocoProxyClient.kt   # 主要客户端类
│   ├── data/                    # 数据层
│   │   ├── model/               # 数据模型
│   │   └── repository/          # 数据仓库
│   ├── di/                      # 依赖注入
│   ├── service/                 # 后台服务
│   │   ├── CocoProxyService.kt  # 代理服务
│   │   └── CocoProxyVpnService.kt # VPN服务
│   ├── receiver/                # 广播接收器
│   ├── ui/                      # 用户界面
│   │   ├── screen/              # 各个屏幕
│   │   ├── theme/               # 主题配置
│   │   └── navigation/          # 导航配置
│   ├── MainActivity.kt          # 主Activity
│   └── CocoProxyApplication.kt  # 应用程序类
└── src/main/res/                # 资源文件
    ├── drawable/                # 图标资源
    ├── values/                  # 字符串、颜色等
    └── AndroidManifest.xml      # 应用清单
```

## 安装和构建

### 环境要求
- Android Studio Arctic Fox (2020.3.1) 或更高版本
- Android SDK API Level 24 (Android 7.0) 或更高
- Kotlin 1.8.0 或更高版本
- Gradle 8.0 或更高版本

### 构建步骤
1. 克隆项目到本地
2. 使用Android Studio打开`client_android`目录
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮或使用命令行：
   ```bash
   ./gradlew assembleDebug
   ```

### 发布构建
```bash
./gradlew assembleRelease
```

## 配置说明

### 服务器配置
- **主机地址**: CocoProxy服务器的IP地址或域名
- **端口**: 服务器监听端口
- **用户名/密码**: 服务器认证凭据
- **SSL设置**: 是否启用SSL加密连接
- **连接超时**: 连接超时时间（秒）
- **重试次数**: 连接失败时的重试次数

### 代理配置
- **本地端口**: 本地SOCKS5代理监听端口
- **代理类型**: SOCKS5、HTTP、HTTPS
- **认证设置**: 本地代理的用户名/密码认证
- **DNS配置**: 自定义DNS服务器设置
- **高级选项**: UDP支持、缓冲区大小、最大连接数

### VPN配置
- **自动连接**: 应用启动时自动连接VPN
- **Kill Switch**: VPN断开时阻断所有网络流量
- **IPv6支持**: 是否路由IPv6流量
- **应用分流**: 选择哪些应用使用VPN
- **DNS设置**: VPN连接时使用的DNS服务器

## 使用指南

### 首次设置
1. 打开应用，进入设置页面
2. 配置服务器信息（主机、端口、认证信息）
3. 配置代理设置（本地端口、代理类型等）
4. 如需使用VPN功能，配置VPN相关设置
5. 返回主页面，点击连接按钮

### 日常使用
- **连接/断开**: 在主页面点击连接/断开按钮
- **查看流量**: 在流量页面查看实时流量统计
- **查看日志**: 在日志页面查看连接日志和错误信息
- **修改设置**: 在设置页面调整各项配置

### VPN使用
1. 首次使用需要授予VPN权限
2. 在设置中启用VPN相关功能
3. 配置VPN参数（DNS、路由等）
4. 在主页面选择VPN模式并连接

## 权限说明

应用需要以下权限：
- **网络访问**: 建立代理连接
- **VPN权限**: 创建VPN连接（可选）
- **前台服务**: 保持代理服务运行
- **开机启动**: 支持开机自启功能
- **唤醒锁**: 防止设备休眠时断开连接

## 故障排除

### 常见问题
1. **连接失败**
   - 检查服务器地址和端口是否正确
   - 确认网络连接正常
   - 检查用户名和密码

2. **VPN无法启动**
   - 确认已授予VPN权限
   - 检查是否有其他VPN应用在运行
   - 重启应用或设备

3. **流量统计不准确**
   - 重启代理服务
   - 清除应用缓存
   - 检查系统时间是否正确

### 日志分析
- 在日志页面查看详细的连接和错误信息
- 启用调试模式获取更详细的日志
- 导出日志文件用于问题分析

## 开发说明

### 代码规范
- 遵循Kotlin官方编码规范
- 使用Jetpack Compose进行UI开发
- 采用MVVM架构模式
- 使用Hilt进行依赖注入
- 使用Coroutines处理异步操作

### 测试
- 单元测试：使用JUnit和Mockito
- UI测试：使用Espresso和Compose测试框架
- 集成测试：测试完整的代理连接流程

### 贡献指南
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

- 项目主页：[GitHub Repository]
- 问题报告：[GitHub Issues]
- 文档：[Documentation]
- 邮箱：[Contact Email]