package com.cocoproxy.client.ui.screen

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.core.CocoProxyClient
import com.cocoproxy.client.data.model.ConnectionStatus
import com.cocoproxy.client.ui.theme.*
import com.cocoproxy.client.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToServerConfig: () -> Unit,
    onNavigateToProxyConfig: () -> Unit,
    onVpnPermissionRequest: () -> Unit,
    onNavigateToLogin: () -> Unit = {},
    viewModel: MainViewModel = hiltViewModel()
) {
    val connectionState by viewModel.connectionState.collectAsStateWithLifecycle()
    val proxyStatus by viewModel.proxyStatus.collectAsStateWithLifecycle()
    val trafficData by viewModel.trafficData.collectAsStateWithLifecycle()
    val serverConfig by viewModel.serverConfig.collectAsStateWithLifecycle()
    val proxyConfig by viewModel.proxyConfig.collectAsStateWithLifecycle()
    val isLoading by viewModel.isLoading.collectAsStateWithLifecycle()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // App Title
        Text(
            text = "CocoProxy",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Connection Status Circle
        ConnectionStatusCircle(
            connectionState = connectionState,
            isLoading = isLoading
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Status Text
        Text(
            text = getStatusText(connectionState, proxyStatus.isRunning),
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Medium,
            color = getStatusColor(connectionState, proxyStatus.isRunning)
        )
        
        if (proxyStatus.isRunning && connectionState == CocoProxyClient.ConnectionState.CONNECTED) {
            Text(
                text = "Proxy running on port ${proxyConfig.localPort}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Control Button
        Button(
            onClick = {
                if (proxyStatus.isRunning) {
                    viewModel.stopProxy()
                } else {
                    // Check if login is required
                    if (viewModel.isLoginRequired()) {
                        onNavigateToLogin()
                    } else {
                        viewModel.startProxy()
                    }
                }
            },
            enabled = !isLoading,
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = if (proxyStatus.isRunning) {
                    MaterialTheme.colorScheme.error
                } else {
                    MaterialTheme.colorScheme.primary
                }
            )
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            Icon(
                imageVector = if (proxyStatus.isRunning) Icons.Filled.Stop else Icons.Filled.PlayArrow,
                contentDescription = null
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (proxyStatus.isRunning) "Stop Proxy" else "Start Proxy",
                style = MaterialTheme.typography.labelLarge
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Server Info Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Server Configuration",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    IconButton(onClick = onNavigateToServerConfig) {
                        Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "Edit server config"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Host:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = serverConfig.host,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Port:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = serverConfig.port.toString(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Proxy Info Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Proxy Configuration",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    IconButton(onClick = onNavigateToProxyConfig) {
                        Icon(
                            imageVector = Icons.Filled.Edit,
                            contentDescription = "Edit proxy config"
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Protocol:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = proxyConfig.protocol.uppercase(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Local Port:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = proxyConfig.localPort.toString(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Encryption:",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    Text(
                        text = proxyConfig.encryption.uppercase(),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // Traffic Summary (if connected)
        if (proxyStatus.isRunning && connectionState == CocoProxyClient.ConnectionState.CONNECTED) {
            Spacer(modifier = Modifier.height(16.dp))
            
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Traffic Summary",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Spacer(modifier = Modifier.height(12.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        TrafficItem(
                            label = "Upload",
                            value = trafficData.formatBytes(trafficData.bytesUploaded),
                            color = TrafficUpload
                        )
                        
                        TrafficItem(
                            label = "Download",
                            value = trafficData.formatBytes(trafficData.bytesDownloaded),
                            color = TrafficDownload
                        )
                        
                        TrafficItem(
                            label = "Connections",
                            value = trafficData.activeConnections.toString(),
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ConnectionStatusCircle(
    connectionState: CocoProxyClient.ConnectionState,
    isLoading: Boolean
) {
    val color = getStatusColor(connectionState, false)
    val animatedProgress by animateFloatAsState(
        targetValue = if (isLoading) 1f else 0f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "progress"
    )
    
    Box(
        modifier = Modifier.size(120.dp),
        contentAlignment = Alignment.Center
    ) {
        // Background circle
        Canvas(
            modifier = Modifier
                .size(120.dp)
                .clip(CircleShape)
        ) {
            drawCircle(
                color = color.copy(alpha = 0.2f),
                radius = size.minDimension / 2
            )
            
            if (isLoading) {
                drawArc(
                    color = color,
                    startAngle = animatedProgress * 360f,
                    sweepAngle = 90f,
                    useCenter = false,
                    style = Stroke(width = 8.dp.toPx())
                )
            } else {
                drawCircle(
                    color = color,
                    radius = size.minDimension / 2,
                    style = Stroke(width = 4.dp.toPx())
                )
            }
        }
        
        // Status icon
        Icon(
            imageVector = getStatusIcon(connectionState),
            contentDescription = null,
            modifier = Modifier.size(48.dp),
            tint = color
        )
    }
}

@Composable
fun TrafficItem(
    label: String,
    value: String,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = color
        )
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

fun getStatusText(connectionState: CocoProxyClient.ConnectionState, isRunning: Boolean): String {
    return when {
        !isRunning -> "Disconnected"
        connectionState == CocoProxyClient.ConnectionState.CONNECTING -> "Connecting..."
        connectionState == CocoProxyClient.ConnectionState.CONNECTED -> "Connected"
        connectionState == CocoProxyClient.ConnectionState.RECONNECTING -> "Reconnecting..."
        connectionState == CocoProxyClient.ConnectionState.ERROR -> "Connection Error"
        else -> "Disconnected"
    }
}

@Composable
fun getStatusColor(connectionState: CocoProxyClient.ConnectionState, isRunning: Boolean): Color {
    return when {
        !isRunning -> StatusDisconnected
        connectionState == CocoProxyClient.ConnectionState.CONNECTING -> StatusConnecting
        connectionState == CocoProxyClient.ConnectionState.CONNECTED -> StatusConnected
        connectionState == CocoProxyClient.ConnectionState.RECONNECTING -> StatusConnecting
        connectionState == CocoProxyClient.ConnectionState.ERROR -> StatusError
        else -> StatusDisconnected
    }
}

fun getStatusIcon(connectionState: CocoProxyClient.ConnectionState): ImageVector {
    return when (connectionState) {
        CocoProxyClient.ConnectionState.CONNECTED -> Icons.Filled.CheckCircle
        CocoProxyClient.ConnectionState.CONNECTING,
        CocoProxyClient.ConnectionState.RECONNECTING -> Icons.Filled.Refresh
        CocoProxyClient.ConnectionState.ERROR -> Icons.Filled.Warning
        else -> Icons.Filled.FiberManualRecord
    }
}