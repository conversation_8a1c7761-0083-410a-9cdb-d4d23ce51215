// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext {
        compose_version = '1.5.8'
        hilt_version = '2.48.1'
    }
    dependencies {
        classpath 'com.google.dagger:hilt-android-gradle-plugin:2.56.2'
    }
}

plugins {
    id 'com.android.application' version '8.11.0' apply false
    id 'com.android.library' version '8.11.0' apply false
    id 'org.jetbrains.kotlin.android' version '2.2.0' apply false
    id 'org.jetbrains.kotlin.plugin.compose' version '2.2.0' apply false
}

task clean(type: Delete) {
    delete rootProject.buildDir
}