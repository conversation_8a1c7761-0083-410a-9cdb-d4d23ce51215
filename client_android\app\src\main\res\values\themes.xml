<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.CocoProxy" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/coco_primary</item>
        <item name="colorPrimaryDark">@color/coco_primary_dark</item>
        <item name="colorAccent">@color/coco_accent</item>
        
        <!-- Material Design color attributes -->
        <item name="colorSurface">@color/surface_light</item>
        <item name="colorOnSurface">@color/text_primary_light</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <item name="colorSecondary">@color/coco_secondary</item>
        <item name="colorOnSecondary">@android:color/white</item>
        
        <item name="android:statusBarColor">@color/coco_primary</item>
        <item name="android:navigationBarColor">@color/surface_light</item>
        <item name="android:windowLightStatusBar" tools:targetApi="m">false</item>
    </style>
</resources>