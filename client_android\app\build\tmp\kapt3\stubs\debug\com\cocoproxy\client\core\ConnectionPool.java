package com.cocoproxy.client.core;

/**
 * Connection pool for managing SOCKS5 proxy connections
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0002\n\u0002\b\t\n\u0002\u0010$\n\u0002\b\u0003\u0018\u0000 42\u00020\u0001:\u00014B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J:\u0010 \u001a\u0004\u0018\u00010\u00072\u0006\u0010!\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u00062\u0006\u0010$\u001a\u00020\u000f2\b\b\u0002\u0010%\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010&J@\u0010\'\u001a\u00020(2\u0006\u0010)\u001a\u00020\u00072\u0006\u0010!\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020\u000f2\u0006\u0010#\u001a\u00020\u00062\u0006\u0010$\u001a\u00020\u000f2\b\b\u0002\u0010%\u001a\u00020\fH\u0086@\u00a2\u0006\u0002\u0010*J\u0016\u0010+\u001a\u00020(2\u0006\u0010,\u001a\u00020\u0006H\u0082@\u00a2\u0006\u0002\u0010-J\b\u0010.\u001a\u00020(H\u0002J\u000e\u0010/\u001a\u00020\u000fH\u0082@\u00a2\u0006\u0002\u00100J\u0012\u00101\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u000102J\u0006\u00103\u001a\u00020(R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\t0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\f0\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0013R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u000e\u0010\u001a\u001a\u00020\u001bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u001dX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u001fX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00065"}, d2 = {"Lcom/cocoproxy/client/core/ConnectionPool;", "", "<init>", "()V", "connections", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Ljava/net/Socket;", "connectionLastUsed", "", "connectionTargets", "connectionIsUdp", "", "_activeConnections", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "activeConnections", "Lkotlinx/coroutines/flow/StateFlow;", "getActiveConnections", "()Lkotlinx/coroutines/flow/StateFlow;", "_totalConnectionsCreated", "totalConnectionsCreated", "getTotalConnectionsCreated", "_totalConnectionsClosed", "totalConnectionsClosed", "getTotalConnectionsClosed", "mutex", "Lkotlinx/coroutines/sync/Mutex;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "cleanupJob", "Lkotlinx/coroutines/Job;", "getConnection", "proxyHost", "proxyPort", "targetHost", "targetPort", "isUdp", "(Ljava/lang/String;ILjava/lang/String;IZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "returnConnection", "", "socket", "(Ljava/net/Socket;Ljava/lang/String;ILjava/lang/String;IZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "removeConnection", "key", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startCleanupJob", "cleanupExpiredConnections", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPoolStats", "", "shutdown", "Companion", "app_debug"})
public final class ConnectionPool {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ConnectionPool";
    private static final long CONNECTION_TIMEOUT = 10000L;
    private static final long CONNECTION_IDLE_TIMEOUT = 60000L;
    private static final long CLEANUP_INTERVAL = 30000L;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.net.Socket> connections = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Long> connectionLastUsed = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.String> connectionTargets = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, java.lang.Boolean> connectionIsUdp = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _activeConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> activeConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _totalConnectionsCreated = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> totalConnectionsCreated = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Integer> _totalConnectionsClosed = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> totalConnectionsClosed = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.sync.Mutex mutex = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.CoroutineScope scope = null;
    @org.jetbrains.annotations.Nullable()
    private kotlinx.coroutines.Job cleanupJob;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.core.ConnectionPool.Companion Companion = null;
    
    public ConnectionPool() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getActiveConnections() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getTotalConnectionsCreated() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Integer> getTotalConnectionsClosed() {
        return null;
    }
    
    /**
     * Get a connection from the pool or create a new one
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getConnection(@org.jetbrains.annotations.NotNull()
    java.lang.String proxyHost, int proxyPort, @org.jetbrains.annotations.NotNull()
    java.lang.String targetHost, int targetPort, boolean isUdp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.net.Socket> $completion) {
        return null;
    }
    
    /**
     * Return a connection to the pool
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object returnConnection(@org.jetbrains.annotations.NotNull()
    java.net.Socket socket, @org.jetbrains.annotations.NotNull()
    java.lang.String proxyHost, int proxyPort, @org.jetbrains.annotations.NotNull()
    java.lang.String targetHost, int targetPort, boolean isUdp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Remove a connection from the pool
     */
    private final java.lang.Object removeConnection(java.lang.String key, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Start the cleanup job to remove expired connections
     */
    private final void startCleanupJob() {
    }
    
    /**
     * Cleanup expired connections
     */
    private final java.lang.Object cleanupExpiredConnections(kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Get pool stats
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getPoolStats() {
        return null;
    }
    
    /**
     * Shutdown the connection pool
     */
    public final void shutdown() {
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/cocoproxy/client/core/ConnectionPool$Companion;", "", "<init>", "()V", "TAG", "", "CONNECTION_TIMEOUT", "", "CONNECTION_IDLE_TIMEOUT", "CLEANUP_INTERVAL", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}