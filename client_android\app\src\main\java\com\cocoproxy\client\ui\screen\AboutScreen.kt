package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.BuildConfig
import com.cocoproxy.client.R
import com.cocoproxy.client.ui.theme.*
import com.cocoproxy.client.ui.viewmodel.MainViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AboutScreen(
    viewModel: MainViewModel = hiltViewModel()
) {
    val context = LocalContext.current
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Header
        Text(
            text = "About",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // App Info Card
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // App Icon
                Icon(
                    imageVector = Icons.Filled.Security,
                    contentDescription = "CocoProxy Logo",
                    modifier = Modifier.size(80.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // App Name
                Text(
                    text = "CocoProxy",
                    style = MaterialTheme.typography.headlineLarge,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
                
                // Version
                Text(
                    text = "Version ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})",
                    style = MaterialTheme.typography.bodyLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Description
                Text(
                    text = "Secure and fast proxy client for Android",
                    style = MaterialTheme.typography.bodyMedium,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Features Section
        AboutSection(
            title = "Features",
            icon = Icons.Filled.Star
        ) {
            FeatureItem(
                icon = Icons.Filled.Security,
                title = "Secure Connection",
                description = "End-to-end encryption with SSL/TLS support"
            )
            
            FeatureItem(
                icon = Icons.Filled.Speed,
                title = "High Performance",
                description = "Optimized for speed and low latency"
            )
            
            FeatureItem(
                icon = Icons.Filled.Tune,
                title = "Flexible Configuration",
                description = "Customizable proxy settings and rules"
            )
            
            FeatureItem(
                icon = Icons.Filled.Analytics,
                title = "Traffic Monitoring",
                description = "Real-time traffic statistics and analysis"
            )
            
            FeatureItem(
                icon = Icons.Filled.VpnKey,
                title = "VPN Support",
                description = "Built-in VPN functionality for system-wide proxy"
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Technical Info Section
        AboutSection(
            title = "Technical Information",
            icon = Icons.Filled.Info
        ) {
            InfoItem(
                label = "Build Type",
                value = if (BuildConfig.DEBUG) "Debug" else "Release"
            )
            
            InfoItem(
                label = "Target SDK",
                value = "API ${BuildConfig.VERSION_CODE}"
            )
            
            InfoItem(
                label = "Min SDK",
                value = "API ${BuildConfig.VERSION_CODE}"
            )
            
            InfoItem(
                label = "Package Name",
                value = BuildConfig.APPLICATION_ID
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Links Section
        AboutSection(
            title = "Links",
            icon = Icons.Filled.Link
        ) {
            LinkItem(
                icon = Icons.Filled.Code,
                title = "Source Code",
                subtitle = "View on GitHub",
                onClick = {
                    // Open GitHub repository
                    // TODO: Implement URL opening
                }
            )
            
            LinkItem(
                icon = Icons.Filled.BugReport,
                title = "Report Issues",
                subtitle = "Submit bug reports and feature requests",
                onClick = {
                    // Open issues page
                    // TODO: Implement URL opening
                }
            )
            
            LinkItem(
                icon = Icons.Filled.Help,
                title = "Documentation",
                subtitle = "User guide and API documentation",
                onClick = {
                    // Open documentation
                    // TODO: Implement URL opening
                }
            )
            
            LinkItem(
                icon = Icons.Filled.Email,
                title = "Contact",
                subtitle = "Get in touch with the developers",
                onClick = {
                    // Open email
                    // TODO: Implement email opening
                }
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Legal Section
        AboutSection(
            title = "Legal",
            icon = Icons.Filled.Gavel
        ) {
            LinkItem(
                icon = Icons.Filled.Description,
                title = "Privacy Policy",
                subtitle = "How we handle your data",
                onClick = {
                    // TODO: Implement URL opening
                }
            )
            
            LinkItem(
                icon = Icons.Filled.Assignment,
                title = "Terms of Service",
                subtitle = "Terms and conditions",
                onClick = {
                    // TODO: Implement URL opening
                }
            )
            
            LinkItem(
                icon = Icons.Filled.Copyright,
                title = "Open Source Licenses",
                subtitle = "Third-party libraries and licenses",
                onClick = {
                    // TODO: Implement license display
                }
            )
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Copyright
        Text(
            text = "© 2024 CocoProxy. All rights reserved.",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
fun AboutSection(
    title: String,
    icon: ImageVector,
    content: @Composable ColumnScope.() -> Unit
) {
    Column {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 8.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(20.dp)
            )
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                content()
            }
        }
    }
}

@Composable
fun FeatureItem(
    icon: ImageVector,
    title: String,
    description: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.Top
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.width(16.dp))
        
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
fun InfoItem(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LinkItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.bodyLarge,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Icon(
                imageVector = Icons.Filled.OpenInNew,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(18.dp)
            )
        }
    }
}