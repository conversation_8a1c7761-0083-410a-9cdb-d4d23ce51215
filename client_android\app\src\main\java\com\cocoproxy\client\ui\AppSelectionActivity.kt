package com.cocoproxy.client.ui

import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.cocoproxy.client.R
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.data.model.AppCategory
import com.cocoproxy.client.data.model.InstalledApp
import com.cocoproxy.client.ui.adapter.AppSelectionAdapter
import com.cocoproxy.client.viewmodel.AppSelectionViewModel
import kotlinx.coroutines.launch

class AppSelectionActivity : AppCompatActivity() {
    
    private lateinit var viewModel: AppSelectionViewModel
    private lateinit var adapter: AppSelectionAdapter
    
    // UI components
    private lateinit var spinnerSplitMode: Spinner
    private lateinit var spinnerCategory: Spinner
    private lateinit var editTextSearch: EditText
    private lateinit var switchIncludeSystem: Switch
    private lateinit var recyclerViewApps: RecyclerView
    private lateinit var progressBar: ProgressBar
    private lateinit var textViewStatus: TextView
    private lateinit var buttonSelectAll: Button
    private lateinit var buttonDeselectAll: Button
    private lateinit var buttonSave: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_app_selection)
        
        initViews()
        initViewModel()
        setupRecyclerView()
        setupSpinners()
        setupSearchAndFilters()
        setupButtons()
        
        // Check if this is statistics mode
        val showStatistics = intent.getBooleanExtra("show_statistics", false)
        if (showStatistics) {
            supportActionBar?.title = "应用统计"
            showAppStatistics()
        }
        
        // Load apps
        viewModel.loadInstalledApps()
    }
    
    private fun initViews() {
        spinnerSplitMode = findViewById(R.id.spinner_split_mode)
        spinnerCategory = findViewById(R.id.spinner_category)
        editTextSearch = findViewById(R.id.edit_text_search)
        switchIncludeSystem = findViewById(R.id.switch_include_system)
        recyclerViewApps = findViewById(R.id.recycler_view_apps)
        progressBar = findViewById(R.id.progress_bar)
        textViewStatus = findViewById(R.id.text_view_status)
        buttonSelectAll = findViewById(R.id.button_select_all)
        buttonDeselectAll = findViewById(R.id.button_deselect_all)
        buttonSave = findViewById(R.id.button_save)
        
        supportActionBar?.title = "应用分流设置"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }
    
    private fun initViewModel() {
        viewModel = ViewModelProvider(this)[AppSelectionViewModel::class.java]
        
        // Observe apps list
        viewModel.filteredApps.observe(this) { apps ->
            adapter.updateApps(apps)
            updateStatus(apps.size)
        }
        
        // Observe loading state
        viewModel.isLoading.observe(this) { isLoading ->
            progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            recyclerViewApps.visibility = if (isLoading) View.GONE else View.VISIBLE
        }
        
        // Observe selected apps
        viewModel.selectedApps.observe(this) { selectedApps ->
            adapter.updateSelectedApps(selectedApps)
            updateSelectionButtons(selectedApps.size)
        }
        
        // Observe split mode
        viewModel.splitMode.observe(this) { splitMode ->
            updateSplitModeDescription(splitMode)
        }
    }
    
    private fun setupRecyclerView() {
        adapter = AppSelectionAdapter(
            onAppSelected = { app, isSelected ->
                viewModel.toggleAppSelection(app, isSelected)
            },
            onAppLongClick = { app ->
                showAppDetails(app)
            },
            appManager = viewModel.getAppManager()
        )
        
        recyclerViewApps.layoutManager = LinearLayoutManager(this)
        recyclerViewApps.adapter = adapter
    }
    
    private fun setupSpinners() {
        // Split mode spinner
        val splitModeAdapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            SplitMode.values().map { getSplitModeDisplayName(it) }
        )
        splitModeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerSplitMode.adapter = splitModeAdapter
        
        spinnerSplitMode.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val splitMode = SplitMode.values()[position]
                viewModel.setSplitMode(splitMode)
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
        
        // Category spinner
        val categories = listOf("全部") + AppCategory.values().map { getCategoryDisplayName(it) }
        val categoryAdapter = ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            categories
        )
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        spinnerCategory.adapter = categoryAdapter
        
        spinnerCategory.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val category = if (position == 0) null else AppCategory.values()[position - 1]
                viewModel.setFilterCategory(category)
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }
    }
    
    private fun setupSearchAndFilters() {
        // Search functionality
        editTextSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                viewModel.setSearchQuery(s?.toString() ?: "")
            }
        })
        
        // Include system apps switch
        switchIncludeSystem.setOnCheckedChangeListener { _, isChecked ->
            viewModel.setIncludeSystemApps(isChecked)
        }
    }
    
    private fun setupButtons() {
        buttonSelectAll.setOnClickListener {
            viewModel.selectAllVisibleApps()
        }
        
        buttonDeselectAll.setOnClickListener {
            viewModel.deselectAllApps()
        }
        
        buttonSave.setOnClickListener {
            lifecycleScope.launch {
                val success = viewModel.saveConfiguration()
                if (success) {
                    Toast.makeText(this@AppSelectionActivity, "配置已保存", Toast.LENGTH_SHORT).show()
                    finish()
                } else {
                    Toast.makeText(this@AppSelectionActivity, "保存失败", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }
    
    private fun updateStatus(appCount: Int) {
        textViewStatus.text = "显示 $appCount 个应用"
    }
    
    private fun updateSelectionButtons(selectedCount: Int) {
        buttonSave.text = "保存 ($selectedCount 个已选择)"
        buttonSave.isEnabled = true
    }
    
    private fun updateSplitModeDescription(splitMode: SplitMode) {
        val description = when (splitMode) {
            SplitMode.WHITELIST -> "只有选中的应用会通过代理"
            SplitMode.BLACKLIST -> "选中的应用不会通过代理，其他应用会通过代理"
            SplitMode.DISABLED -> "所有应用都通过代理"
        }
        // You can add a TextView to show this description
    }
    
    private fun showAppDetails(app: InstalledApp) {
        val details = buildString {
            append("应用名称: ${app.appName}\n")
            append("包名: ${app.packageName}\n")
            append("版本: ${app.versionName} (${app.versionCode})\n")
            append("类别: ${getCategoryDisplayName(app.category)}\n")
            append("系统应用: ${if (app.isSystemApp) "是" else "否"}\n")
            append("UID: ${app.uid}")
        }
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("应用详情")
            .setMessage(details)
            .setPositiveButton("确定", null)
            .show()
    }
    
    private fun getSplitModeDisplayName(splitMode: SplitMode): String {
        return when (splitMode) {
            SplitMode.WHITELIST -> "白名单模式"
            SplitMode.BLACKLIST -> "黑名单模式"
            SplitMode.DISABLED -> "禁用分流"
        }
    }
    
    private fun getCategoryDisplayName(category: AppCategory): String {
        return when (category) {
            AppCategory.SOCIAL -> "社交"
            AppCategory.ENTERTAINMENT -> "娱乐"
            AppCategory.PRODUCTIVITY -> "效率"
            AppCategory.GAMES -> "游戏"
            AppCategory.SHOPPING -> "购物"
            AppCategory.TRAVEL -> "旅行"
            AppCategory.NEWS -> "新闻"
            AppCategory.FINANCE -> "金融"
            AppCategory.EDUCATION -> "教育"
            AppCategory.HEALTH -> "健康"
            AppCategory.SYSTEM -> "系统"
            AppCategory.OTHER -> "其他"
        }
    }
    
    private fun showAppStatistics() {
        viewModel.filteredApps.observe(this) { apps ->
            if (apps.isNotEmpty()) {
                val statistics = viewModel.getAppStatistics()
                val categoryStats = viewModel.getAppsByCategory()
                
                val statsMessage = buildString {
                    append("应用统计信息:\n\n")
                    append("总应用数: ${statistics["total"]}\n")
                    append("已选择: ${statistics["selected"]}\n")
                    append("用户应用: ${statistics["user"]}\n")
                    append("系统应用: ${statistics["system"]}\n\n")
                    append("按类别统计:\n")
                    categoryStats.forEach { (category, count) ->
                        if (count > 0) {
                            append("${getCategoryDisplayName(category)}: $count\n")
                        }
                    }
                }
                
                androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("应用统计")
                    .setMessage(statsMessage)
                    .setPositiveButton("确定") { _, _ ->
                        finish()
                    }
                    .setCancelable(false)
                    .show()
            }
        }
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}