<!DOCTYPE html>
<html>
<head>
    <title>Admin UI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 40px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1 {
            color: #4a6ee0;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        button {
            background: #4a6ee0;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #3a5ecc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin UI Test Page</h1>
        <p>This page tests if the admin UI server is working correctly.</p>
        
        <div>
            <h2>Test Static File Serving</h2>
            <p class="success">✓ Static file serving is working! (You're seeing this page)</p>
        </div>
        
        <div>
            <h2>Test API Endpoint</h2>
            <button onclick="testAPI()">Test API</button>
            <div id="apiResult"></div>
        </div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = "Testing API...";
            
            try {
                const response = await fetch('/api/test');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <p class="success">✓ API test successful!</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <p class="error">✗ API test failed: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>