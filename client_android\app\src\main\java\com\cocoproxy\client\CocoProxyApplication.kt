package com.cocoproxy.client

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class CocoProxyApplication : Application() {

    companion object {
        const val PROXY_NOTIFICATION_CHANNEL_ID = "proxy_service_channel"
        const val VPN_NOTIFICATION_CHANNEL_ID = "vpn_service_channel"
        const val GENERAL_NOTIFICATION_CHANNEL_ID = "general_channel"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Proxy Service Channel
            val proxyChannel = NotificationChannel(
                PROXY_NOTIFICATION_CHANNEL_ID,
                "Proxy Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "CocoProxy service status notifications"
                setShowBadge(false)
            }

            // VPN Service Channel
            val vpnChannel = NotificationChannel(
                VPN_NOTIFICATION_CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "VPN proxy service notifications"
                setShowBadge(false)
            }

            // General Channel
            val generalChannel = NotificationChannel(
                GENERAL_NOTIFICATION_CHANNEL_ID,
                "General",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "General app notifications"
            }

            notificationManager.createNotificationChannels(
                listOf(proxyChannel, vpnChannel, generalChannel)
            )
        }
    }
}