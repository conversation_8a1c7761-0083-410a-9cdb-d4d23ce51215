package com.cocoproxy.client.ui.viewmodel

import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.cocoproxy.client.core.CocoProxyClient
import com.cocoproxy.client.data.repository.ConfigRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val configRepository: ConfigRepository,
    private val cocoProxyClient: CocoProxyClient
) : ViewModel() {
    
    companion object {
        private const val TAG = "LoginViewModel"
    }
    
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    
    /**
     * Perform login with username and password
     */
    fun login(username: String, password: String, rememberMe: Boolean) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(
                    isLoading = true,
                    errorMessage = null
                )
                
                Log.i(TAG, "Attempting login for user: $username")
                
                // Get current server config
                val currentServerConfig = configRepository.serverConfig.first()
                
                // Update server config with login credentials
                val loginServerConfig = currentServerConfig.copy(
                    username = username,
                    password = password,
                    rememberMe = rememberMe
                )
                
                // Get current proxy config
                val proxyConfig = configRepository.proxyConfig.first()
                
                // Attempt to connect (this will trigger HTTP login)
                val loginSuccess = cocoProxyClient.connect(loginServerConfig, proxyConfig)
                
                if (loginSuccess) {
                    // Save credentials if remember me is checked
                    if (rememberMe) {
                        configRepository.saveServerConfig(loginServerConfig)
                        Log.i(TAG, "Login credentials saved")
                    }
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isLoginSuccessful = true,
                        errorMessage = null
                    )
                    
                    Log.i(TAG, "Login successful")
                } else {
                    // Get error message from client
                    val errorMsg = cocoProxyClient.errorMessage.value ?: "登录失败"
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        isLoginSuccessful = false,
                        errorMessage = errorMsg
                    )
                    
                    Log.e(TAG, "Login failed: $errorMsg")
                }
                
            } catch (e: Exception) {
                val errorMsg = "登录错误: ${e.message}"
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    isLoginSuccessful = false,
                    errorMessage = errorMsg
                )
                
                Log.e(TAG, errorMsg, e)
            }
        }
    }
    
    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * Reset login state
     */
    fun resetLoginState() {
        _uiState.value = LoginUiState()
    }
}

/**
 * UI state for login screen
 */
data class LoginUiState(
    val isLoading: Boolean = false,
    val isLoginSuccessful: Boolean = false,
    val errorMessage: String? = null
)