package main

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

// AdminSession 管理员会话
type AdminSession struct {
	Token     string    `json:"token"`
	Username  string    `json:"username"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

var adminSessions = make(map[string]*AdminSession)

// generateToken 生成随机token
func generateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// authMiddleware 身份验证中间件
func authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 跳过登录接口
		if c.Request.URL.Path == "/api/login" {
			c.Next()
			return
		}

		token := c.<PERSON>("Authorization")
		if token == "" {
			token = c.Query("token")
		}

		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Missing authorization token"})
			c.Abort()
			return
		}

		session, exists := adminSessions[token]
		if !exists || time.Now().After(session.ExpiresAt) {
			if exists {
				delete(adminSessions, token)
			}
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			c.Abort()
			return
		}

		// 延长会话时间
		session.ExpiresAt = time.Now().Add(2 * time.Hour)
		c.Set("username", session.Username)
		c.Next()
	}
}

func SetupAdminUI() *gin.Engine {
	r := gin.Default()

	// 允许跨域
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		c.Next()
	})

	// 登录接口
	r.POST("/api/login", func(c *gin.Context) {
		var loginData struct {
			Username string `json:"username" binding:"required"`
			Password string `json:"password" binding:"required"`
		}

		if err := c.ShouldBindJSON(&loginData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
			return
		}

		// 验证用户
		user, err := GetUserByUsername(db, loginData.Username)
		if err != nil || user == nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
			return
		}

		// 验证密码
		if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(loginData.Password)); err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
			return
		}

		// 生成会话token
		token, err := generateToken()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
			return
		}

		session := &AdminSession{
			Token:     token,
			Username:  user.Username,
			CreatedAt: time.Now(),
			ExpiresAt: time.Now().Add(2 * time.Hour),
		}

		adminSessions[token] = session

		c.JSON(http.StatusOK, gin.H{
			"token":    token,
			"username": user.Username,
			"expires":  session.ExpiresAt,
			"is_admin": user.IsAdmin,
		})
	})

	// 应用身份验证中间件到API路由
	api := r.Group("/api")
	api.Use(authMiddleware())

	// 获取当前用户信息
	api.GET("/me", func(c *gin.Context) {
		sessionToken := c.GetHeader("Authorization")
		session, exists := adminSessions[sessionToken]
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid session"})
			return
		}

		user, err := GetUserByUsername(db, session.Username)
		if err != nil || user == nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"id":            user.ID,
			"username":      user.Username,
			"traffic_limit": user.TrafficLimit,
			"used_traffic":  user.UsedTraffic,
			"is_admin":      user.IsAdmin,
		})
	})

	// 用户管理路由 (仅管理员)
	api.GET("/users", func(c *gin.Context) {
		users, err := GetAllUsers(db)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		// 不返回密码哈希
		for _, user := range users {
			user.PasswordHash = ""
		}
		c.JSON(http.StatusOK, users)
	})

	api.GET("/users/:id", func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := strconv.Atoi(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		user, err := GetUserByID(db, id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		if user == nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
			return
		}

		user.PasswordHash = "" // 不返回密码哈希
		c.JSON(http.StatusOK, user)
	})

	api.POST("/users", func(c *gin.Context) {
		var user User
		if err := c.ShouldBindJSON(&user); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// 生成密码哈希
		if user.PasswordHash != "" {
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PasswordHash), bcrypt.DefaultCost)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
				return
			}
			user.PasswordHash = string(hashedPassword)
		}

		if err := AddUser(db, &user); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		user.PasswordHash = "" // 不返回密码哈希
		c.JSON(http.StatusCreated, user)
	})

	api.PUT("/users/:id", func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := strconv.Atoi(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		var user User
		if err := c.ShouldBindJSON(&user); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		user.ID = id

		// 获取现有用户信息
		existingUser, err := GetUserByID(db, id)
		if err != nil || existingUser == nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get existing user"})
			return
		}

		// 如果没有提供新密码，保持原密码
		if user.PasswordHash == "" {
			user.PasswordHash = existingUser.PasswordHash
		} else {
			// 如果提供了新密码，生成哈希
			hashedPassword, err := bcrypt.GenerateFromPassword([]byte(user.PasswordHash), bcrypt.DefaultCost)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
				return
			}
			user.PasswordHash = string(hashedPassword)
		}

		if err := UpdateUser(db, &user); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		user.PasswordHash = "" // 不返回密码哈希
		c.JSON(http.StatusOK, user)
	})

	api.DELETE("/users/:id", func(c *gin.Context) {
		idStr := c.Param("id")
		id, err := strconv.Atoi(idStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
			return
		}

		if err := DeleteUser(db, id); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
	})

	// 流量监控路由
	api.GET("/traffic", func(c *gin.Context) {
		users, err := GetAllUsers(db)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		trafficData := make([]gin.H, len(users))
		for i, user := range users {
			usagePercent := float64(0)
			if user.TrafficLimit > 0 {
				usagePercent = float64(user.UsedTraffic) / float64(user.TrafficLimit) * 100
			}

			trafficData[i] = gin.H{
				"id":             user.ID,
				"username":       user.Username,
				"traffic_limit":  user.TrafficLimit,
				"used_traffic":   user.UsedTraffic,
				"usage_percent":  usagePercent,
				"remaining":      user.TrafficLimit - user.UsedTraffic,
			}
		}

		c.JSON(http.StatusOK, trafficData)
	})

	// 系统状态路由
	api.GET("/status", func(c *gin.Context) {
		users, err := GetAllUsers(db)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		totalUsers := len(users)
		activeUsers := 0
		totalTrafficUsed := int64(0)

		for _, user := range users {
			if user.UsedTraffic > 0 {
				activeUsers++
			}
			totalTrafficUsed += user.UsedTraffic
		}

		c.JSON(http.StatusOK, gin.H{
			"total_users":        totalUsers,
			"active_users":       activeUsers,
			"total_traffic_used": totalTrafficUsed,
			"server_uptime":      time.Since(serverStartTime).String(),
			"current_time":       time.Now(),
		})
	})

	// 配置管理路由
	api.GET("/config", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"server_host": SERVER_HOST,
			"server_port": SERVER_PORT,
			"udp_port":    UDP_PORT,
		})
	})

	// 静态文件路由
	exePath, err := os.Executable()
	if err != nil {
		// 如果获取可执行文件路径失败，使用当前工作目录
		wd, _ := os.Getwd()
		exePath = filepath.Join(wd, "server_go")
	}
	exeDir := filepath.Dir(exePath)
	adminUIRoot := filepath.Join(exeDir, "admin_ui")

	// 检查admin_ui目录是否存在，如果不存在则使用相对路径
	if _, err := os.Stat(adminUIRoot); os.IsNotExist(err) {
		adminUIRoot = "./admin_ui"
	}

	r.StaticFS("/ui", http.Dir(adminUIRoot))

	// 根路径重定向到管理界面
	r.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/ui/")
	})

	return r
}