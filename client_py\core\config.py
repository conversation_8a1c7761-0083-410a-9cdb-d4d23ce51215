#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import logging
from pathlib import Path

class Config:
    """Configuration manager for CocoProxy client"""
    
    def __init__(self):
        """Initialize configuration"""
        self.config_dir = os.path.join(os.path.expanduser("~"), ".cocoproxy")
        self.config_file = os.path.join(self.config_dir, "config.json")
        self.logger = logging.getLogger("CocoProxy.Config")
        
        # Default configuration
        self.default_config = {
            "server": {
                "host": "127.0.0.1",
                "port": 28888,
                "admin_port": 28080
            },
            "proxy": {
                "local_port": 1080,
                "protocol": "socks5",  # socks5, http
                "encryption": "aes"    # none, aes, chacha20
            },
            "user": {
                "username": "",
                "password": "",
                "token": "",
                "remember_me": False
            },
            "ui": {
                "theme": "system",  # system, light, dark
                "language": "en",
                "minimize_to_tray": True,
                "start_on_boot": False,
                "check_updates": True
            }
        }
        
        # Current configuration
        self.config = self.default_config.copy()
        
        # Load configuration
        self.load_config()
    
    def load_config(self):
        """Load configuration from file"""
        try:
            # Create config directory if it doesn't exist
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
            
            # Load config file if it exists
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                
                # Update config with loaded values
                self._update_dict(self.config, loaded_config)
                self.logger.info("Configuration loaded from %s", self.config_file)
            else:
                # Create default config file
                self.save_config()
                self.logger.info("Default configuration created at %s", self.config_file)
        except Exception as e:
            self.logger.error("Failed to load configuration: %s", str(e))
    
    def save_config(self):
        """Save configuration to file"""
        try:
            # Create config directory if it doesn't exist
            if not os.path.exists(self.config_dir):
                os.makedirs(self.config_dir)
            
            # Save config to file
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            
            self.logger.info("Configuration saved to %s", self.config_file)
        except Exception as e:
            self.logger.error("Failed to save configuration: %s", str(e))
    
    def get(self, section, key=None):
        """Get configuration value"""
        if key is None:
            return self.config.get(section, {})
        return self.config.get(section, {}).get(key)
    
    def set(self, section, key, value):
        """Set configuration value"""
        if section not in self.config:
            self.config[section] = {}
        
        self.config[section][key] = value
        self.save_config()
    
    def set_batch(self, settings_dict):
        """Set multiple configuration values at once and save only once"""
        for section, section_data in settings_dict.items():
            if section not in self.config:
                self.config[section] = {}
            
            for key, value in section_data.items():
                self.config[section][key] = value
        
        # Save only once after all changes
        self.save_config()
    
    def reset(self):
        """Reset configuration to default"""
        self.config = self.default_config.copy()
        self.save_config()
    
    def _update_dict(self, target, source):
        """Recursively update dictionary"""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._update_dict(target[key], value)
            else:
                target[key] = value