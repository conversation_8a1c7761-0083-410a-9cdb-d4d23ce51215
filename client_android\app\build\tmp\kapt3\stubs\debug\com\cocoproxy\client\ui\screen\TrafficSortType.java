package com.cocoproxy.client.ui.screen;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/cocoproxy/client/ui/screen/TrafficSortType;", "", "<init>", "(Ljava/lang/String;I)V", "TOTAL_DESC", "TOTAL_ASC", "UPLOAD_DESC", "DOWNLOAD_DESC", "NAME_ASC", "app_debug"})
public enum TrafficSortType {
    /*public static final*/ TOTAL_DESC /* = new TOTAL_DESC() */,
    /*public static final*/ TOTAL_ASC /* = new TOTAL_ASC() */,
    /*public static final*/ UPLOAD_DESC /* = new UPLOAD_DESC() */,
    /*public static final*/ DOWNLOAD_DESC /* = new DOWNLOAD_DESC() */,
    /*public static final*/ NAME_ASC /* = new NAME_ASC() */;
    
    TrafficSortType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.cocoproxy.client.ui.screen.TrafficSortType> getEntries() {
        return null;
    }
}