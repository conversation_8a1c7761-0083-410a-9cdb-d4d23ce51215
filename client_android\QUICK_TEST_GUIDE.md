# Android VPN 快速测试指南

## 修复的问题
- **VPN权限崩溃**：修复了启动VPN时的权限检查逻辑，避免应用崩溃
- **权限检查逻辑**：在ViewModel中先检查权限，再启动VPN服务
- **协议格式错误**：修复了CocoProxy协议格式，与服务器端保持一致
- **VPN实现复杂**：简化了数据包处理，使用HTTP代理重定向

## 修复后的流程
1. 用户点击启动VPN
2. ViewModel直接启动VPN服务
3. VPN服务检查权限
4. 如果权限未授予 → VPN服务发送广播 → MainActivity接收广播并弹出权限对话框
5. 用户授权后 → MainActivity重新启动VPN服务
6. 如果权限已授予 → VPN服务直接启动

## 快速测试步骤

### 1. 编译安装
```bash
cd client_android
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. 配置应用
1. 打开CocoProxy应用
2. 配置服务器信息：
   - 服务器地址：你的服务器IP
   - 端口：28888
   - 用户名：有效用户名
3. 进入应用分流设置
4. 选择"白名单模式"
5. 添加Chrome：`com.android.chrome`

### 3. 启动VPN
1. 点击"启动VPN"
2. **重要**：当弹出VPN权限对话框时，点击"确定"授权
3. 等待VPN连接成功

### 4. 测试访问
1. 打开Chrome浏览器
2. 访问 `https://google.com`
3. 检查是否能正常访问

## 关键日志检查

使用以下命令查看日志：
```bash
adb logcat -s CocoProxyVpnService MainViewModel
```

### 成功的日志应该包含：
```
MainViewModel: VPN permission not granted, requesting permission
MainActivity: VPN permission granted by user
MainViewModel: Starting VPN proxy after permission granted
CocoProxyVpnService: Started local SOCKS5 proxy server on 127.0.0.1:1080
CocoProxyVpnService: Sending CocoProxy packet: X bytes
CocoProxyVpnService: Protocol: 3, Encrypt: 0, User: username
CocoProxyVpnService: SOCKS5 auth successful
CocoProxyVpnService: VPN interface established successfully
```

## 故障排除

### 如果应用崩溃：
- 检查是否授予了VPN权限
- 查看logcat中的错误信息

### 如果Chrome无法访问：
- 确认Chrome在白名单中
- 检查服务器配置是否正确
- 查看SOCKS5代理是否启动

### 如果VPN无法启动：
- 确认没有其他VPN应用在运行
- 重启应用重试
- 检查权限是否正确授予

## 预期结果
修复成功后：
- 应用不会因VPN权限而崩溃
- VPN权限对话框正常弹出并可以授权
- Chrome能够通过代理访问google.com
- 其他应用（非白名单）直接连接

这个修复解决了最关键的崩溃问题，现在应该可以正常测试VPN功能了。
