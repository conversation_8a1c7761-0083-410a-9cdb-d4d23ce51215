package com.cocoproxy.client.ui;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0080\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0019\u001a\u00020\u001a2\b\u0010\u001b\u001a\u0004\u0018\u00010\u001cH\u0014J\b\u0010\u001d\u001a\u00020\u001aH\u0002J\b\u0010\u001e\u001a\u00020\u001aH\u0002J\b\u0010\u001f\u001a\u00020\u001aH\u0002J\b\u0010 \u001a\u00020\u001aH\u0002J\b\u0010!\u001a\u00020\u001aH\u0002J\b\u0010\"\u001a\u00020\u001aH\u0002J\u0010\u0010#\u001a\u00020\u001a2\u0006\u0010$\u001a\u00020%H\u0002J\u0010\u0010&\u001a\u00020\u001a2\u0006\u0010\'\u001a\u00020%H\u0002J\u0010\u0010(\u001a\u00020\u001a2\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u0010+\u001a\u00020\u001a2\u0006\u0010,\u001a\u00020-H\u0002J\u0010\u0010.\u001a\u00020/2\u0006\u0010)\u001a\u00020*H\u0002J\u0010\u00100\u001a\u00020/2\u0006\u00101\u001a\u000202H\u0002J\b\u00103\u001a\u00020\u001aH\u0002J\b\u00104\u001a\u000205H\u0016R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0016X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/cocoproxy/client/ui/AppSelectionActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "<init>", "()V", "viewModel", "Lcom/cocoproxy/client/viewmodel/AppSelectionViewModel;", "adapter", "Lcom/cocoproxy/client/ui/adapter/AppSelectionAdapter;", "spinnerSplitMode", "Landroid/widget/Spinner;", "spinnerCategory", "editTextSearch", "Landroid/widget/EditText;", "switchIncludeSystem", "Landroid/widget/Switch;", "recyclerViewApps", "Landroidx/recyclerview/widget/RecyclerView;", "progressBar", "Landroid/widget/ProgressBar;", "textViewStatus", "Landroid/widget/TextView;", "buttonSelectAll", "Landroid/widget/Button;", "buttonDeselectAll", "buttonSave", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "initViews", "initViewModel", "setupRecyclerView", "setupSpinners", "setupSearchAndFilters", "setupButtons", "updateStatus", "appCount", "", "updateSelectionButtons", "selectedCount", "updateSplitModeDescription", "splitMode", "Lcom/cocoproxy/client/core/SplitMode;", "showAppDetails", "app", "Lcom/cocoproxy/client/data/model/InstalledApp;", "getSplitModeDisplayName", "", "getCategoryDisplayName", "category", "Lcom/cocoproxy/client/data/model/AppCategory;", "showAppStatistics", "onSupportNavigateUp", "", "app_debug"})
public final class AppSelectionActivity extends androidx.appcompat.app.AppCompatActivity {
    private com.cocoproxy.client.viewmodel.AppSelectionViewModel viewModel;
    private com.cocoproxy.client.ui.adapter.AppSelectionAdapter adapter;
    private android.widget.Spinner spinnerSplitMode;
    private android.widget.Spinner spinnerCategory;
    private android.widget.EditText editTextSearch;
    private android.widget.Switch switchIncludeSystem;
    private androidx.recyclerview.widget.RecyclerView recyclerViewApps;
    private android.widget.ProgressBar progressBar;
    private android.widget.TextView textViewStatus;
    private android.widget.Button buttonSelectAll;
    private android.widget.Button buttonDeselectAll;
    private android.widget.Button buttonSave;
    
    public AppSelectionActivity() {
        super();
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void initViews() {
    }
    
    private final void initViewModel() {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void setupSpinners() {
    }
    
    private final void setupSearchAndFilters() {
    }
    
    private final void setupButtons() {
    }
    
    private final void updateStatus(int appCount) {
    }
    
    private final void updateSelectionButtons(int selectedCount) {
    }
    
    private final void updateSplitModeDescription(com.cocoproxy.client.core.SplitMode splitMode) {
    }
    
    private final void showAppDetails(com.cocoproxy.client.data.model.InstalledApp app) {
    }
    
    private final java.lang.String getSplitModeDisplayName(com.cocoproxy.client.core.SplitMode splitMode) {
        return null;
    }
    
    private final java.lang.String getCategoryDisplayName(com.cocoproxy.client.data.model.AppCategory category) {
        return null;
    }
    
    private final void showAppStatistics() {
    }
    
    @java.lang.Override()
    public boolean onSupportNavigateUp() {
        return false;
    }
}