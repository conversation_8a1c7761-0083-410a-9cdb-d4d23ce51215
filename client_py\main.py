#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import logging
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QCoreApplication, Qt

from ui.main_window import MainWindow
from core.config import Config
from core.proxy_client import ProxyClient
from core.logger import setup_logger

def main():
    # Setup logging
    logger = setup_logger()
    logger.info("Starting CocoProxy Client")
    
    try:
        # Enable high DPI scaling
        QCoreApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QCoreApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # Create application
        app = QApplication(sys.argv)
        app.setApplicationName("CocoProxy Client")
        app.setOrganizationName("CocoProxy")
        app.setApplicationVersion("1.0.0")
        
        # Set application icon for taskbar
        icon_path = os.path.join(os.path.dirname(__file__), 'resources', 'icon.ico')
        if os.path.exists(icon_path):
            from PySide6.QtGui import QIcon
            app.setWindowIcon(QIcon(icon_path))
            logger.info(f"Set application icon: {icon_path}")
            
            # Windows-specific taskbar icon fix
            if sys.platform == 'win32':
                try:
                    import ctypes
                    # Set application user model ID for Windows taskbar
                    ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID('CocoProxy.Client.1.0')
                    logger.info("Set Windows application user model ID")
                except Exception as e:
                    logger.warning(f"Failed to set Windows app user model ID: {e}")
        else:
            logger.warning(f"Application icon not found: {icon_path}")
        
        # Load configuration
        config = Config()
        
        # Create proxy client
        proxy_client = ProxyClient(config)
        
        # Create and show main window
        main_window = MainWindow(config, proxy_client)
        main_window.show()
        
        logger.info("Application started successfully")
        
        # Run application
        return app.exec()
        
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        
        # Show error message if possible
        try:
            QMessageBox.critical(None, "Startup Error", f"Failed to start CocoProxy Client:\n{str(e)}")
        except:
            print(f"Failed to start CocoProxy Client: {str(e)}")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())