package main

import (
	"database/sql"
	"fmt"
	"log"
	"sync"

	_ "github.com/mattn/go-sqlite3"
)

// User represents a proxy user
type User struct {
	ID           int    `json:"id"`
	Username     string `json:"username"`
	PasswordHash string `json:"password_hash"` // Hashed password
	Encrypt<PERSON>ey   string `json:"encrypt_key"`   // User-specific encryption key
	TrafficLimit int64  `json:"traffic_limit"` // Traffic limit in bytes
	UsedTraffic  int64  `json:"used_traffic"`  // Used traffic in bytes
	IsAdmin      bool   `json:"is_admin"`      // Admin privilege
}

var ()

// InitDB initializes the SQLite database and creates the users table if it doesn't exist
func InitDB(dataSourceName string) (*sql.DB, error) {
	db, err := sql.Open("sqlite3", dataSourceName)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	createTableSQL := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT NOT NULL UNIQUE,
		password_hash TEXT NOT NULL,
		encrypt_key TEXT NOT NULL,
		traffic_limit INTEGER DEFAULT 0,
		used_traffic INTEGER DEFAULT 0,
		is_admin BOOLEAN DEFAULT FALSE
	);
	`

	_, err = db.Exec(createTableSQL)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to create users table: %w", err)
	}

	log.Println("Database initialized successfully.")
	return db, nil
}

// AddUser adds a new user to the database
func AddUser(db *sql.DB, user *User) error {
	stmt, err := db.Prepare("INSERT INTO users(username, password_hash, encrypt_key, traffic_limit, is_admin) VALUES(?, ?, ?, ?, ?)")
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(user.Username, user.PasswordHash, user.EncryptKey, user.TrafficLimit, user.IsAdmin)
	if err != nil {
		return fmt.Errorf("failed to add user: %w", err)
	}
	log.Printf("User %s added successfully.", user.Username)
	return nil
}

// GetUserByUsername retrieves a user by username
func GetUserByUsername(db *sql.DB, username string) (*User, error) {
	row := db.QueryRow("SELECT id, username, password_hash, encrypt_key, traffic_limit, used_traffic, is_admin FROM users WHERE username = ?", username)

	user := &User{}
	err := row.Scan(&user.ID, &user.Username, &user.PasswordHash, &user.EncryptKey, &user.TrafficLimit, &user.UsedTraffic, &user.IsAdmin)
	if err == sql.ErrNoRows {
		return nil, nil // User not found
	} else if err != nil {
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}
	return user, nil
}

// GetUserByID retrieves a user by ID
func GetUserByID(db *sql.DB, id int) (*User, error) {
	row := db.QueryRow("SELECT id, username, password_hash, encrypt_key, traffic_limit, used_traffic, is_admin FROM users WHERE id = ?", id)

	user := &User{}
	err := row.Scan(&user.ID, &user.Username, &user.PasswordHash, &user.EncryptKey, &user.TrafficLimit, &user.UsedTraffic, &user.IsAdmin)
	if err == sql.ErrNoRows {
		return nil, nil // User not found
	} else if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", err)
	}
	return user, nil
}

// UpdateUser updates an existing user's information
func UpdateUser(db *sql.DB, user *User) error {
	stmt, err := db.Prepare("UPDATE users SET password_hash = ?, encrypt_key = ?, traffic_limit = ?, used_traffic = ?, is_admin = ? WHERE id = ?")
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(user.PasswordHash, user.EncryptKey, user.TrafficLimit, user.UsedTraffic, user.IsAdmin, user.ID)
	if err != nil {
		return fmt.Errorf("failed to update user: %w", err)
	}
	log.Printf("User %s updated successfully.", user.Username)
	return nil
}

// DeleteUser deletes a user by ID
func DeleteUser(db *sql.DB, id int) error {
	stmt, err := db.Prepare("DELETE FROM users WHERE id = ?")
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(id)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}
	log.Printf("User with ID %d deleted successfully.", id)
	return nil
}

// GetAllUsers retrieves all users from the database
func GetAllUsers(db *sql.DB) ([]*User, error) {
	rows, err := db.Query("SELECT id, username, password_hash, encrypt_key, traffic_limit, used_traffic, is_admin FROM users")
	if err != nil {
		return nil, fmt.Errorf("failed to query users: %w", err)
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		user := &User{}
		err := rows.Scan(&user.ID, &user.Username, &user.PasswordHash, &user.EncryptKey, &user.TrafficLimit, &user.UsedTraffic, &user.IsAdmin)
		if err != nil {
			log.Printf("Error scanning user row: %v", err)
			continue
		}
		users = append(users, user)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating user rows: %w", err)
	}
	return users, nil
}

// UpdateUsedTraffic updates the used traffic for a user
func UpdateUsedTraffic(db *sql.DB, userID int, trafficDelta int64) error {
	stmt, err := db.Prepare("UPDATE users SET used_traffic = used_traffic + ? WHERE id = ?")
	if err != nil {
		return fmt.Errorf("failed to prepare statement for traffic update: %w", err)
	}
	defer stmt.Close()

	_, err = stmt.Exec(trafficDelta, userID)
	if err != nil {
		return fmt.Errorf("failed to update used traffic: %w", err)
	}
	return nil
}

// UserManager provides thread-safe access to user data
type UserManager struct {
	db *sql.DB
	mu sync.RWMutex
}

// NewUserManager creates a new UserManager
func NewUserManager(db *sql.DB) *UserManager {
	return &UserManager{db: db}
}

// GetUser retrieves a user by username from the manager
func (um *UserManager) GetUser(username string) (*User, error) {
	um.mu.RLock()
	defer um.mu.RUnlock()
	return GetUserByUsername(um.db, username)
}

// RecordTraffic records traffic for a user
func (um *UserManager) RecordTraffic(userID int, trafficDelta int64) error {
	um.mu.Lock()
	defer um.mu.Unlock()
	return UpdateUsedTraffic(um.db, userID, trafficDelta)
}