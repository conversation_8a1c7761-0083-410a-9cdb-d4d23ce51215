package com.cocoproxy.client.core;

/**
 * SOCKS5 client implementation for proxy connections
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u0000  2\u00020\u0001:\u0001 B/\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0004\b\b\u0010\tJ \u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u0005H\u0086@\u00a2\u0006\u0002\u0010\u000eJ\u001e\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0082@\u00a2\u0006\u0002\u0010\u0015J\u001e\u0010\u0016\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0014H\u0082@\u00a2\u0006\u0002\u0010\u0015J.\u0010\u0017\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00142\u0006\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u0005H\u0082@\u00a2\u0006\u0002\u0010\u0018J4\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u00142\u0014\b\u0002\u0010\u001d\u001a\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u001a0\u001eH\u0086@\u00a2\u0006\u0002\u0010\u001fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0006\u001a\u0004\u0018\u00010\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/cocoproxy/client/core/Socks5Client;", "", "proxyHost", "", "proxyPort", "", "username", "password", "<init>", "(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V", "connect", "Ljava/net/Socket;", "targetHost", "targetPort", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "performHandshake", "", "input", "Ljava/io/InputStream;", "output", "Ljava/io/OutputStream;", "(Ljava/io/InputStream;Ljava/io/OutputStream;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "authenticate", "sendConnectRequest", "(Ljava/io/InputStream;Ljava/io/OutputStream;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "forwardData", "", "sourceInput", "targetOutput", "onDataTransferred", "Lkotlin/Function1;", "(Ljava/io/InputStream;Ljava/io/OutputStream;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_debug"})
public final class Socks5Client {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String proxyHost = null;
    private final int proxyPort = 0;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String username = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String password = null;
    private static final int SOCKS_VERSION = 5;
    private static final int NO_AUTH = 0;
    private static final int USERNAME_PASSWORD_AUTH = 2;
    private static final int CONNECT_COMMAND = 1;
    private static final int IPV4_ADDRESS_TYPE = 1;
    private static final int DOMAIN_ADDRESS_TYPE = 3;
    private static final int SUCCESS_REPLY = 0;
    private static final int CONNECTION_TIMEOUT = 10000;
    private static final int READ_TIMEOUT = 30000;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.core.Socks5Client.Companion Companion = null;
    
    public Socks5Client(@org.jetbrains.annotations.NotNull()
    java.lang.String proxyHost, int proxyPort, @org.jetbrains.annotations.Nullable()
    java.lang.String username, @org.jetbrains.annotations.Nullable()
    java.lang.String password) {
        super();
    }
    
    /**
     * Connect to target through SOCKS5 proxy
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object connect(@org.jetbrains.annotations.NotNull()
    java.lang.String targetHost, int targetPort, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.net.Socket> $completion) {
        return null;
    }
    
    /**
     * Perform SOCKS5 initial handshake
     */
    private final java.lang.Object performHandshake(java.io.InputStream input, java.io.OutputStream output, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Perform username/password authentication
     */
    private final java.lang.Object authenticate(java.io.InputStream input, java.io.OutputStream output, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Send CONNECT request
     */
    private final java.lang.Object sendConnectRequest(java.io.InputStream input, java.io.OutputStream output, java.lang.String targetHost, int targetPort, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * Forward data between two streams
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object forwardData(@org.jetbrains.annotations.NotNull()
    java.io.InputStream sourceInput, @org.jetbrains.annotations.NotNull()
    java.io.OutputStream targetOutput, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onDataTransferred, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\t\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/cocoproxy/client/core/Socks5Client$Companion;", "", "<init>", "()V", "SOCKS_VERSION", "", "NO_AUTH", "USERNAME_PASSWORD_AUTH", "CONNECT_COMMAND", "IPV4_ADDRESS_TYPE", "DOMAIN_ADDRESS_TYPE", "SUCCESS_REPLY", "CONNECTION_TIMEOUT", "READ_TIMEOUT", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}