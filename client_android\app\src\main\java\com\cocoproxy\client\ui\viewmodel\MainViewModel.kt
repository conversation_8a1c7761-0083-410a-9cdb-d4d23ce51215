package com.cocoproxy.client.ui.viewmodel

import android.app.Application
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.cocoproxy.client.core.CocoProxyClient
import com.cocoproxy.client.data.model.*
import com.cocoproxy.client.data.repository.ConfigRepository
import com.cocoproxy.client.service.CocoProxyService
import com.cocoproxy.client.service.CocoProxyVpnService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    application: Application,
    private val cocoProxyClient: CocoProxyClient,
    private val configRepository: ConfigRepository
) : AndroidViewModel(application) {
    
    companion object {
        private const val TAG = "MainViewModel"
    }
    
    private val context: Context = application.applicationContext
    
    // Configuration flows
    val serverConfig = configRepository.serverConfig
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ServerConfig()
        )
    
    val proxyConfig = configRepository.proxyConfig
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ProxyConfig()
        )
    
    val uiConfig = configRepository.uiConfig
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = UiConfig()
        )
    
    val vpnConfig = configRepository.vpnConfig
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = VpnConfig()
        )
    
    val userProfile = configRepository.userProfile
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    // Client state flows
    val connectionState = cocoProxyClient.connectionState
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = CocoProxyClient.ConnectionState.DISCONNECTED
        )
    
    val trafficData = cocoProxyClient.trafficData
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = TrafficData()
        )
    
    val errorMessage = cocoProxyClient.errorMessage
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = null
        )
    
    // Proxy status
    private val _proxyStatus = MutableStateFlow(
        ProxyStatus(
            isRunning = false,
            connectionStatus = ConnectionStatus.DISCONNECTED
        )
    )
    val proxyStatus = _proxyStatus.asStateFlow()
    
    // Connection status derived from proxy status
    val connectionStatus = proxyStatus
        .map { it.connectionStatus }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = ConnectionStatus.DISCONNECTED
        )
    
    // VPN permission request
    private val _vpnPermissionRequest = MutableLiveData<Boolean>(false)
    val vpnPermissionRequest: LiveData<Boolean> = _vpnPermissionRequest
    
    // Loading states
    private val _isLoading = MutableStateFlow(false)
    val isLoading = _isLoading.asStateFlow()
    
    // Snackbar messages
    private val _snackbarMessage = MutableStateFlow<String?>(null)
    val snackbarMessage = _snackbarMessage.asStateFlow()
    
    // Navigation events
    private val _navigationEvent = MutableStateFlow<NavigationEvent?>(null)
    val navigationEvent = _navigationEvent.asStateFlow()
    
    // Logs
    private val _logs = MutableStateFlow<List<LogEntry>>(emptyList())
    val logs = _logs.asStateFlow()
    
    init {
        observeConnectionState()
        observeErrorMessages()
        
        // Auto-connect if credentials are saved
        autoConnectIfPossible()
    }
    
    /**
     * Check if login is required
     */
    fun isLoginRequired(): Boolean {
        val serverCfg = serverConfig.value
        // If remember me is enabled and we have username/password, we can auto-login
        if (serverCfg.rememberMe && 
            serverCfg.username.isNotBlank() && 
            serverCfg.password.isNotBlank()) {
            return false
        }
        // Otherwise, check if we have a valid token
        return serverCfg.token.isBlank()
    }
    
    /**
     * Start proxy service
     */
    fun startProxy() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                // Force reload configs to get latest values
                val serverCfg = configRepository.serverConfig.first()
                val proxyCfg = configRepository.proxyConfig.first()
                val vpnCfg = configRepository.vpnConfig.first()
                
                Log.d(TAG, "Starting proxy with fresh config - useVpn: ${proxyCfg.useVpn}, enabled: ${vpnCfg.enabled}")
                
                // Check if we need to login first with saved credentials
                if (serverCfg.token.isBlank() && 
                    serverCfg.rememberMe && 
                    serverCfg.username.isNotBlank() && 
                    serverCfg.password.isNotBlank()) {
                    
                    Log.i(TAG, "Attempting auto-login with saved credentials")
                    
                    // Try to login with saved credentials using the same logic as LoginViewModel
                    val loginSuccess = cocoProxyClient.connect(serverCfg, proxyCfg)
                    
                    if (loginSuccess) {
                        // Save the updated config with token
                        configRepository.saveServerConfig(serverCfg)
                        Log.i(TAG, "Auto-login successful, credentials saved")
                    } else {
                        Log.e(TAG, "Auto-login failed: ${cocoProxyClient.errorMessage.value}")
                        return@launch
                    }
                    
                    // Update server config with new token and reload VPN config
                    val updatedServerCfg = configRepository.serverConfig.first()
                    val updatedVpnCfg = configRepository.vpnConfig.first()
                    
                    // Use VPN mode if VPN config is enabled (use fresh config)
                    val shouldUseVpn = updatedVpnCfg.enabled
                    Log.d(TAG, "VPN config enabled: ${updatedVpnCfg.enabled}, will use VPN: $shouldUseVpn")
                    if (shouldUseVpn) {
                        // Use VPN service for system-wide proxy
                        Log.d(TAG, "Requesting VPN permission for system-wide proxy")
                        Log.d(TAG, "Setting _vpnPermissionRequest to true")
                        Log.d(TAG, "Starting VPN proxy directly")
                        startVpnProxy(updatedServerCfg, proxyCfg)
                    } else {
                        // Use regular proxy service
                        Log.d(TAG, "Starting regular proxy service")
                        startRegularProxy(updatedServerCfg, proxyCfg)
                    }
                } else {
                    // Use VPN mode if VPN config is enabled (use fresh config)
                    val shouldUseVpn = vpnCfg.enabled
                    Log.d(TAG, "VPN config enabled: ${vpnCfg.enabled}, will use VPN: $shouldUseVpn")
                    if (shouldUseVpn) {
                        // Use VPN service for system-wide proxy
                        Log.d(TAG, "Requesting VPN permission for system-wide proxy")
                        Log.d(TAG, "Starting VPN proxy directly")
                        startVpnProxy(serverCfg, proxyCfg)
                    } else {
                        // Use regular proxy service
                        Log.d(TAG, "Starting regular proxy service")
                        startRegularProxy(serverCfg, proxyCfg)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start proxy", e)
                _snackbarMessage.value = "Failed to start proxy: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Stop proxy service
     */
    fun stopProxy() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                
                Log.d(TAG, "Stopping all proxy services")
                
                // Stop VPN service with proper action
                val vpnIntent = Intent(context, CocoProxyVpnService::class.java).apply {
                    action = CocoProxyVpnService.ACTION_STOP_VPN
                }
                context.startService(vpnIntent)
                
                // Stop regular proxy service
                val proxyIntent = Intent(context, CocoProxyService::class.java).apply {
                    action = CocoProxyService.ACTION_STOP_PROXY
                }
                context.startService(proxyIntent)
                
                // Disconnect from server
                cocoProxyClient.disconnect()
                
                _proxyStatus.value = _proxyStatus.value.copy(
                    isRunning = false,
                    connectionStatus = ConnectionStatus.DISCONNECTED
                )
                
                _snackbarMessage.value = "Proxy stopped"
                
            } catch (e: Exception) {
                Log.e(TAG, "Failed to stop proxy", e)
                _snackbarMessage.value = "Failed to stop proxy: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Start regular proxy service
     */
    private suspend fun startRegularProxy(serverConfig: ServerConfig, proxyConfig: ProxyConfig) {
        // Connect to server
        val connected = cocoProxyClient.connect(serverConfig, proxyConfig)
        
        if (connected) {
            // Start proxy service
            val intent = Intent(context, CocoProxyService::class.java).apply {
                action = CocoProxyService.ACTION_START_PROXY
                putExtra("proxy_config", proxyConfig)
                putExtra("server_config", serverConfig)
            }
            
            context.startForegroundService(intent)
            
            _proxyStatus.value = _proxyStatus.value.copy(
                isRunning = true,
                connectionStatus = ConnectionStatus.CONNECTED,
                localPort = proxyConfig.localPort,
                serverHost = serverConfig.host,
                serverPort = serverConfig.port,
                protocol = proxyConfig.protocol,
                encryption = proxyConfig.encryption
            )
            
            _snackbarMessage.value = "Proxy started on port ${proxyConfig.localPort}"
        } else {
            throw Exception("Failed to connect to server")
        }
    }
    
    /**
     * Start VPN proxy service
     */
    private suspend fun startVpnProxy(serverConfig: ServerConfig, proxyConfig: ProxyConfig) {
        // Connect to server
        val connected = cocoProxyClient.connect(serverConfig, proxyConfig)
        
        if (connected) {
            // Load current app split configuration
            val appSplitConfig = configRepository.appSplitConfig.first()
            val vpnConfigWithSplit = vpnConfig.value.copy(
                enabled = true,
                appSplitConfig = appSplitConfig
            )
            
            Log.d(TAG, "Starting VPN with app split config: mode=${appSplitConfig.mode}, enabled=${appSplitConfig.enabled}, selectedApps=${appSplitConfig.selectedApps}")
            
            // Start VPN service
            val intent = Intent(context, CocoProxyVpnService::class.java).apply {
                action = CocoProxyVpnService.ACTION_START_VPN
                putExtra("proxy_config", proxyConfig)
                putExtra("server_config", serverConfig)
                putExtra("vpn_config", vpnConfigWithSplit)
            }
            
            context.startForegroundService(intent)
            
            _proxyStatus.value = _proxyStatus.value.copy(
                isRunning = true,
                connectionStatus = ConnectionStatus.CONNECTED,
                localPort = proxyConfig.localPort,
                serverHost = serverConfig.host,
                serverPort = serverConfig.port,
                protocol = proxyConfig.protocol,
                encryption = proxyConfig.encryption
            )
            
            _snackbarMessage.value = "VPN proxy started"
        } else {
            throw Exception("Failed to connect to server")
        }
    }
    
    /**
     * Handle VPN permission granted
     */
    fun onVpnPermissionGranted() {
        Log.d(TAG, "onVpnPermissionGranted called")
        viewModelScope.launch {
            try {
                Log.d(TAG, "Starting VPN proxy after permission granted")
                val serverCfg = serverConfig.value
                val proxyCfg = proxyConfig.value
                startVpnProxy(serverCfg, proxyCfg)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start VPN proxy", e)
                _snackbarMessage.value = "Failed to start VPN proxy: ${e.message}"
            }
        }
    }
    
    /**
     * Handle VPN permission denied
     */
    fun onVpnPermissionDenied() {
        _snackbarMessage.value = "VPN permission denied. Using regular proxy instead."
        viewModelScope.launch {
            try {
                val serverCfg = serverConfig.value
                val proxyCfg = proxyConfig.value.copy(useVpn = false)
                startRegularProxy(serverCfg, proxyCfg)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to start regular proxy", e)
                _snackbarMessage.value = "Failed to start proxy: ${e.message}"
            }
        }
    }
    
    /**
     * Save server configuration
     */
    fun saveServerConfig(config: ServerConfig) {
        viewModelScope.launch {
            try {
                configRepository.saveServerConfig(config)
                _snackbarMessage.value = "Server configuration saved"
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save server config", e)
                _snackbarMessage.value = "Failed to save configuration: ${e.message}"
            }
        }
    }
    
    /**
     * Save proxy configuration
     */
    fun saveProxyConfig(config: ProxyConfig) {
        viewModelScope.launch {
            try {
                configRepository.saveProxyConfig(config)
                _snackbarMessage.value = "Proxy configuration saved"
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save proxy config", e)
                _snackbarMessage.value = "Failed to save configuration: ${e.message}"
            }
        }
    }
    
    /**
     * Save UI configuration
     */
    fun saveUiConfig(config: UiConfig) {
        viewModelScope.launch {
            try {
                configRepository.saveUiConfig(config)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to save UI config", e)
                _snackbarMessage.value = "Failed to save UI configuration: ${e.message}"
            }
        }
    }
    
    /**
     * Update VPN configuration
     */
    fun updateVpnConfig(config: VpnConfig) {
        Log.d(TAG, "updateVpnConfig called with enabled: ${config.enabled}")
        viewModelScope.launch {
            try {
                configRepository.saveVpnConfig(config)
                Log.d(TAG, "VPN config saved successfully - enabled: ${config.enabled}")
                _snackbarMessage.value = "VPN configuration updated"
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update VPN config", e)
                _snackbarMessage.value = "Failed to update VPN configuration: ${e.message}"
            }
        }
    }
    
    /**
     * Update UI configuration
     */
    fun updateUiConfig(config: UiConfig) {
        viewModelScope.launch {
            try {
                configRepository.saveUiConfig(config)
            } catch (e: Exception) {
                Log.e(TAG, "Failed to update UI config", e)
                _snackbarMessage.value = "Failed to update UI configuration: ${e.message}"
            }
        }
    }
    
    /**
     * Test connection to server
     */
    fun testConnection(config: ServerConfig) {
        viewModelScope.launch {
            try {
                _snackbarMessage.value = "Testing connection..."
                // Simple connection test using socket
                val socket = java.net.Socket()
                socket.connect(java.net.InetSocketAddress(config.host, config.port), 5000)
                socket.close()
                _snackbarMessage.value = "Connection test successful"
            } catch (e: Exception) {
                Log.e(TAG, "Connection test failed", e)
                _snackbarMessage.value = "Connection test failed: ${e.message}"
            }
        }
    }
    
    /**
     * Clear snackbar message
     */
    fun clearSnackbarMessage() {
        _snackbarMessage.value = null
    }
    
    /**
     * Clear navigation event
     */
    fun clearNavigationEvent() {
        _navigationEvent.value = null
    }
    
    /**
     * Clear VPN permission request
     */
    fun clearVpnPermissionRequest() {
        Log.d(TAG, "Clearing VPN permission request")
        _vpnPermissionRequest.postValue(false)
    }
    
    /**
     * Navigate to screen
     */
    fun navigateTo(event: NavigationEvent) {
        android.util.Log.d(TAG, "navigateTo called with event: $event")
        _navigationEvent.value = event
        android.util.Log.d(TAG, "navigationEvent set to: ${_navigationEvent.value}")
    }
    
    /**
     * Clear logs
     */
    fun clearLogs() {
        _logs.value = emptyList()
    }
    
    /**
     * Add log entry
     */
    fun addLog(entry: LogEntry) {
        val currentLogs = _logs.value.toMutableList()
        currentLogs.add(entry)
        // Keep only last 1000 logs
        if (currentLogs.size > 1000) {
            currentLogs.removeAt(0)
        }
        _logs.value = currentLogs
    }
    
    /**
     * Observe connection state changes
     */
    private fun observeConnectionState() {
        viewModelScope.launch {
            connectionState.collect { state ->
                val status = when (state) {
                    CocoProxyClient.ConnectionState.DISCONNECTED -> ConnectionStatus.DISCONNECTED
                    CocoProxyClient.ConnectionState.CONNECTING -> ConnectionStatus.CONNECTING
                    CocoProxyClient.ConnectionState.CONNECTED -> ConnectionStatus.CONNECTED
                    CocoProxyClient.ConnectionState.RECONNECTING -> ConnectionStatus.RECONNECTING
                    CocoProxyClient.ConnectionState.ERROR -> ConnectionStatus.ERROR
                }
                
                _proxyStatus.value = _proxyStatus.value.copy(
                    connectionStatus = status
                )
            }
        }
    }
    
    /**
     * Observe error messages
     */
    private fun observeErrorMessages() {
        viewModelScope.launch {
            errorMessage.collect { error ->
                error?.let {
                    _snackbarMessage.value = it
                }
            }
        }
    }
    
    /**
     * Activity lifecycle callbacks
     */
    fun onActivityResumed() {
        // Refresh status when activity resumes
    }
    
    fun onActivityPaused() {
        // Handle activity pause
    }
    
    fun onActivityDestroyed() {
        // Clean up resources
        cocoProxyClient.cleanup()
    }
    
    /**
     * Auto-connect if credentials are saved and remember me is enabled
     */
    private fun autoConnectIfPossible() {
        viewModelScope.launch {
            try {
                val serverCfg = serverConfig.first()
                val proxyCfg = proxyConfig.first()
                
                // Check if we have saved credentials and remember me is enabled
                if (serverCfg.rememberMe && 
                    serverCfg.username.isNotBlank() && 
                    serverCfg.password.isNotBlank() &&
                    serverCfg.host.isNotBlank()) {
                    
                    Log.i(TAG, "Auto-connecting with saved credentials for user: ${serverCfg.username}")
                    
                    // Attempt to connect automatically
                    val connected = cocoProxyClient.connect(serverCfg, proxyCfg)
                    
                    if (connected) {
                        Log.i(TAG, "Auto-connection successful")
                        _snackbarMessage.value = "Auto-connected to CocoProxy server"
                    } else {
                        Log.w(TAG, "Auto-connection failed, user will need to login manually")
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Auto-connection error: ${e.message}", e)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        cocoProxyClient.cleanup()
    }
}

/**
 * Navigation events
 */
sealed class NavigationEvent {
    object ToSettings : NavigationEvent()
    object ToLogs : NavigationEvent()
    object ToAbout : NavigationEvent()
    data class ToServerConfig(val config: ServerConfig) : NavigationEvent()
    data class ToProxyConfig(val config: ProxyConfig) : NavigationEvent()
    object ToAppSelection : NavigationEvent()
}