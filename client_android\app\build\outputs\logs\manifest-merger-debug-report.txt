-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:92:9-100:20
	android:grantUriPermissions
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:96:13-47
	android:authorities
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:94:13-64
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:95:13-37
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:93:13-62
manifest
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:2:1-104:12
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:2:1-104:12
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f35707833982181690cb3c2329c6171\transformed\viewbinding-8.11.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39546f4057aa09c1296d6b5fff36610f\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bd67b71d8115dbabcf0b86491ddf4b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a4aa263d1eb689ca03ffd3b2c9cb68\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f2210bc8a6d534a291247240961b469\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\62d1ad221a09ae174cf4adff92e9811d\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\43c0aad1c13be2cada31cc8ef0ca1a12\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4992229281e1b92fa8e4d31aba9c2dc5\transformed\hilt-android-2.56.2\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6d672960847732cb7a80d2d51952\transformed\navigation-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30fa17476782c50261426b482a9136b2\transformed\navigation-common-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6101365ab2630433d8405de0e746e84d\transformed\navigation-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae57acbb9ec61a641dad6593a500b63\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b2f9d796720166fde0aebef930f0171\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6e986030570dcb76bfc44b2af10b9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58a3c11749570a39d2a8715727b38ef4\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4346c883cde9cf63bcc39156b9f0cbe7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdd7e9968cff011c8af91585ae8585c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0403b5520835ce4ce6fbf69ad3d01ae\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af487bbcc8109d8debbb81cf238b789d\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4cb2c4585d5322ea191b977399580b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0917d8792e60b2ace69e3f9df59b4d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\131eeaccd3f0846c2bb18730a4e3782c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eca9db10787c27faae1e4c1cd6405cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e94ff8696dcc75b46d3c2b42fc8872\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cc71231d7a0229c94f071cec2df84c5\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\792ea9d7689403d8d5d65322dc49da76\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa70dea4b1e9f31b91cca3c2f1f11ae\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f820a56eceb9cc9b6a72f98e6bf417c6\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\09161a22d66b8e761de1796a28f04e5a\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b663043642abf817686cedc2c4559000\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1694f09334e1f7ede4d10c1d9cddabd0\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1428367d134a79390e77e82e8366b8b\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea546b6a426f1efa8554d250d465e5ea\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4645df068e063c918b9201c84e6fab3\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d738f24a3b4b8f739846a9920e07cbe\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\af6f31d190e6a66aae178f42e4061df4\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\283010777e224fcaf0e1a8fd5f51a167\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d18ce481d02f582fe8d6cf70430a2ecf\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2121e312a8ead30b8f6575d5c581555b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e20815d58c62e40ac588d51364710fc\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b844de0dce91d1a451db4248b626492e\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b81ee63b05f864b41dfcf2c3713235\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675e4c95571311d3f9bcc8433af19a3d\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4f30ba4777b6dbd39f2272bcae9aeae\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1338638d984b8099b0c4812e06449d1\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484bbbdf37fd3da9a0421c32173be7a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2a660d1c4e01e5a646e3e29e978a354\transformed\lifecycle-viewmodel-savedstate-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dedc61c32d556c0d249b9f8e1120a496\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd9b927ea32acf86ac29be583a64838c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f262ffa1d6a4096e4ec92aad6d25e5b2\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8cce64f54fa47e0238ebd379e3e057\transformed\savedstate-1.3.0\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfdb696c3965f975af8ef0d2d060349c\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\16ef344c6fe49b80d82ec82c65734dcc\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bededfbe962dab5a2b82a08bd159e553\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e8935ae99503a3c7532e5584243ae7\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\805354c5b606e4f98eb98f9e39ea75e3\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d84b344d6ab1ca303c9dd3525ddfef58\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ae7eba0e0a8b7eea05c5fff89631c3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\715c249fe39ba08f578a688d30e6d5ac\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee8ddedd40ddec5bf51178a5e01da541\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b22c639c6d46c386db00ac8055734df0\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\200211e947d74631f05fd99f25e7733a\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a5ad6fe211fbc3710eb0840c596c1a7\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:17:1-29:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7bb12c4644081e9ccbcf99b4acf0f0a\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ccd3fbc9e03badbd74f53ee8041144\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5224560f49aa765b31729055143bc91\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b42bc809d3551cc5c67d0435f5f5e205\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d8feb00738f3eddca317f218873e9e7\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6d70f39a523d85a5c1dd619d23a60d\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\308ee28a4311c7a75ef33cdb251cde7c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\311dcd1c412326b6f800e203cc5c763b\transformed\datastore-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaee422285a377aeefd287f3595559a4\transformed\room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fb8f0cb80662c61cd63da156ef46ec6\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\facc886edaeaaa2415c321ad948291c2\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca47ecba80e53ed1f07bd27e4933b271\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23f68dba485d6db8056f0e8321154b95\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a531cebb24d40e05eb552bb2e0872269\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddade4c59b882cc53c26fc8f8c13eb09\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01c91693adc5c08c8c8774639c3b2779\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d33d15957742518c5a43e6da3b27d66\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80e1c1751f2015e34323993f2392c0c\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbbacca52993d0fefc75fb737366da1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e7b9faf28e935a468a873237197353\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a9b9dfcb0bceb927c9871ff30ec06f\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a989cf34af60e61de8a1819220024990\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c4e966968ccb2edb6532b7265a162a5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf75fd3e851a399c62181011bdda4784\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba7b84656ed968c5a779033f64752d22\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e507c3f076874a5eb7e300a226c9fe\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:24:5-79
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.BIND_VPN_SERVICE
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:11:5-75
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:11:22-72
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:14:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:14:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_DATA_SYNC
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:15:5-87
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:15:22-84
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:18:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:18:22-78
uses-permission#android.permission.WAKE_LOCK
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:21:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:21:22-65
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:24:5-77
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:24:22-74
application
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:26:5-102:19
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:26:5-102:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39546f4057aa09c1296d6b5fff36610f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39546f4057aa09c1296d6b5fff36610f\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bd67b71d8115dbabcf0b86491ddf4b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bd67b71d8115dbabcf0b86491ddf4b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:22:5-27:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d33d15957742518c5a43e6da3b27d66\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d33d15957742518c5a43e6da3b27d66\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:34:9-35
	android:label
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:32:9-41
	android:fullBackupContent
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:30:9-54
	android:roundIcon
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:33:9-54
	tools:targetApi
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:37:9-29
	android:icon
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:31:9-43
	android:allowBackup
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:28:9-35
	android:theme
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:35:9-47
	android:dataExtractionRules
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:29:9-65
	android:usesCleartextTraffic
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:36:9-44
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:27:9-45
activity#com.cocoproxy.client.ui.MainActivity
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:40:9-50:20
	android:label
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:43:13-45
	android:launchMode
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:45:13-43
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:42:13-36
	android:theme
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:44:13-51
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:41:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:46:13-49:29
action#android.intent.action.MAIN
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:47:17-69
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:48:17-77
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:48:27-74
activity#com.cocoproxy.client.ui.AppSelectionActivity
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:53:9-58:61
	android:parentActivityName
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:58:13-58
	android:label
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:56:13-33
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:55:13-37
	android:theme
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:57:13-51
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:54:13-52
service#com.cocoproxy.client.service.CocoProxyVpnService
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:61:9-69:19
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:64:13-37
	android:permission
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:63:13-69
	android:foregroundServiceType
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:65:13-53
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:62:13-56
intent-filter#action:name:android.net.VpnService
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:66:13-68:29
action#android.net.VpnService
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:67:17-65
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:67:25-62
service#com.cocoproxy.client.service.CocoProxyService
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:72:9-76:56
	android:enabled
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:74:13-35
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:75:13-37
	android:foregroundServiceType
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:76:13-53
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:73:13-53
receiver#com.cocoproxy.client.receiver.BootReceiver
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:79:9-89:20
	android:enabled
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:81:13-35
	android:exported
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:82:13-37
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:80:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:83:13-88:29
	android:priority
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:83:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:84:17-79
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:84:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:85:17-84
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:85:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:86:17-81
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:86:25-78
data
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:87:17-50
	android:scheme
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:87:23-47
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:97:13-99:54
	android:resource
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:99:17-51
	android:name
		ADDED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml:98:17-67
uses-sdk
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f35707833982181690cb3c2329c6171\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1f35707833982181690cb3c2329c6171\transformed\viewbinding-8.11.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39546f4057aa09c1296d6b5fff36610f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39546f4057aa09c1296d6b5fff36610f\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bd67b71d8115dbabcf0b86491ddf4b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\81bd67b71d8115dbabcf0b86491ddf4b\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a4aa263d1eb689ca03ffd3b2c9cb68\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\91a4aa263d1eb689ca03ffd3b2c9cb68\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f2210bc8a6d534a291247240961b469\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9f2210bc8a6d534a291247240961b469\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\62d1ad221a09ae174cf4adff92e9811d\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\62d1ad221a09ae174cf4adff92e9811d\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\43c0aad1c13be2cada31cc8ef0ca1a12\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\43c0aad1c13be2cada31cc8ef0ca1a12\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4992229281e1b92fa8e4d31aba9c2dc5\transformed\hilt-android-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4992229281e1b92fa8e4d31aba9c2dc5\transformed\hilt-android-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6d672960847732cb7a80d2d51952\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6d672960847732cb7a80d2d51952\transformed\navigation-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30fa17476782c50261426b482a9136b2\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\30fa17476782c50261426b482a9136b2\transformed\navigation-common-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6101365ab2630433d8405de0e746e84d\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6101365ab2630433d8405de0e746e84d\transformed\navigation-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae57acbb9ec61a641dad6593a500b63\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7ae57acbb9ec61a641dad6593a500b63\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b2f9d796720166fde0aebef930f0171\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b2f9d796720166fde0aebef930f0171\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6e986030570dcb76bfc44b2af10b9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6dd6e986030570dcb76bfc44b2af10b9\transformed\work-runtime-ktx-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58a3c11749570a39d2a8715727b38ef4\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58a3c11749570a39d2a8715727b38ef4\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4346c883cde9cf63bcc39156b9f0cbe7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4346c883cde9cf63bcc39156b9f0cbe7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdd7e9968cff011c8af91585ae8585c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bdd7e9968cff011c8af91585ae8585c9\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0403b5520835ce4ce6fbf69ad3d01ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b0403b5520835ce4ce6fbf69ad3d01ae\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af487bbcc8109d8debbb81cf238b789d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\af487bbcc8109d8debbb81cf238b789d\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4cb2c4585d5322ea191b977399580b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4cb2c4585d5322ea191b977399580b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0917d8792e60b2ace69e3f9df59b4d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d0917d8792e60b2ace69e3f9df59b4d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\131eeaccd3f0846c2bb18730a4e3782c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\131eeaccd3f0846c2bb18730a4e3782c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eca9db10787c27faae1e4c1cd6405cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1eca9db10787c27faae1e4c1cd6405cb\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e94ff8696dcc75b46d3c2b42fc8872\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e94ff8696dcc75b46d3c2b42fc8872\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cc71231d7a0229c94f071cec2df84c5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cc71231d7a0229c94f071cec2df84c5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\792ea9d7689403d8d5d65322dc49da76\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\792ea9d7689403d8d5d65322dc49da76\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa70dea4b1e9f31b91cca3c2f1f11ae\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa70dea4b1e9f31b91cca3c2f1f11ae\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f820a56eceb9cc9b6a72f98e6bf417c6\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f820a56eceb9cc9b6a72f98e6bf417c6\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\09161a22d66b8e761de1796a28f04e5a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\09161a22d66b8e761de1796a28f04e5a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b663043642abf817686cedc2c4559000\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b663043642abf817686cedc2c4559000\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1694f09334e1f7ede4d10c1d9cddabd0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\1694f09334e1f7ede4d10c1d9cddabd0\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1428367d134a79390e77e82e8366b8b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b1428367d134a79390e77e82e8366b8b\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea546b6a426f1efa8554d250d465e5ea\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea546b6a426f1efa8554d250d465e5ea\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4645df068e063c918b9201c84e6fab3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4645df068e063c918b9201c84e6fab3\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d738f24a3b4b8f739846a9920e07cbe\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d738f24a3b4b8f739846a9920e07cbe\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\af6f31d190e6a66aae178f42e4061df4\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\af6f31d190e6a66aae178f42e4061df4\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\283010777e224fcaf0e1a8fd5f51a167\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\283010777e224fcaf0e1a8fd5f51a167\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d18ce481d02f582fe8d6cf70430a2ecf\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d18ce481d02f582fe8d6cf70430a2ecf\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2121e312a8ead30b8f6575d5c581555b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2121e312a8ead30b8f6575d5c581555b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e20815d58c62e40ac588d51364710fc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e20815d58c62e40ac588d51364710fc\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b844de0dce91d1a451db4248b626492e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b844de0dce91d1a451db4248b626492e\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b81ee63b05f864b41dfcf2c3713235\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b81ee63b05f864b41dfcf2c3713235\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675e4c95571311d3f9bcc8433af19a3d\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\675e4c95571311d3f9bcc8433af19a3d\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4f30ba4777b6dbd39f2272bcae9aeae\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a4f30ba4777b6dbd39f2272bcae9aeae\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1338638d984b8099b0c4812e06449d1\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1338638d984b8099b0c4812e06449d1\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484bbbdf37fd3da9a0421c32173be7a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5484bbbdf37fd3da9a0421c32173be7a\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2a660d1c4e01e5a646e3e29e978a354\transformed\lifecycle-viewmodel-savedstate-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e2a660d1c4e01e5a646e3e29e978a354\transformed\lifecycle-viewmodel-savedstate-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dedc61c32d556c0d249b9f8e1120a496\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dedc61c32d556c0d249b9f8e1120a496\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd9b927ea32acf86ac29be583a64838c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd9b927ea32acf86ac29be583a64838c\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f262ffa1d6a4096e4ec92aad6d25e5b2\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f262ffa1d6a4096e4ec92aad6d25e5b2\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8cce64f54fa47e0238ebd379e3e057\transformed\savedstate-1.3.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.savedstate:savedstate:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf8cce64f54fa47e0238ebd379e3e057\transformed\savedstate-1.3.0\AndroidManifest.xml:4:5-43
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfdb696c3965f975af8ef0d2d060349c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cfdb696c3965f975af8ef0d2d060349c\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\16ef344c6fe49b80d82ec82c65734dcc\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\16ef344c6fe49b80d82ec82c65734dcc\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bededfbe962dab5a2b82a08bd159e553\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bededfbe962dab5a2b82a08bd159e553\transformed\lifecycle-service-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e8935ae99503a3c7532e5584243ae7\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e8935ae99503a3c7532e5584243ae7\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\805354c5b606e4f98eb98f9e39ea75e3\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\805354c5b606e4f98eb98f9e39ea75e3\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d84b344d6ab1ca303c9dd3525ddfef58\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d84b344d6ab1ca303c9dd3525ddfef58\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ae7eba0e0a8b7eea05c5fff89631c3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\25ae7eba0e0a8b7eea05c5fff89631c3\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\715c249fe39ba08f578a688d30e6d5ac\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\715c249fe39ba08f578a688d30e6d5ac\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee8ddedd40ddec5bf51178a5e01da541\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ee8ddedd40ddec5bf51178a5e01da541\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b22c639c6d46c386db00ac8055734df0\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\b22c639c6d46c386db00ac8055734df0\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\200211e947d74631f05fd99f25e7733a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\200211e947d74631f05fd99f25e7733a\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a5ad6fe211fbc3710eb0840c596c1a7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a5ad6fe211fbc3710eb0840c596c1a7\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7bb12c4644081e9ccbcf99b4acf0f0a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7bb12c4644081e9ccbcf99b4acf0f0a\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ccd3fbc9e03badbd74f53ee8041144\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\93ccd3fbc9e03badbd74f53ee8041144\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5224560f49aa765b31729055143bc91\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e5224560f49aa765b31729055143bc91\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b42bc809d3551cc5c67d0435f5f5e205\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b42bc809d3551cc5c67d0435f5f5e205\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d8feb00738f3eddca317f218873e9e7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3d8feb00738f3eddca317f218873e9e7\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6d70f39a523d85a5c1dd619d23a60d\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2f6d70f39a523d85a5c1dd619d23a60d\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\308ee28a4311c7a75ef33cdb251cde7c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore-preferences:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\308ee28a4311c7a75ef33cdb251cde7c\transformed\datastore-preferences-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\311dcd1c412326b6f800e203cc5c763b\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.datastore:datastore:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\311dcd1c412326b6f800e203cc5c763b\transformed\datastore-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaee422285a377aeefd287f3595559a4\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\aaee422285a377aeefd287f3595559a4\transformed\room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fb8f0cb80662c61cd63da156ef46ec6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5fb8f0cb80662c61cd63da156ef46ec6\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\facc886edaeaaa2415c321ad948291c2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\facc886edaeaaa2415c321ad948291c2\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca47ecba80e53ed1f07bd27e4933b271\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\ca47ecba80e53ed1f07bd27e4933b271\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23f68dba485d6db8056f0e8321154b95\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\23f68dba485d6db8056f0e8321154b95\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a531cebb24d40e05eb552bb2e0872269\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a531cebb24d40e05eb552bb2e0872269\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddade4c59b882cc53c26fc8f8c13eb09\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ddade4c59b882cc53c26fc8f8c13eb09\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01c91693adc5c08c8c8774639c3b2779\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\01c91693adc5c08c8c8774639c3b2779\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d33d15957742518c5a43e6da3b27d66\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9d33d15957742518c5a43e6da3b27d66\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80e1c1751f2015e34323993f2392c0c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c80e1c1751f2015e34323993f2392c0c\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbbacca52993d0fefc75fb737366da1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9bbbacca52993d0fefc75fb737366da1\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e7b9faf28e935a468a873237197353\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\12e7b9faf28e935a468a873237197353\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a9b9dfcb0bceb927c9871ff30ec06f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\78a9b9dfcb0bceb927c9871ff30ec06f\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a989cf34af60e61de8a1819220024990\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a989cf34af60e61de8a1819220024990\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c4e966968ccb2edb6532b7265a162a5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c4e966968ccb2edb6532b7265a162a5\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf75fd3e851a399c62181011bdda4784\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf75fd3e851a399c62181011bdda4784\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba7b84656ed968c5a779033f64752d22\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ba7b84656ed968c5a779033f64752d22\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e507c3f076874a5eb7e300a226c9fe\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.56.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e507c3f076874a5eb7e300a226c9fe\transformed\dagger-lint-aar-2.56.2\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\WorkSpace\SecurityPrj\CocoProxy\client_android\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\488adaa717445eda3be9c78d0d0ace48\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:33:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\543e22d21d5be547de3622e01584b9ec\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\d90591207b18c0642669f87dc0362404\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0476086797a743475c3f98cd52042ee1\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.cocoproxy.client.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.cocoproxy.client.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4cade9efc845f0b61b194dafd01134c8\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a1983dda27c34347fcf3b10fd9479778\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:23:9-26:79
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:25:13-36
	android:theme
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:26:13-76
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.8.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\ef3fb2ac0493b765828537b21243e1bb\transformed\ui-test-manifest-1.8.3\AndroidManifest.xml:24:13-63
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9fd2ca02f30c6bb511c6e1e896e5981b\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6114e9a6a5970cda3298827bb072c109\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
