package main

import (
	"database/sql"
	"log"
	"time"
	"os"
	"html/template"
	"net/http"

	"github.com/gin-gonic/gin"
	_ "github.com/mattn/go-sqlite3"
	"golang.org/x/crypto/bcrypt"
)

// User represents a user in the system
type User struct {
	ID           int    `json:"id"`
	Username     string `json:"username"`
	PasswordHash string `json:"-"`
	EncryptKey   string `json:"-"`
	TrafficLimit int64  `json:"traffic_limit"`
	UsedTraffic  int64  `json:"used_traffic"`
	IsAdmin      bool   `json:"is_admin"`
}

// UserManager handles user operations
type UserManager struct {
	db *sql.DB
}

// InitDB initializes the database
func InitDB(dbPath string) (*sql.DB, error) {
	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, err
	}

	// Create users table if not exists
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT UNIQUE NOT NULL,
		password_hash TEXT NOT NULL,
		encrypt_key TEXT NOT NULL,
		traffic_limit INTEGER DEFAULT 0,
		used_traffic INTEGER DEFAULT 0,
		is_admin BOOLEAN DEFAULT 0
	)`)

	return db, err
}

// NewUserManager creates a new user manager
func NewUserManager(db *sql.DB) *UserManager {
	return &UserManager{db: db}
}

// GetUser retrieves a user by username
func (um *UserManager) GetUser(username string) (*User, error) {
	row := um.db.QueryRow("SELECT id, username, password_hash, encrypt_key, traffic_limit, used_traffic, is_admin FROM users WHERE username = ?", username)
	
	var user User
	err := row.Scan(&user.ID, &user.Username, &user.PasswordHash, &user.EncryptKey, &user.TrafficLimit, &user.UsedTraffic, &user.IsAdmin)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	
	return &user, nil
}

// AddUser adds a new user to the database
func AddUser(db *sql.DB, user *User) error {
	stmt, err := db.Prepare("INSERT INTO users(username, password_hash, encrypt_key, traffic_limit, is_admin) VALUES(?, ?, ?, ?, ?)")
	if err != nil {
		return err
	}
	defer stmt.Close()
	
	_, err = stmt.Exec(user.Username, user.PasswordHash, user.EncryptKey, user.TrafficLimit, user.IsAdmin)
	return err
}

// AdminSession represents an admin session
type AdminSession struct {
	Token     string    `json:"token"`
	Username  string    `json:"username"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
}

var (
	db *sql.DB
	userManager *UserManager
	serverStartTime time.Time
	adminSessions = make(map[string]*AdminSession)
)

// SetupAdminUI configures and returns the admin UI router
func SetupAdminUI() *gin.Engine {
	router := gin.Default()
	
	// Enable CORS
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})
	
	// Serve static files
	router.Static("/ui", "./admin_ui")
	
	// Redirect root to UI
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/ui/")
	})
	
	// Login endpoint
	router.POST("/api/login", func(c *gin.Context) {
		var loginData struct {
			Username string `json:"username"`
			Password string `json:"password"`
		}
		
		if err := c.BindJSON(&loginData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
			return
		}
		
		user, err := userManager.GetUser(loginData.Username)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Server error"})
			return
		}
		
		if user == nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
			return
		}
		
		if !user.IsAdmin {
			c.JSON(http.StatusForbidden, gin.H{"error": "Not an admin user"})
			return
		}
		
		err = bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(loginData.Password))
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
			return
		}
		
		// Generate session token
		token := generateRandomToken()
		
		// Create session
		session := &AdminSession{
			Token:     token,
			Username:  user.Username,
			CreatedAt: time.Now(),
			ExpiresAt: time.Now().Add(24 * time.Hour),
		}
		
		adminSessions[token] = session
		
		c.JSON(http.StatusOK, gin.H{
			"token": token,
			"user": gin.H{
				"username": user.Username,
				"isAdmin": user.IsAdmin,
			},
		})
	})
	
	return router
}

// generateRandomToken generates a random token
func generateRandomToken() string {
	b := make([]byte, 16)
	_, err := os.ReadFile("/dev/urandom")
	if err != nil {
		// Fallback to time-based token if /dev/urandom is not available
		return time.Now().Format("20060102150405") + "token"
	}
	return time.Now().Format("20060102150405") + string(b)
}

func main() {
	log.Println("Starting Admin UI server only...")
	
	// 初始化数据库
	var err error
	db, err = InitDB("../coco.db")
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer db.Close()

	userManager = NewUserManager(db)
	serverStartTime = time.Now()

	// 确保有管理员用户
	existingAdmin, err := userManager.GetUser("admin")
	if err != nil {
		log.Fatalf("Failed to check for default admin user: %v", err)
	}
	
	if existingAdmin == nil {
		// Create default admin user with password "admin123"
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin123"), bcrypt.DefaultCost)
		if err != nil {
			log.Fatalf("Failed to hash default admin password: %v", err)
		}
		
		adminUser := User{
			Username:     "admin",
			PasswordHash: string(hashedPassword),
			EncryptKey:   "your-secret-key-16",
			IsAdmin:      true,
		}
		
		err = AddUser(db, &adminUser)
		if err != nil {
			log.Fatalf("Failed to add default admin user: %v", err)
		}
		log.Println("Default admin user added. Username: admin, Password: admin123")
	} else {
		log.Println("Admin user already exists")
	}

	// 启动管理界面服务器
	adminRouter := SetupAdminUI()
	adminPort := "8080"
	log.Printf("Admin UI server starting on :%s", adminPort)
	log.Printf("Access the admin panel at: http://localhost:%s/ui/", adminPort)
	
	if err := adminRouter.Run(":" + adminPort); err != nil {
		log.Fatalf("Admin UI server failed to start: %v", err)
	}
}