package com.cocoproxy.client.data.model;

/**
 * Application category for auto-selection
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000f\u00a8\u0006\u0010"}, d2 = {"Lcom/cocoproxy/client/data/model/AppCategory;", "", "<init>", "(Ljava/lang/String;I)V", "SOCIAL", "ENTERTAINMENT", "PRODUCTIVITY", "GAMES", "SHOPPING", "TRAVEL", "NEWS", "FINANCE", "EDUCATION", "HEALTH", "SYSTEM", "OTHER", "app_debug"})
public enum AppCategory {
    /*public static final*/ SOCIAL /* = new SOCIAL() */,
    /*public static final*/ ENTERTAINMENT /* = new ENTERTAINMENT() */,
    /*public static final*/ PRODUCTIVITY /* = new PRODUCTIVITY() */,
    /*public static final*/ GAMES /* = new GAMES() */,
    /*public static final*/ SHOPPING /* = new SHOPPING() */,
    /*public static final*/ TRAVEL /* = new TRAVEL() */,
    /*public static final*/ NEWS /* = new NEWS() */,
    /*public static final*/ FINANCE /* = new FINANCE() */,
    /*public static final*/ EDUCATION /* = new EDUCATION() */,
    /*public static final*/ HEALTH /* = new HEALTH() */,
    /*public static final*/ SYSTEM /* = new SYSTEM() */,
    /*public static final*/ OTHER /* = new OTHER() */;
    
    AppCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.cocoproxy.client.data.model.AppCategory> getEntries() {
        return null;
    }
}