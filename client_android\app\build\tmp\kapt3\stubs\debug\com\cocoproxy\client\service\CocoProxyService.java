package com.cocoproxy.client.service;

@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u00aa\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0000\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u0000 X2\u00020\u0001:\u0003XYZB\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010*\u001a\u00020+2\b\u0010,\u001a\u0004\u0018\u00010-H\u0016J\b\u0010.\u001a\u00020/H\u0016J\"\u00100\u001a\u0002012\b\u0010,\u001a\u0004\u0018\u00010-2\u0006\u00102\u001a\u0002012\u0006\u00103\u001a\u000201H\u0016J\b\u00104\u001a\u00020/H\u0016J\u0016\u00105\u001a\u00020/2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020)J\u0006\u00106\u001a\u00020/J\u000e\u00107\u001a\u00020/H\u0082@\u00a2\u0006\u0002\u00108J\u0016\u00109\u001a\u00020/2\u0006\u0010:\u001a\u00020;H\u0082@\u00a2\u0006\u0002\u0010<J\u0016\u0010=\u001a\u00020/2\u0006\u0010>\u001a\u00020!H\u0082@\u00a2\u0006\u0002\u0010?J\u001e\u0010@\u001a\u00020\u00122\u0006\u0010A\u001a\u00020B2\u0006\u0010C\u001a\u00020DH\u0082@\u00a2\u0006\u0002\u0010EJ\u0016\u0010F\u001a\u00020/2\u0006\u0010>\u001a\u00020!H\u0082@\u00a2\u0006\u0002\u0010?J\u0016\u0010G\u001a\u00020/2\u0006\u0010>\u001a\u00020!H\u0082@\u00a2\u0006\u0002\u0010?J&\u0010H\u001a\u00020/2\u0006\u0010>\u001a\u00020!2\u0006\u0010I\u001a\u00020 2\u0006\u0010J\u001a\u000201H\u0082@\u00a2\u0006\u0002\u0010KJ6\u0010L\u001a\u00020M2\u0006\u0010N\u001a\u00020B2\u0006\u0010O\u001a\u00020D2\u0006\u0010P\u001a\u00020 2\u0006\u0010I\u001a\u00020 2\u0006\u0010J\u001a\u000201H\u0082@\u00a2\u0006\u0002\u0010QJ\u0010\u0010R\u001a\u00020/2\u0006\u0010S\u001a\u00020\u0017H\u0002J\b\u0010T\u001a\u00020/H\u0002J\b\u0010U\u001a\u00020/H\u0002J\b\u0010V\u001a\u00020WH\u0002R\u001e\u0010\u0004\u001a\u00020\u00058\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR\u0012\u0010\n\u001a\u00060\u000bR\u00020\u0000X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\f\u001a\u0004\u0018\u00010\rX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00120\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00120\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0015R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00170\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0015R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001b0\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0015R\u001a\u0010\u001e\u001a\u000e\u0012\u0004\u0012\u00020 \u0012\u0004\u0012\u00020!0\u001fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\"\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010$\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010%\u001a\u00020#X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010&\u001a\u00020\'X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010(\u001a\u00020)X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006["}, d2 = {"Lcom/cocoproxy/client/service/CocoProxyService;", "Landroid/app/Service;", "<init>", "()V", "cocoProxyClient", "Lcom/cocoproxy/client/core/CocoProxyClient;", "getCocoProxyClient", "()Lcom/cocoproxy/client/core/CocoProxyClient;", "setCocoProxyClient", "(Lcom/cocoproxy/client/core/CocoProxyClient;)V", "binder", "Lcom/cocoproxy/client/service/CocoProxyService$ProxyServiceBinder;", "serverSocket", "Ljava/net/ServerSocket;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "_isRunning", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "isRunning", "Lkotlinx/coroutines/flow/StateFlow;", "()Lkotlinx/coroutines/flow/StateFlow;", "_trafficData", "Lcom/cocoproxy/client/data/model/TrafficData;", "trafficData", "getTrafficData", "_connectionStatus", "Lcom/cocoproxy/client/core/CocoProxyClient$ConnectionState;", "connectionStatus", "getConnectionStatus", "activeConnections", "Ljava/util/concurrent/ConcurrentHashMap;", "", "Lcom/cocoproxy/client/service/CocoProxyService$ProxyConnection;", "bytesUploaded", "Ljava/util/concurrent/atomic/AtomicLong;", "bytesDownloaded", "totalConnections", "proxyConfig", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "serverConfig", "Lcom/cocoproxy/client/data/model/ServerConfig;", "onBind", "Landroid/os/IBinder;", "intent", "Landroid/content/Intent;", "onCreate", "", "onStartCommand", "", "flags", "startId", "onDestroy", "startProxy", "stopProxy", "startSocksServer", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleClientConnection", "clientSocket", "Ljava/net/Socket;", "(Ljava/net/Socket;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleSocks5Connection", "connection", "(Lcom/cocoproxy/client/service/CocoProxyService$ProxyConnection;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleSocks5Auth", "input", "Ljava/io/InputStream;", "output", "Ljava/io/OutputStream;", "(Ljava/io/InputStream;Ljava/io/OutputStream;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "handleHttpConnection", "handleHttpsConnection", "forwardData", "targetHost", "targetPort", "(Lcom/cocoproxy/client/service/CocoProxyService$ProxyConnection;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "forwardDataStream", "", "source", "destination", "direction", "(Ljava/io/InputStream;Ljava/io/OutputStream;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTrafficData", "traffic", "updateTrafficStats", "createNotificationChannel", "createNotification", "Landroid/app/Notification;", "Companion", "ProxyServiceBinder", "ProxyConnection", "app_debug"})
public final class CocoProxyService extends android.app.Service {
    private static final int NOTIFICATION_ID = 1002;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String CHANNEL_ID = "proxy_service_channel";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_START_PROXY = "com.cocoproxy.client.START_PROXY";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_STOP_PROXY = "com.cocoproxy.client.STOP_PROXY";
    @javax.inject.Inject()
    public com.cocoproxy.client.core.CocoProxyClient cocoProxyClient;
    @org.jetbrains.annotations.NotNull()
    private final com.cocoproxy.client.service.CocoProxyService.ProxyServiceBinder binder = null;
    @org.jetbrains.annotations.Nullable()
    private java.net.ServerSocket serverSocket;
    @org.jetbrains.annotations.NotNull()
    private kotlinx.coroutines.CoroutineScope serviceScope;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRunning = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.data.model.TrafficData> _trafficData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> trafficData = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> _connectionStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> connectionStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.ConcurrentHashMap<java.lang.String, com.cocoproxy.client.service.CocoProxyService.ProxyConnection> activeConnections = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong bytesUploaded = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong bytesDownloaded = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.concurrent.atomic.AtomicLong totalConnections = null;
    @org.jetbrains.annotations.NotNull()
    private com.cocoproxy.client.data.model.ProxyConfig proxyConfig;
    @org.jetbrains.annotations.NotNull()
    private com.cocoproxy.client.data.model.ServerConfig serverConfig;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.service.CocoProxyService.Companion Companion = null;
    
    public CocoProxyService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.cocoproxy.client.core.CocoProxyClient getCocoProxyClient() {
        return null;
    }
    
    public final void setCocoProxyClient(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.CocoProxyClient p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isRunning() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.data.model.TrafficData> getTrafficData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.cocoproxy.client.core.CocoProxyClient.ConnectionState> getConnectionStatus() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.os.IBinder onBind(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent) {
        return null;
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    public int onStartCommand(@org.jetbrains.annotations.Nullable()
    android.content.Intent intent, int flags, int startId) {
        return 0;
    }
    
    @java.lang.Override()
    public void onDestroy() {
    }
    
    public final void startProxy(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig proxyConfig, @org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig serverConfig) {
    }
    
    public final void stopProxy() {
    }
    
    private final java.lang.Object startSocksServer(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object handleClientConnection(java.net.Socket clientSocket, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object handleSocks5Connection(com.cocoproxy.client.service.CocoProxyService.ProxyConnection connection, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object handleSocks5Auth(java.io.InputStream input, java.io.OutputStream output, kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    private final java.lang.Object handleHttpConnection(com.cocoproxy.client.service.CocoProxyService.ProxyConnection connection, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object handleHttpsConnection(com.cocoproxy.client.service.CocoProxyService.ProxyConnection connection, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object forwardData(com.cocoproxy.client.service.CocoProxyService.ProxyConnection connection, java.lang.String targetHost, int targetPort, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Forward data from source to destination stream
     */
    private final java.lang.Object forwardDataStream(java.io.InputStream source, java.io.OutputStream destination, java.lang.String direction, java.lang.String targetHost, int targetPort, kotlin.coroutines.Continuation<java.lang.Object> $completion) {
        return null;
    }
    
    private final void updateTrafficData(com.cocoproxy.client.data.model.TrafficData traffic) {
    }
    
    private final void updateTrafficStats() {
    }
    
    private final void createNotificationChannel() {
    }
    
    private final android.app.Notification createNotification() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0007X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/cocoproxy/client/service/CocoProxyService$Companion;", "", "<init>", "()V", "NOTIFICATION_ID", "", "CHANNEL_ID", "", "ACTION_START_PROXY", "ACTION_STOP_PROXY", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0082\b\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010\f\u001a\u00020\rJ\t\u0010\u000e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000f\u001a\u00020\u0005H\u00c6\u0003J\u001d\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000b\u00a8\u0006\u0017"}, d2 = {"Lcom/cocoproxy/client/service/CocoProxyService$ProxyConnection;", "", "id", "", "socket", "Ljava/net/Socket;", "<init>", "(Ljava/lang/String;Ljava/net/Socket;)V", "getId", "()Ljava/lang/String;", "getSocket", "()Ljava/net/Socket;", "close", "", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
    static final class ProxyConnection {
        @org.jetbrains.annotations.NotNull()
        private final java.lang.String id = null;
        @org.jetbrains.annotations.NotNull()
        private final java.net.Socket socket = null;
        
        public ProxyConnection(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.net.Socket socket) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String getId() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.net.Socket getSocket() {
            return null;
        }
        
        public final void close() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final java.net.Socket component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.service.CocoProxyService.ProxyConnection copy(@org.jetbrains.annotations.NotNull()
        java.lang.String id, @org.jetbrains.annotations.NotNull()
        java.net.Socket socket) {
            return null;
        }
        
        @java.lang.Override()
        public boolean equals(@org.jetbrains.annotations.Nullable()
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override()
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override()
        @org.jetbrains.annotations.NotNull()
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\b\u0086\u0004\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0006\u0010\u0004\u001a\u00020\u0005\u00a8\u0006\u0006"}, d2 = {"Lcom/cocoproxy/client/service/CocoProxyService$ProxyServiceBinder;", "Landroid/os/Binder;", "<init>", "(Lcom/cocoproxy/client/service/CocoProxyService;)V", "getService", "Lcom/cocoproxy/client/service/CocoProxyService;", "app_debug"})
    public final class ProxyServiceBinder extends android.os.Binder {
        
        public ProxyServiceBinder() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.cocoproxy.client.service.CocoProxyService getService() {
            return null;
        }
    }
}