package com.cocoproxy.client.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.cocoproxy.client.core.AppManager
import com.cocoproxy.client.core.AppSplitConfig
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.data.model.*
import com.cocoproxy.client.data.repository.ConfigRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class AppSelectionViewModel(application: Application) : AndroidViewModel(application) {
    
    private val appManager = AppManager(application)
    private val configRepository = ConfigRepository(application)
    
    // LiveData for UI
    private val _allApps = MutableLiveData<List<InstalledApp>>()
    private val _filteredApps = MutableLiveData<List<InstalledApp>>()
    val filteredApps: LiveData<List<InstalledApp>> = _filteredApps
    
    private val _selectedApps = MutableLiveData<Set<String>>()
    val selectedApps: LiveData<Set<String>> = _selectedApps
    
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading
    
    private val _splitMode = MutableLiveData<SplitMode>()
    val splitMode: LiveData<SplitMode> = _splitMode
    
    // Filter states
    private var searchQuery = ""
    private var filterCategory: AppCategory? = null
    private var includeSystemApps = false
    
    init {
        loadCurrentConfiguration()
    }
    
    /**
     * Load installed apps
     */
    fun loadInstalledApps() {
        viewModelScope.launch {
            _isLoading.value = true
            
            try {
                val apps = withContext(Dispatchers.IO) {
                    appManager.getInstalledApps(includeSystemApps)
                }
                
                _allApps.value = apps
                applyFilters()
            } catch (e: Exception) {
                // Handle error
                _allApps.value = emptyList()
                _filteredApps.value = emptyList()
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    /**
     * Load current configuration
     */
    private fun loadCurrentConfiguration() {
        viewModelScope.launch {
            try {
                configRepository.vpnConfig.collect { config ->
                    val appSplitConfig = config.appSplitConfig
                    _splitMode.value = appSplitConfig.mode
                    _selectedApps.value = appSplitConfig.selectedPackages.toSet()
                }
            } catch (e: Exception) {
                // Use default values
                _splitMode.value = SplitMode.DISABLED
                _selectedApps.value = emptySet()
            }
        }
    }
    
    /**
     * Set search query
     */
    fun setSearchQuery(query: String) {
        searchQuery = query
        applyFilters()
    }
    
    /**
     * Set filter category
     */
    fun setFilterCategory(category: AppCategory?) {
        filterCategory = category
        applyFilters()
    }
    
    /**
     * Set include system apps
     */
    fun setIncludeSystemApps(include: Boolean) {
        if (includeSystemApps != include) {
            includeSystemApps = include
            loadInstalledApps() // Reload apps
        }
    }
    
    /**
     * Set split mode
     */
    fun setSplitMode(mode: SplitMode) {
        _splitMode.value = mode
    }
    
    /**
     * Toggle app selection
     */
    fun toggleAppSelection(app: InstalledApp, isSelected: Boolean) {
        val currentSelected = _selectedApps.value?.toMutableSet() ?: mutableSetOf()
        
        if (isSelected) {
            currentSelected.add(app.packageName)
        } else {
            currentSelected.remove(app.packageName)
        }
        
        _selectedApps.value = currentSelected
    }
    
    /**
     * Select all visible apps
     */
    fun selectAllVisibleApps() {
        val currentSelected = _selectedApps.value?.toMutableSet() ?: mutableSetOf()
        val visibleApps = _filteredApps.value ?: emptyList()
        
        visibleApps.forEach { app ->
            currentSelected.add(app.packageName)
        }
        
        _selectedApps.value = currentSelected
    }
    
    /**
     * Deselect all apps
     */
    fun deselectAllApps() {
        _selectedApps.value = emptySet()
    }
    
    /**
     * Apply filters to app list
     */
    private fun applyFilters() {
        val allApps = _allApps.value ?: return
        
        val filtered = allApps.filter { app ->
            // Search filter
            val matchesSearch = if (searchQuery.isBlank()) {
                true
            } else {
                app.appName.contains(searchQuery, ignoreCase = true) ||
                app.packageName.contains(searchQuery, ignoreCase = true)
            }
            
            // Category filter
            val matchesCategory = filterCategory?.let { category ->
                app.category == category
            } ?: true
            
            matchesSearch && matchesCategory
        }.sortedWith(compareBy<InstalledApp> { it.isSystemApp }.thenBy { it.appName })
        
        _filteredApps.value = filtered
    }
    
    /**
     * Save configuration
     */
    suspend fun saveConfiguration(): Boolean {
        return try {
            withContext(Dispatchers.IO) {
                configRepository.vpnConfig.first().let { currentConfig ->
                    val selectedPackages = _selectedApps.value?.toList() ?: emptyList()
                    val splitMode = _splitMode.value ?: SplitMode.DISABLED
                    
                    val appSplitConfig = AppSplitConfig(
                        enabled = splitMode != SplitMode.DISABLED,
                        mode = splitMode,
                        selectedApps = selectedPackages.toSet()
                    )
                    
                    val updatedConfig = currentConfig.copy(
                        appSplitConfig = appSplitConfig
                    )
                    
                    configRepository.saveVpnConfig(updatedConfig)
                }
                true
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Get app statistics
     */
    fun getAppStatistics(): Map<String, Int> {
        val allApps = _allApps.value ?: emptyList()
        val selectedApps = _selectedApps.value ?: emptySet()
        
        return mapOf(
            "total" to allApps.size,
            "selected" to selectedApps.size,
            "system" to allApps.count { it.isSystemApp },
            "user" to allApps.count { !it.isSystemApp }
        )
    }
    
    fun getAppManager(): AppManager {
        return appManager
    }
    
    /**
     * Get apps by category
     */
    fun getAppsByCategory(): Map<AppCategory, Int> {
        val allApps = _allApps.value ?: emptyList()
        
        return AppCategory.values().associateWith { category ->
            allApps.count { it.category == category }
        }
    }
    
    /**
     * Import apps from package names
     */
    fun importAppsFromPackageNames(packageNames: List<String>) {
        val currentSelected = _selectedApps.value?.toMutableSet() ?: mutableSetOf()
        val allApps = _allApps.value ?: emptyList()
        
        packageNames.forEach { packageName ->
            if (allApps.any { it.packageName == packageName }) {
                currentSelected.add(packageName)
            }
        }
        
        _selectedApps.value = currentSelected
    }
    
    /**
     * Export selected package names
     */
    fun exportSelectedPackageNames(): List<String> {
        return _selectedApps.value?.toList() ?: emptyList()
    }
    
    /**
     * Reset to default configuration
     */
    fun resetToDefault() {
        _splitMode.value = SplitMode.DISABLED
        _selectedApps.value = emptySet()
    }
}