# CocoProxy Go Server - 管理界面完善版

## 概述

这是CocoProxy网络代理服务的Go版本服务端，现在包含了完整的Web管理界面。

## 功能特性

### 后台管理界面
- **用户管理**: 添加、编辑、删除用户，设置流量限制和管理员权限
- **流量监控**: 实时查看用户流量使用情况，包含可视化进度条
- **系统状态**: 显示服务器运行状态、用户统计、总流量使用等
- **系统配置**: 查看服务器配置信息

### 安全特性
- **身份验证**: 基于Token的会话管理
- **密码加密**: 使用bcrypt加密存储用户密码
- **权限控制**: 管理员权限验证

### API接口
- **RESTful API**: 完整的用户管理和监控API
- **跨域支持**: 支持CORS跨域请求
- **错误处理**: 完善的错误处理和响应

## 快速开始

### 1. 启动服务器

```bash
cd server_go
./main.exe  # Windows
# 或
./main      # Linux/Mac
```

### 2. 访问管理界面

打开浏览器访问: `http://localhost:28080`

### 3. 登录管理界面

- **默认用户名**: `admin`
- **默认密码**: `admin123`

## 端口说明

- **28080**: Web管理界面端口
- **28888**: 代理服务端口 (TCP)
- **28889**: UDP代理端口

## 管理界面功能

### 仪表板 (Dashboard)
- 总用户数统计
- 活跃用户数统计  
- 总流量使用统计
- 服务器运行时间

### 用户管理 (User Management)
- 查看所有用户列表
- 添加新用户
- 编辑用户信息（用户名、密码、加密密钥、流量限制、管理员权限）
- 删除用户

### 流量监控 (Traffic Monitor)
- 查看每个用户的流量使用情况
- 流量限制和已用流量对比
- 可视化进度条显示使用率
- 剩余流量计算

### 系统配置 (System Config)
- 查看服务器配置信息
- 服务器地址和端口信息

## API接口文档

### 认证接口

#### 登录
```
POST /api/login
Content-Type: application/json

{
    "username": "admin",
    "password": "admin123"
}
```

### 用户管理接口

#### 获取所有用户
```
GET /api/users
Authorization: <token>
```

#### 获取单个用户
```
GET /api/users/:id
Authorization: <token>
```

#### 添加用户
```
POST /api/users
Authorization: <token>
Content-Type: application/json

{
    "username": "testuser",
    "password_hash": "password123",
    "encrypt_key": "encryption-key",
    "traffic_limit": 1073741824,
    "is_admin": false
}
```

#### 更新用户
```
PUT /api/users/:id
Authorization: <token>
Content-Type: application/json

{
    "username": "testuser",
    "password_hash": "newpassword123",
    "encrypt_key": "new-encryption-key",
    "traffic_limit": 2147483648,
    "is_admin": false
}
```

#### 删除用户
```
DELETE /api/users/:id
Authorization: <token>
```

### 监控接口

#### 获取流量数据
```
GET /api/traffic
Authorization: <token>
```

#### 获取系统状态
```
GET /api/status
Authorization: <token>
```

#### 获取系统配置
```
GET /api/config
Authorization: <token>
```

## 数据库

使用SQLite数据库存储用户信息，数据库文件: `coco.db`

### 用户表结构
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    encrypt_key TEXT NOT NULL,
    traffic_limit INTEGER DEFAULT 0,
    used_traffic INTEGER DEFAULT 0,
    is_admin BOOLEAN DEFAULT FALSE
);
```

## 安全注意事项

1. **修改默认密码**: 首次登录后请立即修改默认管理员密码
2. **HTTPS部署**: 生产环境建议使用HTTPS
3. **防火墙配置**: 适当配置防火墙规则
4. **定期备份**: 定期备份数据库文件

## 开发说明

### 项目结构
```
server_go/
├── main.go           # 主程序入口
├── admin_ui.go       # Web管理界面路由和API
├── user_manager.go   # 用户管理功能
├── admin_ui/         # 前端静态文件
│   └── index.html    # 管理界面HTML
├── go.mod           # Go模块依赖
├── go.sum           # 依赖校验文件
└── coco.db          # SQLite数据库文件
```

### 依赖包
- `github.com/gin-gonic/gin`: Web框架
- `github.com/mattn/go-sqlite3`: SQLite驱动
- `golang.org/x/crypto/bcrypt`: 密码加密

## 故障排除

### 常见问题

1. **端口被占用**
   - 检查8080和8888端口是否被其他程序占用
   - 使用`netstat -an | findstr "8080"`检查端口状态

2. **数据库权限问题**
   - 确保程序有读写当前目录的权限
   - 检查coco.db文件权限

3. **管理界面无法访问**
   - 确认服务器已正常启动
   - 检查防火墙设置
   - 确认admin_ui目录存在且包含index.html

### 日志查看

服务器启动时会输出日志信息，包括：
- 数据库初始化状态
- 默认管理员用户创建状态
- 服务器启动状态和端口信息

## 更新日志

### v1.1 (当前版本)
- ✅ 完善的Web管理界面
- ✅ 用户管理功能
- ✅ 流量监控功能
- ✅ 系统状态监控
- ✅ 安全的身份验证
- ✅ 响应式设计
- ✅ 完整的API接口

### v1.0 (原始版本)
- ✅ 基础代理服务
- ✅ 简单的API文档页面
- ✅ 用户数据库管理

## 联系方式

如有问题或建议，请通过项目仓库提交Issue。