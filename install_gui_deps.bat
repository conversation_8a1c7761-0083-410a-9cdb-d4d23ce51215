@echo off
echo Installing GUI dependencies for CocoProxy Client...
echo.

cd /d "%~dp0\client_py"

echo Installing Python packages...
pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ✓ Dependencies installed successfully!
    echo.
    echo You can now run the client with:
    echo   python main.py
    echo.
    echo Or test the tray functionality with:
    echo   python ..\test_tray_icon.py
) else (
    echo.
    echo ✗ Failed to install dependencies!
    echo Please check your Python installation and try again.
)

echo.
pause