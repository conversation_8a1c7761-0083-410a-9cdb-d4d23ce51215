@echo off
echo Building CocoProxy Client executable...

REM Check if PyInstaller is installed
python -m PyInstaller --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Installing PyInstaller...
    python -m pip install pyinstaller
)

REM Create build directory
if not exist "dist" mkdir dist
if not exist "build" mkdir build

REM Build executable
echo Building executable...
python -m PyInstaller ^
    --windowed ^
    --onefile ^
    --name "CocoProxy Client" ^
    --distpath dist ^
    --workpath build ^
    --specpath build ^
    main.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build completed successfully!
    echo Executable created: dist\CocoProxy Client.exe
    echo.
    echo To run the executable:
    echo   cd dist
    echo   "CocoProxy Client.exe"
    echo.
) else (
    echo.
    echo Build failed!
    echo Please check the error messages above.
)

pause