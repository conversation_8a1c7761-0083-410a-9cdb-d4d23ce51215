package com.cocoproxy.client.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import com.cocoproxy.client.data.repository.ConfigRepository
import com.cocoproxy.client.service.CocoProxyService
import com.cocoproxy.client.service.CocoProxyVpnService
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {
    
    @Inject
    lateinit var configRepository: ConfigRepository
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED ||
            intent.action == Intent.ACTION_MY_PACKAGE_REPLACED ||
            intent.action == Intent.ACTION_PACKAGE_REPLACED) {
            
            scope.launch {
                try {
                    val vpnConfig = configRepository.vpnConfig.first()
                    val serverConfig = configRepository.serverConfig.first()
                    val proxyConfig = configRepository.proxyConfig.first()
                    
                    // Check if auto-connect is enabled
                    if (vpnConfig.autoConnect) {
                        // Start VPN service if auto-connect is enabled
                        val vpnIntent = Intent(context, CocoProxyVpnService::class.java).apply {
                            action = CocoProxyVpnService.ACTION_START_VPN
                            putExtra("server_config", serverConfig)
                            putExtra("proxy_config", proxyConfig)
                            putExtra("vpn_config", vpnConfig)
                        }
                        
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            context.startForegroundService(vpnIntent)
                        } else {
                            context.startService(vpnIntent)
                        }
                    } else {
                        // Check if proxy should auto-start
                        val uiConfig = configRepository.uiConfig.first()
                        if (uiConfig.autoStartProxy) {
                            val proxyIntent = Intent(context, CocoProxyService::class.java).apply {
                                action = CocoProxyService.ACTION_START_PROXY
                                putExtra("server_config", serverConfig)
                                putExtra("proxy_config", proxyConfig)
                            }
                            
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                context.startForegroundService(proxyIntent)
                            } else {
                                context.startService(proxyIntent)
                            }
                        }
                    }
                } catch (e: Exception) {
                    // Log error but don't crash
                    android.util.Log.e("BootReceiver", "Failed to auto-start services", e)
                }
            }
        }
    }
}