package com.cocoproxy.client.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0012\b\u0007\u0018\u0000 .2\u00020\u0001:\u0001.B\u0013\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0016\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u001fJ\u0016\u0010 \u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0002\u0010!J\u0016\u0010\"\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010#J\u0016\u0010$\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u0014H\u0086@\u00a2\u0006\u0002\u0010%J\u0016\u0010&\u001a\u00020\u001d2\u0006\u0010\u001e\u001a\u00020\u0017H\u0086@\u00a2\u0006\u0002\u0010\'J\u0016\u0010(\u001a\u00020\u001d2\u0006\u0010)\u001a\u00020\u001aH\u0086@\u00a2\u0006\u0002\u0010*J\u000e\u0010+\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u0010-\u001a\u00020\u001dH\u0086@\u00a2\u0006\u0002\u0010,R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0017\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\fR\u0017\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\fR\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00170\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\fR\u0019\u0010\u0019\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u001a0\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\f\u00a8\u0006/"}, d2 = {"Lcom/cocoproxy/client/data/repository/ConfigRepository;", "", "context", "Landroid/content/Context;", "<init>", "(Landroid/content/Context;)V", "gson", "Lcom/google/gson/Gson;", "serverConfig", "Lkotlinx/coroutines/flow/Flow;", "Lcom/cocoproxy/client/data/model/ServerConfig;", "getServerConfig", "()Lkotlinx/coroutines/flow/Flow;", "proxyConfig", "Lcom/cocoproxy/client/data/model/ProxyConfig;", "getProxyConfig", "uiConfig", "Lcom/cocoproxy/client/data/model/UiConfig;", "getUiConfig", "vpnConfig", "Lcom/cocoproxy/client/data/model/VpnConfig;", "getVpnConfig", "appSplitConfig", "Lcom/cocoproxy/client/core/AppSplitConfig;", "getAppSplitConfig", "userProfile", "Lcom/cocoproxy/client/data/model/UserProfile;", "getUserProfile", "saveServerConfig", "", "config", "(Lcom/cocoproxy/client/data/model/ServerConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveProxyConfig", "(Lcom/cocoproxy/client/data/model/ProxyConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveUiConfig", "(Lcom/cocoproxy/client/data/model/UiConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveVpnConfig", "(Lcom/cocoproxy/client/data/model/VpnConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveAppSplitConfig", "(Lcom/cocoproxy/client/core/AppSplitConfig;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "saveUserProfile", "profile", "(Lcom/cocoproxy/client/data/model/UserProfile;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearUserProfile", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearAllConfig", "Companion", "app_debug"})
public final class ConfigRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "ConfigRepository";
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> SERVER_HOST = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Integer> SERVER_PORT = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Integer> SERVER_ADMIN_PORT = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> SERVER_USERNAME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> SERVER_PASSWORD = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> SERVER_TOKEN = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> SERVER_REMEMBER_ME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Integer> PROXY_LOCAL_PORT = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> PROXY_PROTOCOL = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> PROXY_ENCRYPTION = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> PROXY_AUTO_START = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> PROXY_USE_VPN = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> UI_THEME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> UI_LANGUAGE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> UI_MINIMIZE_TO_TRAY = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> UI_START_ON_BOOT = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> UI_CHECK_UPDATES = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> UI_SHOW_NOTIFICATIONS = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> VPN_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VPN_DNS_SERVERS = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VPN_ROUTES = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VPN_EXCLUDED_APPS = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Integer> VPN_MTU = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> VPN_SESSION_NAME = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> APP_SPLIT_MODE = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> APP_SPLIT_SELECTED_APPS = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.Boolean> APP_SPLIT_ENABLED = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.datastore.preferences.core.Preferences.Key<java.lang.String> USER_PROFILE = null;
    @org.jetbrains.annotations.NotNull()
    private final com.google.gson.Gson gson = null;
    
    /**
     * Get server configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.ServerConfig> serverConfig = null;
    
    /**
     * Get proxy configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.ProxyConfig> proxyConfig = null;
    
    /**
     * Get UI configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.UiConfig> uiConfig = null;
    
    /**
     * Get VPN configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.VpnConfig> vpnConfig = null;
    
    /**
     * Get app split configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.core.AppSplitConfig> appSplitConfig = null;
    
    /**
     * Get user profile flow
     */
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.UserProfile> userProfile = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.cocoproxy.client.data.repository.ConfigRepository.Companion Companion = null;
    
    @javax.inject.Inject()
    public ConfigRepository(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * Get server configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.ServerConfig> getServerConfig() {
        return null;
    }
    
    /**
     * Get proxy configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.ProxyConfig> getProxyConfig() {
        return null;
    }
    
    /**
     * Get UI configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.UiConfig> getUiConfig() {
        return null;
    }
    
    /**
     * Get VPN configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.VpnConfig> getVpnConfig() {
        return null;
    }
    
    /**
     * Get app split configuration flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.core.AppSplitConfig> getAppSplitConfig() {
        return null;
    }
    
    /**
     * Get user profile flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.cocoproxy.client.data.model.UserProfile> getUserProfile() {
        return null;
    }
    
    /**
     * Save server configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveServerConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ServerConfig config, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Save proxy configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveProxyConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.ProxyConfig config, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Save UI configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveUiConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UiConfig config, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Save VPN configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveVpnConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.VpnConfig config, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Save app split configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveAppSplitConfig(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.core.AppSplitConfig config, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Save user profile
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object saveUserProfile(@org.jetbrains.annotations.NotNull()
    com.cocoproxy.client.data.model.UserProfile profile, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear user profile
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearUserProfile(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Clear all configuration
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object clearAllConfig(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0016\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010#\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010$\u001a\b\u0012\u0004\u0012\u00020\u00050\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006%"}, d2 = {"Lcom/cocoproxy/client/data/repository/ConfigRepository$Companion;", "", "<init>", "()V", "TAG", "", "SERVER_HOST", "Landroidx/datastore/preferences/core/Preferences$Key;", "SERVER_PORT", "", "SERVER_ADMIN_PORT", "SERVER_USERNAME", "SERVER_PASSWORD", "SERVER_TOKEN", "SERVER_REMEMBER_ME", "", "PROXY_LOCAL_PORT", "PROXY_PROTOCOL", "PROXY_ENCRYPTION", "PROXY_AUTO_START", "PROXY_USE_VPN", "UI_THEME", "UI_LANGUAGE", "UI_MINIMIZE_TO_TRAY", "UI_START_ON_BOOT", "UI_CHECK_UPDATES", "UI_SHOW_NOTIFICATIONS", "VPN_ENABLED", "VPN_DNS_SERVERS", "VPN_ROUTES", "VPN_EXCLUDED_APPS", "VPN_MTU", "VPN_SESSION_NAME", "APP_SPLIT_MODE", "APP_SPLIT_SELECTED_APPS", "APP_SPLIT_ENABLED", "USER_PROFILE", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}