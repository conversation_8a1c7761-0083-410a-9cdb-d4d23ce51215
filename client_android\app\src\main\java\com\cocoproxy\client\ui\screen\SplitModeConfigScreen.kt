package com.cocoproxy.client.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.cocoproxy.client.core.SplitMode
import com.cocoproxy.client.ui.viewmodel.AppSplitViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SplitModeConfigScreen(
    onNavigateBack: () -> Unit,
    viewModel: AppSplitViewModel = hiltViewModel()
) {
    val splitConfig by viewModel.splitConfig.collectAsStateWithLifecycle()
    var selectedMode by remember { mutableStateOf(splitConfig.mode) }
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("分流模式配置") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Filled.ArrowBack, contentDescription = "返回")
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            viewModel.setSplitMode(selectedMode)
                            onNavigateBack()
                        }
                    ) {
                        Text("保存")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 说明卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            Icons.Filled.Info,
                            contentDescription = null,
                            tint = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            "分流模式说明",
                            style = MaterialTheme.typography.titleMedium,
                            fontWeight = FontWeight.Bold
                        )
                    }
                    Text(
                        "选择应用分流的工作模式，不同模式下应用的网络流量走向不同。",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // 模式选择
            Text(
                "选择分流模式",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Column(
                modifier = Modifier.selectableGroup(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                SplitModeOption(
                    mode = SplitMode.DISABLED,
                    title = "禁用分流",
                    description = "所有应用的网络流量都通过代理服务器",
                    icon = Icons.Filled.Block,
                    isSelected = selectedMode == SplitMode.DISABLED,
                    onSelect = { selectedMode = SplitMode.DISABLED }
                )
                
                SplitModeOption(
                    mode = SplitMode.WHITELIST,
                    title = "白名单模式",
                    description = "只有选中的应用通过代理，其他应用直连网络",
                    icon = Icons.Filled.CheckCircle,
                    isSelected = selectedMode == SplitMode.WHITELIST,
                    onSelect = { selectedMode = SplitMode.WHITELIST }
                )
                
                SplitModeOption(
                    mode = SplitMode.BLACKLIST,
                    title = "黑名单模式", 
                    description = "除了选中的应用外，其他应用都通过代理",
                    icon = Icons.Filled.Cancel,
                    isSelected = selectedMode == SplitMode.BLACKLIST,
                    onSelect = { selectedMode = SplitMode.BLACKLIST }
                )
            }
            
            // 当前选择的详细说明
            if (selectedMode != splitConfig.mode) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.primaryContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            "模式变更提示",
                            style = MaterialTheme.typography.titleSmall,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.primary
                        )
                        Text(
                            getModeChangeDescription(splitConfig.mode, selectedMode),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // 底部按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onNavigateBack,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("取消")
                }
                
                Button(
                    onClick = {
                        viewModel.setSplitMode(selectedMode)
                        onNavigateBack()
                    },
                    modifier = Modifier.weight(1f),
                    enabled = selectedMode != splitConfig.mode
                ) {
                    Text("保存设置")
                }
            }
        }
    }
}

@Composable
private fun SplitModeOption(
    mode: SplitMode,
    title: String,
    description: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .selectable(
                selected = isSelected,
                onClick = onSelect,
                role = Role.RadioButton
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) {
                MaterialTheme.colorScheme.primaryContainer
            } else {
                MaterialTheme.colorScheme.surface
            }
        ),
        border = if (isSelected) {
            CardDefaults.outlinedCardBorder().copy(
                width = 2.dp,
                brush = androidx.compose.ui.graphics.SolidColor(MaterialTheme.colorScheme.primary)
            )
        } else {
            null
        }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Icon(
                icon,
                contentDescription = null,
                tint = if (isSelected) {
                    MaterialTheme.colorScheme.primary
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
                modifier = Modifier.size(32.dp)
            )
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = if (isSelected) {
                        MaterialTheme.colorScheme.primary
                    } else {
                        MaterialTheme.colorScheme.onSurface
                    }
                )
                Text(
                    description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            RadioButton(
                selected = isSelected,
                onClick = null // handled by card click
            )
        }
    }
}

private fun getModeChangeDescription(currentMode: SplitMode, newMode: SplitMode): String {
    return when {
        currentMode == SplitMode.DISABLED && newMode != SplitMode.DISABLED -> {
            "启用应用分流后，您需要选择要分流的应用。"
        }
        currentMode != SplitMode.DISABLED && newMode == SplitMode.DISABLED -> {
            "禁用分流后，所有应用都将通过代理连接。"
        }
        currentMode == SplitMode.WHITELIST && newMode == SplitMode.BLACKLIST -> {
            "切换到黑名单模式后，之前选中的应用将不通过代理。"
        }
        currentMode == SplitMode.BLACKLIST && newMode == SplitMode.WHITELIST -> {
            "切换到白名单模式后，只有选中的应用会通过代理。"
        }
        else -> "模式变更将在保存后生效。"
    }
}