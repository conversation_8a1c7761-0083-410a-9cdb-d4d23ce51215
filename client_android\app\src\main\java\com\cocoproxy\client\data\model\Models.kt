package com.cocoproxy.client.data.model

import android.os.Parcelable
import androidx.compose.runtime.Stable
import kotlinx.parcelize.Parcelize
import com.cocoproxy.client.core.AppSplitConfig

/**
 * Server configuration data class
 */
@Parcelize
@Stable
data class ServerConfig(
    val host: String = "127.0.0.1",
    val port: Int = 28888,
    val adminPort: Int = 28080,
    val username: String = "",
    val password: String = "",
    val token: String = "",
    val rememberMe: Boolean = false,
    val useSSL: Boolean = false,
    val verifySSL: Boolean = true,
    val timeout: Int = 30,
    val retryCount: Int = 3
) : Parcelable

/**
 * Proxy configuration data class
 */
@Parcelize
@Stable
data class ProxyConfig(
    val localPort: Int = 1080,
    val protocol: String = "socks5", // socks5, http
    val encryption: String = "aes", // none, aes, chacha20
    val autoStart: Boolean = false,
    val useVpn: Boolean = false, // Use VPN service for system-wide proxy
    val enableAuth: Boolean = false,
    val authUsername: String = "",
    val authPassword: String = "",
    val forwardConnection: Boolean = true,
    val proxyType: String = "socks5",
    val enableDNS: Boolean = true,
    val dnsServer: String = "*******",
    val enableUDP: Boolean = true,
    val bufferSize: Int = 4096,
    val maxConnections: Int = 100
) : Parcelable

/**
 * UI configuration data class
 */
@Stable
data class UiConfig(
    val theme: String = "system", // system, light, dark
    val language: String = "en",
    val minimizeToTray: Boolean = true,
    val startOnBoot: Boolean = false,
    val checkUpdates: Boolean = true,
    val showNotifications: Boolean = true,
    val autoStartProxy: Boolean = false,
    val darkTheme: Boolean = false,
    val dynamicColors: Boolean = false,
    val trafficAlerts: Boolean = false,
    val debugMode: Boolean = false
)

/**
 * Complete application configuration
 */
@Stable
data class AppConfig(
    val server: ServerConfig = ServerConfig(),
    val proxy: ProxyConfig = ProxyConfig(),
    val ui: UiConfig = UiConfig()
)

/**
 * Traffic data for monitoring
 */
@Stable
data class TrafficData(
    val bytesUploaded: Long = 0L,
    val bytesDownloaded: Long = 0L,
    val activeConnections: Int = 0,
    val totalConnections: Long = 0L,
    val upSpeed: Long = 0L, // bytes per second
    val downSpeed: Long = 0L, // bytes per second
    val timestamp: Long = System.currentTimeMillis()
) {
    val totalBytes: Long get() = bytesUploaded + bytesDownloaded
    
    fun formatBytes(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes} B"
            bytes < 1024 * 1024 -> "${String.format("%.1f", bytes / 1024.0)} KB"
            bytes < 1024 * 1024 * 1024 -> "${String.format("%.1f", bytes / (1024.0 * 1024.0))} MB"
            else -> "${String.format("%.1f", bytes / (1024.0 * 1024.0 * 1024.0))} GB"
        }
    }
    
    fun formatSpeed(bytesPerSecond: Long): String {
        return "${formatBytes(bytesPerSecond)}/s"
    }
}

/**
 * Connection status enumeration
 */
enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    RECONNECTING,
    ERROR;
    
    val isConnected: Boolean
        get() = this == CONNECTED
}

/**
 * Proxy status data class
 */
@Stable
data class ProxyStatus(
    val isRunning: Boolean = false,
    val connectionStatus: ConnectionStatus = ConnectionStatus.DISCONNECTED,
    val localPort: Int = 0,
    val serverHost: String = "",
    val serverPort: Int = 0,
    val protocol: String = "",
    val encryption: String = "",
    val errorMessage: String? = null,
    val lastConnected: Long? = null,
    val uptime: Long = 0L // milliseconds
)

/**
 * Log entry data class
 */
@Stable
data class LogEntry(
    val timestamp: Long = System.currentTimeMillis(),
    val level: LogLevel = LogLevel.INFO,
    val tag: String = "",
    val message: String = "",
    val throwable: Throwable? = null,
    val thread: String = "",
    val className: String = ""
)

/**
 * Log level enumeration
 */
enum class LogLevel(val displayName: String, val priority: Int) {
    VERBOSE("VERBOSE", 2),
    DEBUG("DEBUG", 3),
    INFO("INFO", 4),
    WARN("WARN", 5),
    ERROR("ERROR", 6)
}

/**
 * Server info data class
 */
@Stable
data class ServerInfo(
    val version: String = "",
    val uptime: Long = 0L,
    val activeClients: Int = 0,
    val totalConnections: Long = 0L,
    val supportedProtocols: List<String> = emptyList(),
    val supportedEncryption: List<String> = emptyList(),
    val maxClients: Int = 0,
    val serverTime: Long = 0L
)

/**
 * User profile data class
 */
@Stable
data class UserProfile(
    val username: String = "",
    val email: String = "",
    val plan: String = "free", // free, premium, enterprise
    val expiryDate: Long? = null,
    val dataLimit: Long? = null, // bytes
    val dataUsed: Long = 0L, // bytes
    val isActive: Boolean = true,
    val permissions: List<String> = emptyList()
) {
    val dataRemaining: Long?
        get() = dataLimit?.let { limit -> (limit - dataUsed).coerceAtLeast(0L) }
    
    val dataUsagePercentage: Float
        get() = dataLimit?.let { limit ->
            if (limit > 0) (dataUsed.toFloat() / limit.toFloat()).coerceIn(0f, 1f)
            else 0f
        } ?: 0f
}

/**
 * Network interface information
 */
@Stable
data class NetworkInterface(
    val name: String,
    val displayName: String,
    val isUp: Boolean,
    val isLoopback: Boolean,
    val addresses: List<String>
)

/**
 * VPN configuration for system-wide proxy
 */
@Parcelize
@Stable
data class VpnConfig(
    val enabled: Boolean = false,
    val dnsServers: List<String> = listOf("*******", "*******"),
    val routes: List<String> = emptyList(),
    val excludedApps: List<String> = emptyList(),
    val mtu: Int = 1500,
    val sessionName: String = "CocoProxy VPN",
    val autoConnect: Boolean = false,
    val killSwitch: Boolean = false,
    val ipv6Enabled: Boolean = false,
    val enableUdp: Boolean = true,
    val username: String = "", // Added username field
    val appSplitConfig: AppSplitConfig = AppSplitConfig()
) : Parcelable



/**
 * Application category for auto-selection
 */
enum class AppCategory {
    SOCIAL,
    ENTERTAINMENT,
    PRODUCTIVITY,
    GAMES,
    SHOPPING,
    TRAVEL,
    NEWS,
    FINANCE,
    EDUCATION,
    HEALTH,
    SYSTEM,
    OTHER
}

/**
 * Installed application information
 */
@Stable
data class InstalledApp(
    val packageName: String,
    val appName: String,
    val versionName: String = "",
    val versionCode: Long = 0,
    val isSystemApp: Boolean = false,
    val uid: Int = -1,
    val category: AppCategory = AppCategory.OTHER,
    val installTime: Long = 0,
    val lastUpdateTime: Long = 0,
    val dataUsage: Long = 0, // Bytes used through proxy
    val isSelected: Boolean = false
)

/**
 * Application traffic statistics
 */
@Parcelize
@Stable
data class AppTrafficStats(
    val packageName: String,
    val appName: String,
    val uploadBytes: Long = 0,
    val downloadBytes: Long = 0,
    val isProxied: Boolean = false,
    val connectionsCount: Long = 0,
    val lastActiveTime: Long = 0,
    val isActive: Boolean = false
) : Parcelable {
    val totalBytes: Long get() = uploadBytes + downloadBytes
}