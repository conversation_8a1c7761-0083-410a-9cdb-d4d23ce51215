#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import logging
import os
import time
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit, QGroupBox,
    QSystemTrayIcon, QMenu, QMenuBar, QStatusBar, QMessageBox,
    QFrame, QSizePolicy, QApplication
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, Q_ARG
from PySide6.QtGui import QIcon, QPixmap, QAction
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtGui import QPainter

from .login_dialog import LoginDialog
from .settings_dialog import SettingsDialog
from .about_dialog import AboutDialog

class MainWindow(QMainWindow):
    """Main window for CocoProxy client"""
    
    def __init__(self, config, proxy_client):
        """Initialize main window"""
        super().__init__()
        self.config = config
        self.proxy_client = proxy_client
        self.logger = logging.getLogger("CocoProxy.MainWindow")
        
        # Window properties
        self.setWindowTitle("CocoProxy Client")
        self.setMinimumSize(600, 400)
        self.resize(800, 600)
        
        # Set application icon
        self.set_application_icon()
        
        # Setup UI
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_system_tray()
        
        # Connect signals
        self.connect_signals()
        
        # Initialize proxy status
        self.update_proxy_status(False, "Proxy not started")
        
        # Initialize state
        self.update_connection_status(False, "Not connected")
        
        # Auto-login if credentials are saved
        self.auto_login()
    
    def setup_ui(self):
        """Setup user interface"""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Connection status group
        self.setup_connection_group(main_layout)
        
        # Server info group
        self.setup_server_info_group(main_layout)
        
        # Traffic info group
        self.setup_traffic_group(main_layout)
        
        # Control buttons
        self.setup_control_buttons(main_layout)
        
        # Log output
        self.setup_log_output(main_layout)
    
    def setup_connection_group(self, parent_layout):
        """Setup connection status group"""
        group = QGroupBox("Connection Status")
        layout = QGridLayout(group)
        
        # Status label
        self.status_label = QLabel("Not connected")
        self.status_label.setStyleSheet("font-weight: bold; color: red;")
        layout.addWidget(QLabel("Status:"), 0, 0)
        layout.addWidget(self.status_label, 0, 1)
        
        # Server address
        server_host = self.config.get("server", "host")
        server_port = self.config.get("server", "port")
        self.server_label = QLabel(f"{server_host}:{server_port}")
        layout.addWidget(QLabel("Server:"), 1, 0)
        layout.addWidget(self.server_label, 1, 1)
        
        # Local proxy port
        local_port = self.config.get("proxy", "local_port")
        self.local_port_label = QLabel(str(local_port))
        layout.addWidget(QLabel("Local Port:"), 2, 0)
        layout.addWidget(self.local_port_label, 2, 1)
        
        # Protocol
        protocol = self.config.get("proxy", "protocol")
        self.protocol_label = QLabel(protocol.upper())
        layout.addWidget(QLabel("Protocol:"), 3, 0)
        layout.addWidget(self.protocol_label, 3, 1)
        
        # Proxy status
        self.proxy_status_label = QLabel("Not started")
        self.proxy_status_label.setStyleSheet("font-weight: bold; color: red;")
        layout.addWidget(QLabel("Proxy Status:"), 4, 0)
        layout.addWidget(self.proxy_status_label, 4, 1)
        
        parent_layout.addWidget(group)
    
    def setup_server_info_group(self, parent_layout):
        """Setup server information group"""
        group = QGroupBox("Server Information")
        layout = QGridLayout(group)
        
        # Server uptime
        self.uptime_label = QLabel("Unknown")
        layout.addWidget(QLabel("Uptime:"), 0, 0)
        layout.addWidget(self.uptime_label, 0, 1)
        
        # Total users
        self.users_label = QLabel("Unknown")
        layout.addWidget(QLabel("Total Users:"), 1, 0)
        layout.addWidget(self.users_label, 1, 1)
        
        # Server version
        self.version_label = QLabel("Unknown")
        layout.addWidget(QLabel("Version:"), 2, 0)
        layout.addWidget(self.version_label, 2, 1)
        
        parent_layout.addWidget(group)
    
    def setup_traffic_group(self, parent_layout):
        """Setup traffic information group"""
        group = QGroupBox("Traffic Information")
        layout = QVBoxLayout(group)
        
        # Traffic progress bar
        self.traffic_progress = QProgressBar()
        self.traffic_progress.setTextVisible(True)
        layout.addWidget(self.traffic_progress)
        
        # Traffic details
        details_layout = QGridLayout()
        
        self.used_traffic_label = QLabel("0 MB")
        details_layout.addWidget(QLabel("Used:"), 0, 0)
        details_layout.addWidget(self.used_traffic_label, 0, 1)
        
        self.total_traffic_label = QLabel("Unlimited")
        details_layout.addWidget(QLabel("Limit:"), 1, 0)
        details_layout.addWidget(self.total_traffic_label, 1, 1)
        
        self.remaining_traffic_label = QLabel("Unlimited")
        details_layout.addWidget(QLabel("Remaining:"), 2, 0)
        details_layout.addWidget(self.remaining_traffic_label, 2, 1)
        
        layout.addLayout(details_layout)
        parent_layout.addWidget(group)
    
    def setup_control_buttons(self, parent_layout):
        """Setup control buttons"""
        button_layout = QHBoxLayout()
        
        # Connect/Disconnect button
        self.connect_button = QPushButton("Connect")
        self.connect_button.clicked.connect(self.toggle_connection)
        button_layout.addWidget(self.connect_button)
        
        # Test connection button
        self.test_button = QPushButton("Test Connection")
        self.test_button.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_button)
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.clicked.connect(self.refresh_info)
        button_layout.addWidget(self.refresh_button)
        
        button_layout.addStretch()
        parent_layout.addLayout(button_layout)
    
    def setup_log_output(self, parent_layout):
        """Setup log output area"""
        group = QGroupBox("Log Output")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        parent_layout.addWidget(group)
    
    def setup_menu(self):
        """Setup menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("&File")
        
        login_action = QAction("&Login", self)
        login_action.triggered.connect(self.show_login_dialog)
        file_menu.addAction(login_action)
        
        logout_action = QAction("Log&out", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("E&xit", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Settings menu
        settings_menu = menubar.addMenu("&Settings")
        
        preferences_action = QAction("&Preferences", self)
        preferences_action.triggered.connect(self.show_settings_dialog)
        settings_menu.addAction(preferences_action)
        
        # Help menu
        help_menu = menubar.addMenu("&Help")
        
        about_action = QAction("&About", self)
        about_action.triggered.connect(self.show_about_dialog)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("Ready")
    
    def set_application_icon(self):
        """Set application icon"""
        try:
            # Try ICO format first (better for Windows taskbar)
            ico_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icon.ico')
            ico_path = os.path.abspath(ico_path)
            
            if os.path.exists(ico_path):
                # Load ICO icon
                icon = QIcon(ico_path)
                self.setWindowIcon(icon)
                
                # Set application icon globally
                QApplication.instance().setWindowIcon(icon)
                
                # Store icon for tray use
                self.app_icon = icon
                self.logger.info(f"Loaded ICO icon: {ico_path}")
            else:
                # Fallback to SVG
                svg_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icon.svg')
                svg_path = os.path.abspath(svg_path)
                
                if os.path.exists(svg_path):
                    # Load SVG icon and convert to QIcon
                    icon = self.load_svg_icon(svg_path, 64, 64)
                    self.setWindowIcon(icon)
                    
                    # Set application icon globally
                    QApplication.instance().setWindowIcon(icon)
                    
                    # Store icon for tray use
                    self.app_icon = icon
                    self.logger.info(f"Loaded SVG icon: {svg_path}")
                else:
                    self.logger.warning(f"No icon files found")
                    self.app_icon = self.style().standardIcon(self.style().SP_ComputerIcon)
        except Exception as e:
            self.logger.error(f"Failed to load application icon: {e}")
            self.app_icon = self.style().standardIcon(self.style().SP_ComputerIcon)
    
    def load_svg_icon(self, svg_path, width, height):
        """Load SVG file and convert to QIcon"""
        renderer = QSvgRenderer(svg_path)
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        return QIcon(pixmap)
    
    def setup_system_tray(self):
        """Setup system tray icon"""
        if not QSystemTrayIcon.isSystemTrayAvailable():
            self.logger.warning("System tray is not available")
            return
        
        # Create tray icon
        self.tray_icon = QSystemTrayIcon(self)
        
        # Set tray icon
        if hasattr(self, 'app_icon') and self.app_icon and not self.app_icon.isNull():
            self.tray_icon.setIcon(self.app_icon)
        else:
            # Try to load icon directly for tray
            ico_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'icon.ico')
            ico_path = os.path.abspath(ico_path)
            if os.path.exists(ico_path):
                tray_icon = QIcon(ico_path)
                self.tray_icon.setIcon(tray_icon)
            else:
                self.tray_icon.setIcon(self.style().standardIcon(self.style().SP_ComputerIcon))
        
        # Set tooltip
        self.tray_icon.setToolTip("CocoProxy Client")
        
        # Create tray menu
        tray_menu = QMenu()
        
        # Show/Hide action
        show_action = QAction("显示主窗口", self)
        show_action.triggered.connect(self.show_main_window)
        tray_menu.addAction(show_action)
        
        tray_menu.addSeparator()
        
        # About action
        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about_dialog)
        tray_menu.addAction(about_action)
        
        tray_menu.addSeparator()
        
        # Quit action
        quit_action = QAction("退出", self)
        quit_action.triggered.connect(self.quit_application)
        tray_menu.addAction(quit_action)
        
        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.activated.connect(self.tray_icon_activated)
        
        # Show tray icon
        self.tray_icon.show()
        
        # Show tray message on first run
        first_run = self.config.get("ui", "first_run")
        if first_run is None or first_run == True or first_run == "true":
            self.tray_icon.showMessage(
                "CocoProxy Client",
                "应用程序已最小化到系统托盘",
                QSystemTrayIcon.Information,
                3000
            )
            self.config.set("ui", "first_run", "false")
    
    def connect_signals(self):
        """Connect signals"""
        self.proxy_client.connection_status_changed.connect(self.update_connection_status)
        self.proxy_client.traffic_updated.connect(self.update_traffic_info)
        self.proxy_client.proxy_status_changed.connect(self.update_proxy_status)
        self.proxy_client.error_occurred.connect(self.show_error_message)
    
    def auto_login(self):
        """Auto-login if credentials are saved"""
        if self.config.get("user", "remember_me"):
            username = self.config.get("user", "username")
            password = self.config.get("user", "password")
            
            if username and password:
                self.log_message(f"Auto-login for user: {username}")
                self.proxy_client.login(username, password, True)
    
    def toggle_connection(self):
        """Toggle connection"""
        if self.proxy_client.connected:
            self.logout()
        else:
            self.show_login_dialog()
    
    def show_login_dialog(self):
        """Show login dialog"""
        dialog = LoginDialog(self.config, self)
        if dialog.exec() == LoginDialog.Accepted:
            username, password, remember = dialog.get_credentials()
            self.log_message(f"Attempting login for user: {username}")
            self.proxy_client.login(username, password, remember)
    
    def logout(self):
        """Logout from server"""
        self.proxy_client.logout()
        self.log_message("Logged out")
    
    def test_connection(self):
        """Test connection to server"""
        self.log_message("Testing connection...")
        success, message = self.proxy_client.test_connection()
        self.log_message(f"Connection test: {message}")
        
        if success:
            QMessageBox.information(self, "Connection Test", "Connection successful!")
        else:
            QMessageBox.warning(self, "Connection Test", f"Connection failed: {message}")
    
    def refresh_info(self):
        """Refresh server information"""
        self.log_message("Refreshing server information...")
        success, data = self.proxy_client.get_server_status()
        
        if success:
            self.update_server_info(data)
            self.log_message("Server information updated")
        else:
            self.log_message(f"Failed to refresh server info: {data}")
    
    def show_settings_dialog(self):
        """Show settings dialog"""
        dialog = SettingsDialog(self.config, self)
        # Connect the settings applied signal to handle configuration changes
        dialog.settings_applied.connect(self.on_settings_applied)
        if dialog.exec() == SettingsDialog.Accepted:
            self.update_ui_from_config()
    
    def on_settings_applied(self):
        """Handle settings applied - restart proxy if connected"""
        try:
            # Check if proxy is currently connected
            was_connected = self.proxy_client.connected
            
            if was_connected:
                self.log_message("Configuration changed, restarting proxy...")
                # Use QTimer to defer the restart to avoid blocking UI
                from PySide6.QtCore import QTimer
                QTimer.singleShot(100, self._restart_proxy_async)
            else:
                # Just update UI if not connected
                self.update_ui_from_config()
                self.log_message("Configuration updated successfully")
                
        except Exception as e:
            self.logger.error(f"Error applying settings: {e}")
            self.log_message(f"Error applying settings: {e}")
    
    def _restart_proxy_async(self):
        """Restart proxy in background thread to avoid blocking UI"""
        import threading
        
        def restart_task():
            try:
                # Disconnect first
                self.proxy_client.logout()
                # Update UI to reflect disconnected state (thread-safe)
                from PySide6.QtCore import QMetaObject, Qt
                QMetaObject.invokeMethod(self, "_update_ui_after_disconnect", Qt.QueuedConnection)
                
                # Small delay to ensure UI updates
                time.sleep(0.5)
                
                # Reconnect with new settings (thread-safe)
                QMetaObject.invokeMethod(self, "_reconnect_after_settings", Qt.QueuedConnection)
                
            except Exception as e:
                self.logger.error(f"Error restarting proxy: {e}")
                QMetaObject.invokeMethod(self, "log_message", Qt.QueuedConnection, 
                                       Q_ARG(str, f"Error restarting proxy: {e}"))
        
        # Start restart in background thread
        restart_thread = threading.Thread(target=restart_task, daemon=True)
        restart_thread.start()
    
    def _update_ui_after_disconnect(self):
        """Update UI after disconnect (called from main thread)"""
        self.update_connection_status(False, "Disconnected")
        self.update_ui_from_config()
    
    def _reconnect_after_settings(self):
        """Reconnect after settings change (called from main thread)"""
        self.toggle_connection()
    
    def show_about_dialog(self):
        """Show about dialog"""
        dialog = AboutDialog(self)
        dialog.exec()
    
    def update_connection_status(self, connected, message):
        """Update connection status"""
        self.proxy_client.connected = connected
        
        if connected:
            self.status_label.setText("Connected")
            self.status_label.setStyleSheet("font-weight: bold; color: green;")
            self.connect_button.setText("Disconnect")
        else:
            self.status_label.setText("Disconnected")
            self.status_label.setStyleSheet("font-weight: bold; color: red;")
            self.connect_button.setText("Connect")
        
        self.status_bar.showMessage(message)
        self.log_message(message)
    
    def update_proxy_status(self, running, message):
        """Update proxy status"""
        if running:
            self.proxy_status_label.setText("Running")
            self.proxy_status_label.setStyleSheet("font-weight: bold; color: green;")
        else:
            self.proxy_status_label.setText("Stopped")
            self.proxy_status_label.setStyleSheet("font-weight: bold; color: red;")
        
        self.log_message(f"Proxy: {message}")
    
    def update_traffic_info(self, used, limit):
        """Update traffic information"""
        try:
            # Ensure we have valid numbers
            used = float(used) if used is not None else 0.0
            limit = float(limit) if limit is not None else 0.0
            
            # Convert bytes to MB
            used_mb = used / (1024 * 1024)
            
            if limit > 0:
                limit_mb = limit / (1024 * 1024)
                remaining_mb = max(0, limit_mb - used_mb)  # Ensure non-negative
                percentage = min(100, int((used / limit) * 100))  # Cap at 100%
                
                self.traffic_progress.setMaximum(100)
                self.traffic_progress.setValue(percentage)
                self.traffic_progress.setFormat(f"{percentage}% ({used_mb:.1f} MB / {limit_mb:.1f} MB)")
                
                self.total_traffic_label.setText(f"{limit_mb:.1f} MB")
                self.remaining_traffic_label.setText(f"{remaining_mb:.1f} MB")
            else:
                self.traffic_progress.setMaximum(0)
                self.traffic_progress.setFormat(f"{used_mb:.1f} MB")
                
                self.total_traffic_label.setText("Unlimited")
                self.remaining_traffic_label.setText("Unlimited")
            
            self.used_traffic_label.setText(f"{used_mb:.1f} MB")
            
        except Exception as e:
            self.logger.error(f"Error updating traffic info: {e}")
            # Set safe default values
            self.used_traffic_label.setText("Error")
            self.total_traffic_label.setText("Error")
            self.remaining_traffic_label.setText("Error")
            self.traffic_progress.setMaximum(0)
    
    def update_server_info(self, data):
        """Update server information"""
        if isinstance(data, dict):
            self.uptime_label.setText(data.get("uptime", "Unknown"))
            self.users_label.setText(str(data.get("total_users", "Unknown")))
            self.version_label.setText(data.get("version", "Unknown"))
    
    def update_ui_from_config(self):
        """Update UI elements from configuration"""
        # Update server info
        server_host = self.config.get("server", "host")
        server_port = self.config.get("server", "port")
        self.server_label.setText(f"{server_host}:{server_port}")
        
        # Update local port
        local_port = self.config.get("proxy", "local_port")
        self.local_port_label.setText(str(local_port))
        
        # Update protocol
        protocol = self.config.get("proxy", "protocol")
        self.protocol_label.setText(protocol.upper())
    
    def show_error_message(self, message):
        """Show error message"""
        self.log_message(f"Error: {message}")
        QMessageBox.critical(self, "Error", message)
    
    def log_message(self, message):
        """Add message to log output"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def show_main_window(self):
        """Show and activate main window"""
        self.show()
        self.raise_()
        self.activateWindow()
        self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)
    
    def show_about_dialog(self):
        """Show about dialog"""
        try:
            about_dialog = AboutDialog(self)
            about_dialog.exec()
        except Exception as e:
            self.logger.error(f"Failed to show about dialog: {e}")
            # Fallback to simple message box
            QMessageBox.about(self, "关于 CocoProxy", 
                            "CocoProxy Client\n\n"
                            "一个安全的代理客户端应用程序\n\n"
                            "版本: 1.0.0")
    
    def quit_application(self):
        """Quit application completely"""
        # Cleanup
        try:
            self.proxy_client.logout()
        except:
            pass
        
        # Hide tray icon
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        
        # Quit application
        QApplication.instance().quit()
    
    def tray_icon_activated(self, reason):
        """Handle tray icon activation"""
        if reason == QSystemTrayIcon.DoubleClick:
            if self.isVisible() and not self.isMinimized():
                self.hide()
            else:
                self.show_main_window()
    
    def closeEvent(self, event):
        """Handle close event"""
        # Always minimize to tray if tray is available
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            self.hide()
            
            # Show tray message on first close
            if not hasattr(self, '_first_close_shown'):
                self.tray_icon.showMessage(
                    "CocoProxy Client",
                    "应用程序已最小化到系统托盘，双击托盘图标可重新打开",
                    QSystemTrayIcon.Information,
                    3000
                )
                self._first_close_shown = True
            
            event.ignore()
        else:
            # Fallback: actually quit if no tray available
            self.quit_application()
            event.accept()